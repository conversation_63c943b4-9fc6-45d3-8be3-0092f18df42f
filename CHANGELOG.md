### Changelog

All notable changes to this project will be documented in this file. Dates are displayed in UTC.

Generated by [`auto-changelog`](https://github.com/CookPete/auto-changelog).

#### [v0.1.68](https://e.coding.net/seer-group/meta-v/compare/v0.1.67...v0.1.68)

- [feat] 支持镜像插槽 [`343c922`](https://e.coding.net/seer-group/meta-v/commit/343c9226bdd85167bdf598c2f539b86746ad212d)

#### [v0.1.67](https://e.coding.net/seer-group/meta-v/compare/v0.1.66...v0.1.67)

> 23 December 2024

- [feat] 修改目录 [`b1390ea`](https://e.coding.net/seer-group/meta-v/commit/b1390ea7dfbb5d8c72a8872ffc43f82ce4d07a3c)
- [feat] 添加设备类 [`85d7794`](https://e.coding.net/seer-group/meta-v/commit/85d77945e6c958b964721d2cf308dd7f04e29cd8)
- [feat] 插槽支持 follow mast [`8359fec`](https://e.coding.net/seer-group/meta-v/commit/8359fecc2280715f4d50333baecb90ad7224918e)

#### [v0.1.66](https://e.coding.net/seer-group/meta-v/compare/v0.1.65...v0.1.66)

> 5 September 2024

- [feat] 调整默认环境光亮度；渲染模式适配自适应三视图模式 [`f9aa070`](https://e.coding.net/seer-group/meta-v/commit/f9aa0700a07914d776cbfbf6b86ec00374691953)

#### [v0.1.65](https://e.coding.net/seer-group/meta-v/compare/v0.1.64...v0.1.65)

> 29 August 2024

- [feat] 适配 BVH worker url [`ec76829`](https://e.coding.net/seer-group/meta-v/commit/ec768292be0d39ace3cc7cb9eb845df53d3d5c8b)
- [feat] 正交相机适配 render pass [`bc9f0e0`](https://e.coding.net/seer-group/meta-v/commit/bc9f0e0b389897efbd7d7206e02f9eb080927d05)

#### [v0.1.64](https://e.coding.net/seer-group/meta-v/compare/v0.1.63...v0.1.64)

> 27 August 2024

- [fix] 正交相机尺寸拉伸问题 [`b8330d4`](https://e.coding.net/seer-group/meta-v/commit/b8330d463f5bfdfa92c5bd3559efb01012a9d116)

#### [v0.1.63](https://e.coding.net/seer-group/meta-v/compare/v0.1.62...v0.1.63)

> 26 August 2024

- [feat] 新增相机距离 UI [`b903b27`](https://e.coding.net/seer-group/meta-v/commit/b903b27efa8229ec2c8ad79458f7c3d180f19177)
- [feat] 正交模式 支持缩放 [`5274f20`](https://e.coding.net/seer-group/meta-v/commit/5274f2030eff7b27764a20b2bc795f82cf6315dc)

#### [v0.1.62](https://e.coding.net/seer-group/meta-v/compare/v0.1.61...v0.1.62)

> 26 August 2024

- [feat] 新增世界球 UI；新增正交相机 [`098f943`](https://e.coding.net/seer-group/meta-v/commit/098f94310e2c1850dfe85ff70ddd7eddd5280948)

#### [v0.1.61](https://e.coding.net/seer-group/meta-v/compare/v0.1.60...v0.1.61)

> 22 August 2024

- [feat] 支持下载透视图，自定义命名 amr [`78cf067`](https://e.coding.net/seer-group/meta-v/commit/78cf0678d476dd17d8e926e5bb7d5967f2ffcde9)

#### [v0.1.60](https://e.coding.net/seer-group/meta-v/compare/v0.1.59...v0.1.60)

> 20 August 2024

- [feat] 整理 logo 对象 [`b8f5206`](https://e.coding.net/seer-group/meta-v/commit/b8f520680aa47a71dc4dac6fc63da520eb730342)

#### [v0.1.59](https://e.coding.net/seer-group/meta-v/compare/v0.1.58...v0.1.59)

> 19 August 2024

- [feat] 添加加速域名 [`1db3d5a`](https://e.coding.net/seer-group/meta-v/commit/1db3d5aab7579370ed5ccfe98f15c9ab761355fc)

#### [v0.1.58](https://e.coding.net/seer-group/meta-v/compare/v0.1.57...v0.1.58)

> 19 August 2024

- [feat] 调整插槽设备名称样式 [`56c75ab`](https://e.coding.net/seer-group/meta-v/commit/56c75abd1f5712c59eef8279838765973a6e4284)
- [feat] 字体调整 [`91a412b`](https://e.coding.net/seer-group/meta-v/commit/91a412bdbec763d33801fe5e7d00f59c33ccd4df)
- [fix] 移除测试代码 [`5a1aedd`](https://e.coding.net/seer-group/meta-v/commit/5a1aedd3a2512a0684eb5e5a23c989d7d6a88151)

#### [v0.1.57](https://e.coding.net/seer-group/meta-v/compare/v0.1.56...v0.1.57)

> 15 August 2024

- [feat] 优化 shapekey 防抖，增加记忆过滤 [`e2ffb65`](https://e.coding.net/seer-group/meta-v/commit/e2ffb655528f7858c92c76896e3171832fe455f4)

#### [v0.1.56](https://e.coding.net/seer-group/meta-v/compare/v0.1.55...v0.1.56)

> 15 August 2024

- [feat] 插槽显示设备名称 [`b6b2d56`](https://e.coding.net/seer-group/meta-v/commit/b6b2d567469b238bc2698ea8d65b18ebc22791eb)

#### [v0.1.55](https://e.coding.net/seer-group/meta-v/compare/v0.1.54...v0.1.55)

> 15 August 2024

#### [v0.1.54](https://e.coding.net/seer-group/meta-v/compare/v0.1.53...v0.1.54)

> 14 August 2024

- [feat] 优化插槽图层问题 [`f354782`](https://e.coding.net/seer-group/meta-v/commit/f3547822dd0549477c39d3280bec1a6efc4843a1)

#### [v0.1.53](https://e.coding.net/seer-group/meta-v/compare/v0.1.52...v0.1.53)

> 13 August 2024

- [feat] 增加删除 设备 事件 [`f41558d`](https://e.coding.net/seer-group/meta-v/commit/f41558d6499ca82ece85965593567e67da7e24fc)

#### [v0.1.52](https://e.coding.net/seer-group/meta-v/compare/v0.1.51...v0.1.52)

> 13 August 2024

- [feat] 提高渲染参数 [`fa4069a`](https://e.coding.net/seer-group/meta-v/commit/fa4069aa3137951ac8182aac51afbbae721b052e)
- [feat] types [`bbff28d`](https://e.coding.net/seer-group/meta-v/commit/bbff28d64062ca67d752bc4cbcd2ede06cf51eaf)

#### [v0.1.51](https://e.coding.net/seer-group/meta-v/compare/v0.1.50...v0.1.51)

> 13 August 2024

- [feat] 添加 shapekey 防抖，添加卸载设备 ui [`e3fe5fb`](https://e.coding.net/seer-group/meta-v/commit/e3fe5fb5ca54fdef06302e13aecc11be6447cac5)
- [feat] 优化 Functions ui [`b05ff05`](https://e.coding.net/seer-group/meta-v/commit/b05ff05920c484786358f5ae9d9920be3fa52ce6)

#### [v0.1.50](https://e.coding.net/seer-group/meta-v/compare/v0.1.49...v0.1.50)

> 7 August 2024

- [feat] 添加 head tail [`6b26e58`](https://e.coding.net/seer-group/meta-v/commit/6b26e580ef8ba5f7f78f6b9470b9fbd6d94a33cb)

#### [v0.1.49](https://e.coding.net/seer-group/meta-v/compare/v0.1.48...v0.1.49)

> 6 August 2024

- [feat] 开启后处理 [`358be97`](https://e.coding.net/seer-group/meta-v/commit/358be97c95d5bf5828ada120d2a1533b4f4cdb7c)

#### [v0.1.48](https://e.coding.net/seer-group/meta-v/compare/v0.1.47...v0.1.48)

> 6 August 2024

- [feat] AMR 暴露 goodsSize [`f9d9e7d`](https://e.coding.net/seer-group/meta-v/commit/f9d9e7dffe721387a012226fc048923045a28619)

#### [v0.1.47](https://e.coding.net/seer-group/meta-v/compare/v0.1.46...v0.1.47)

> 5 August 2024

- [fix] 1、适配 CDD20；2、修复选中之后渲染依然有插槽名称的 Bug [`42f1183`](https://e.coding.net/seer-group/meta-v/commit/42f1183ff1745fc78681e70888b228b5eef8dcd3)

#### [v0.1.46](https://e.coding.net/seer-group/meta-v/compare/v0.1.45...v0.1.46)

> 5 August 2024

- [fix] 修复 fov 配置不生效问题 [`117dc0c`](https://e.coding.net/seer-group/meta-v/commit/117dc0c6cfad5094ab0733cb9d63e47234e65b5e)

#### [v0.1.45](https://e.coding.net/seer-group/meta-v/compare/v0.1.44...v0.1.45)

> 2 August 2024

- [feat] download 方法修改为异步，解决下载时带尺寸标记问题，修复 firefox 不显示可安装 + 号 [`159b172`](https://e.coding.net/seer-group/meta-v/commit/159b172312db0a4f4dec1ab7fa1823ef718e3c9c)
- [fix] 修复部分车型插槽位移方向错误问题 [`aaeea95`](https://e.coding.net/seer-group/meta-v/commit/aaeea95e47050cea20adc46422e91e04a39b88c1)

#### [v0.1.44](https://e.coding.net/seer-group/meta-v/compare/v0.1.43...v0.1.44)

> 2 August 2024

#### [v0.1.43](https://e.coding.net/seer-group/meta-v/compare/v0.1.42...v0.1.43)

> 31 July 2024

- [feat] 添加 Amr 事件 [`7465dd7`](https://e.coding.net/seer-group/meta-v/commit/7465dd7e6e7c7fd9f4a25a2645233da1f2f426b5)

#### [v0.1.42](https://e.coding.net/seer-group/meta-v/compare/v0.1.41...v0.1.42)

> 30 July 2024

- [feat] 添加 readme [`c548e3a`](https://e.coding.net/seer-group/meta-v/commit/c548e3a1c571bb96bdc54113abe750106b3b455e)
- [feat] 添加场景内容清理方法 [`2e8cf5c`](https://e.coding.net/seer-group/meta-v/commit/2e8cf5cd47293f9627a9de932effc05d95ab4e5c)

#### [v0.1.41](https://e.coding.net/seer-group/meta-v/compare/v0.1.40...v0.1.41)

> 29 July 2024

- [feat] 添加隐藏 AMR 插槽的参数 [`93934ca`](https://e.coding.net/seer-group/meta-v/commit/93934cafe65ac073098e033cdc4d9b6700620ff6)

#### [v0.1.40](https://e.coding.net/seer-group/meta-v/compare/v0.1.39...v0.1.40)

> 29 July 2024

- [feat] 导出 amr 类 [`71449fc`](https://e.coding.net/seer-group/meta-v/commit/71449fc8023cf6dec35be23bdee48dcd2a032486)

#### [v0.1.39](https://e.coding.net/seer-group/meta-v/compare/v0.1.38...v0.1.39)

> 26 July 2024

- [fix] 修复 Logo 在渲染模式时的错位问题 [`8fe4428`](https://e.coding.net/seer-group/meta-v/commit/8fe4428b3011548d2c1014fede7b59622b45251a)
- [fix] 修复渲染结束之后移动相机无法恢复渲染的问题 [`7bb000a`](https://e.coding.net/seer-group/meta-v/commit/7bb000ae7d7963cdf88b8bbd1812a34716653193)

#### [v0.1.38](https://e.coding.net/seer-group/meta-v/compare/v0.1.37...v0.1.38)

> 25 July 2024

- [feat] clearAmr 的时候清理 overlayScene [`7fe5055`](https://e.coding.net/seer-group/meta-v/commit/7fe5055dcce98ca7dc94c4ba0c3ee66b435247af)
- [feat] 添加 ignoreSize 过滤 [`7e15a94`](https://e.coding.net/seer-group/meta-v/commit/7e15a942a41f41d0f7be3326cd70377cde7c2aed)

#### [v0.1.37](https://e.coding.net/seer-group/meta-v/compare/v0.1.36...v0.1.37)

> 25 July 2024

- [feat] 设置 fit 角度默认为 30 度 [`7f071a6`](https://e.coding.net/seer-group/meta-v/commit/7f071a6ec4eb1f604dbe60208ff4f4a75a898515)

#### [v0.1.36](https://e.coding.net/seer-group/meta-v/compare/v0.1.35...v0.1.36)

> 25 July 2024

- [fix] 修复 Logo 没有跟随 shapekey 变动问题 [`e8be617`](https://e.coding.net/seer-group/meta-v/commit/e8be617ac60d4ec0b75c3adb96297a4ad9616ffd)

#### [v0.1.35](https://e.coding.net/seer-group/meta-v/compare/v0.1.34...v0.1.35)

> 24 July 2024

- [fix] 修复 Cropper resize 方法引用对象错误问题 [`f646a0b`](https://e.coding.net/seer-group/meta-v/commit/f646a0b80c9f777de375dd4499d83b81f4f65317)

#### [v0.1.34](https://e.coding.net/seer-group/meta-v/compare/v0.1.33...v0.1.34)

> 24 July 2024

- [fix] 修复 Logo clear 方法无效问题 [`a16df38`](https://e.coding.net/seer-group/meta-v/commit/a16df384f2d8fbd7e9020c9d8254a61091a6b3b7)

#### [v0.1.33](https://e.coding.net/seer-group/meta-v/compare/v0.1.32...v0.1.33)

> 24 July 2024

- [fix] 修复渲染模式下贴图重复问题 [`b89b269`](https://e.coding.net/seer-group/meta-v/commit/b89b269af1880269e74d7d4ee94d0a69eb42a5c5)

#### [v0.1.32](https://e.coding.net/seer-group/meta-v/compare/v0.1.31...v0.1.32)

> 23 July 2024

- [fix] 修复 jpg 像素图片边缘像素拉伸问题 [`a0daab2`](https://e.coding.net/seer-group/meta-v/commit/a0daab221071446c9b4edf22b125f45dbb09cb68)
- [fix] 调整 texture 加载时机 [`294b0a5`](https://e.coding.net/seer-group/meta-v/commit/294b0a599f879092ed61b2cfbef2e6b90976a569)

#### [v0.1.31](https://e.coding.net/seer-group/meta-v/compare/v0.1.30...v0.1.31)

> 23 July 2024

- [fix] 修复模式切换时引起的 logo 变化 [`a23b5c9`](https://e.coding.net/seer-group/meta-v/commit/a23b5c953399b27d41a8727ca24e547737a04b2b)

#### [v0.1.30](https://e.coding.net/seer-group/meta-v/compare/v0.1.29...v0.1.30)

> 23 July 2024

- [fix] 修复 Logo 缩放 Y 轴拉伸问题 [`6f344c6`](https://e.coding.net/seer-group/meta-v/commit/6f344c67256f6fada2b5eacdf14bce1ff6a63c7e)
- [fix] 修复初始化料箱层数延时问题 [`30e860e`](https://e.coding.net/seer-group/meta-v/commit/30e860e0443fa3799fa6ced17aab239aba75d409)
- [feat] 加深 uv 轮廓深度 [`02e15a3`](https://e.coding.net/seer-group/meta-v/commit/02e15a3bf1a2db0c09fbc8f20d930f5a5ed34bc4)

#### [v0.1.29](https://e.coding.net/seer-group/meta-v/compare/v0.1.28...v0.1.29)

> 22 July 2024

- [feat] 新增料箱车物料占位效果 [`157ceef`](https://e.coding.net/seer-group/meta-v/commit/157ceef378b0615d3b6457e6b8538b94909b5159)
- [feat] 移除无用渲染器 [`16d8a11`](https://e.coding.net/seer-group/meta-v/commit/16d8a11a3179897aad24af161160a1e6d15d1479)

#### [v0.1.28](https://e.coding.net/seer-group/meta-v/compare/v0.1.27...v0.1.28)

> 17 July 2024

- [feat] 插槽支持挡板 [`bcf1834`](https://e.coding.net/seer-group/meta-v/commit/bcf183484e30af45f08352abdf7a03da274aae2c)
- [feat] 顶升支持 Controller 控制 [`4fd8210`](https://e.coding.net/seer-group/meta-v/commit/4fd821041dba99f3b76fd3a3b28ca720784677fc)

#### [v0.1.27](https://e.coding.net/seer-group/meta-v/compare/v0.1.26...v0.1.27)

> 16 July 2024

- [feat] 支持配置多级门架高度，支持渲染过程中配置车体参数 [`b7fb300`](https://e.coding.net/seer-group/meta-v/commit/b7fb30000b9db8a5c4657abd09c2968070b09e72)

#### [v0.1.26](https://e.coding.net/seer-group/meta-v/compare/v0.1.25...v0.1.26)

> 16 July 2024

- [feat] 支持多级门架高度级联响应 [`33a5542`](https://e.coding.net/seer-group/meta-v/commit/33a5542d35e49144f57290834c2eacbe100caa7e)

#### [v0.1.25](https://e.coding.net/seer-group/meta-v/compare/v0.1.24...v0.1.25)

> 15 July 2024

- [feat] 根据起始高度自动设置 门架 高度 [`2ddaef0`](https://e.coding.net/seer-group/meta-v/commit/2ddaef0c823e4418cfa4e790ab35db6813c8e748)

#### [v0.1.24](https://e.coding.net/seer-group/meta-v/compare/v0.1.23...v0.1.24)

> 15 July 2024

- [feat] 更新 CropperConfigs 数据结构 [`81f8574`](https://e.coding.net/seer-group/meta-v/commit/81f8574b10c0e2014b329d1b317092db45fbf697)

#### [v0.1.23](https://e.coding.net/seer-group/meta-v/compare/v0.1.22...v0.1.23)

> 11 July 2024

- [fix] 修复无功能清单时 仍然显示添加按钮问题 [`787d4bd`](https://e.coding.net/seer-group/meta-v/commit/787d4bda070fe1b38026194e6a850c16fb3d7b4c)

#### [v0.1.22](https://e.coding.net/seer-group/meta-v/compare/v0.1.21...v0.1.22)

> 11 July 2024

- [fix] 移除测试代码 [`90348f7`](https://e.coding.net/seer-group/meta-v/commit/90348f712c2f27cc6ceb1e2b4efdf1314430758a)

#### [v0.1.21](https://e.coding.net/seer-group/meta-v/compare/v0.1.20...v0.1.21)

> 11 July 2024

#### [v0.1.20](https://e.coding.net/seer-group/meta-v/compare/v0.1.19...v0.1.20)

> 11 July 2024

- [feat] 优化卡片样式 [`6ac0b9d`](https://e.coding.net/seer-group/meta-v/commit/6ac0b9d49a93e4d885e64fa5eb4d29beb49f2d17)

#### [v0.1.19](https://e.coding.net/seer-group/meta-v/compare/v0.1.18...v0.1.19)

> 11 July 2024

- [feat] 插槽添加功能列表 [`4b51b64`](https://e.coding.net/seer-group/meta-v/commit/4b51b64a0ffbeae1fde9caede5d6dd8b1ab1bebf)
- [feat] 优化料斗数量设置 [`f710fdd`](https://e.coding.net/seer-group/meta-v/commit/f710fddd94407e7c143c0b0865783beebf3e0e7f)
- [feat] 优化测试模型 [`403d5f8`](https://e.coding.net/seer-group/meta-v/commit/403d5f825a51e4ec4002d855faefd398cbebcc76)

#### [v0.1.18](https://e.coding.net/seer-group/meta-v/compare/v0.1.17...v0.1.18)

> 10 July 2024

- [fix] 修复 UV 尺寸不对的问题 [`5b53c4f`](https://e.coding.net/seer-group/meta-v/commit/5b53c4f18994f125a4caf28aa61790da6732ed9b)

#### [v0.1.17](https://e.coding.net/seer-group/meta-v/compare/v0.1.16...v0.1.17)

> 10 July 2024

- [fix] 修复线条小时问题 [`33be39b`](https://e.coding.net/seer-group/meta-v/commit/33be39b011513112df41f21ad791f201044f3c05)

#### [v0.1.16](https://e.coding.net/seer-group/meta-v/compare/v0.1.15...v0.1.16)

> 9 July 2024

- [fix] 修复 Skin 重复添加问题 [`cc5f918`](https://e.coding.net/seer-group/meta-v/commit/cc5f918608e1b360f096430d92fb4735575fff7a)

#### [v0.1.15](https://e.coding.net/seer-group/meta-v/compare/v0.1.14...v0.1.15)

> 9 July 2024

- [feat] 新增 Logo 的 clear 方法，提供清楚 Logo 能力 [`9f22572`](https://e.coding.net/seer-group/meta-v/commit/9f22572221483f3647512b0c09732ba8092cb818)
- [feat] SizeBox 添加 dispose 方法 [`e5b428f`](https://e.coding.net/seer-group/meta-v/commit/e5b428f3733627abea4a4c9f3866be08ce05efe2)

#### [v0.1.14](https://e.coding.net/seer-group/meta-v/compare/v0.1.13...v0.1.14)

> 9 July 2024

- [feat] 添加新的虚线旋转半径 [`872b7d9`](https://e.coding.net/seer-group/meta-v/commit/872b7d997e1ff64433836ebcb72d84cf47119505)
- [feat] 添加更加友好的尺寸标注，高度值跟随观察者转动 [`f528bf5`](https://e.coding.net/seer-group/meta-v/commit/f528bf518f54d880ba896c9f8819f4c42a4e54fb)
- [feat] 场景相机 fov 可变 [`459dc00`](https://e.coding.net/seer-group/meta-v/commit/459dc00b80adbd7156b80b32329439efba83263e)

#### [v0.1.13](https://e.coding.net/seer-group/meta-v/compare/v0.1.12...v0.1.13)

> 8 July 2024

- [feat] 添加新的虚线标尺 [`84ee9b4`](https://e.coding.net/seer-group/meta-v/commit/84ee9b46db878ff388cd7984b1d70fed5af6e832)
- [feat] 移除废弃方法 [`68ebfd5`](https://e.coding.net/seer-group/meta-v/commit/68ebfd55d1039d153ecfc766e3e8b94ad685cb47)

#### [v0.1.12](https://e.coding.net/seer-group/meta-v/compare/v0.1.11...v0.1.12)

> 8 July 2024

- [feat] 添加 LOGO Mesh 的 2D UV 映射 [`9cf870f`](https://e.coding.net/seer-group/meta-v/commit/9cf870fa930e6b9f41eacf776460fd8c602981ba)
- [feat] 更新 changelog 方法描述 [`fcb4ede`](https://e.coding.net/seer-group/meta-v/commit/fcb4edefec52433a2638800b4048f00bcc060fe1)

#### [v0.1.11](https://e.coding.net/seer-group/meta-v/compare/v0.1.10...v0.1.11)

> 8 July 2024

- [feat] 修复 Logo 高度计算不准问题 [`30315df`](https://e.coding.net/seer-group/meta-v/commit/30315dff0bc50584c0c3268866f4af685b40a325)
- [feat] 添加画布配置导出，设置方法，用于支持图片编辑 [`6ea707e`](https://e.coding.net/seer-group/meta-v/commit/6ea707e2c55dd423d541d75308927a3730284254)

#### [v0.1.10](https://e.coding.net/seer-group/meta-v/compare/v0.1.9...v0.1.10)

> 5 July 2024

- [feat] Logo 支持 reset 恢复默认值 [`242da42`](https://e.coding.net/seer-group/meta-v/commit/242da42b75dccfb4776c5267ca4595204a2b1246)

#### [v0.1.9](https://e.coding.net/seer-group/meta-v/compare/v0.1.8...v0.1.9)

> 4 July 2024

- [feat] [`d41f19c`](https://e.coding.net/seer-group/meta-v/commit/d41f19c5912a676dac6d22373f6273c57bc3d2f7)
- [feat] 换肤支持恢复默认值 [`3a27d2b`](https://e.coding.net/seer-group/meta-v/commit/3a27d2b93d16529535337c0d1239cd49b4d855c9)

#### [v0.1.8](https://e.coding.net/seer-group/meta-v/compare/v0.1.7...v0.1.8)

> 3 July 2024

- [fit] 修复设备跟随插槽错位问题 [`91a91ea`](https://e.coding.net/seer-group/meta-v/commit/91a91ea25c0f2ba6eed1ee450889c5313f34b47a)
- [feat] 新的测试模型 [`656605f`](https://e.coding.net/seer-group/meta-v/commit/656605f63be3073b542dd670182f0d32fd664832)

#### [v0.1.7](https://e.coding.net/seer-group/meta-v/compare/v0.1.6...v0.1.7)

> 3 July 2024

- [fix] 插槽跟随旋转中心点遗留问题 [`3c5672f`](https://e.coding.net/seer-group/meta-v/commit/3c5672faf73676c86cf4aee993622c4323c068f2)

#### [v0.1.6](https://e.coding.net/seer-group/meta-v/compare/v0.1.5...v0.1.6)

> 2 July 2024

- [fix] 修复插槽跟随中心点位移问题 [`f643850`](https://e.coding.net/seer-group/meta-v/commit/f64385024d9c405cb65cb49b308f753af02f8168)
- [fix] 渲染模式下 依旧显示旋转半径问题 [`c2f23f7`](https://e.coding.net/seer-group/meta-v/commit/c2f23f707c612296be1ede905fce9e287aa415a6)
- [feat] 渲染模式使用纯背景 [`addcd4e`](https://e.coding.net/seer-group/meta-v/commit/addcd4ea867c278c8cbea971b365dd195fe5151c)

#### [v0.1.5](https://e.coding.net/seer-group/meta-v/compare/v0.1.4...v0.1.5)

> 2 July 2024

- [fix] 修复叉车插齿变更之后插槽位移不一致问题 [`6018578`](https://e.coding.net/seer-group/meta-v/commit/601857851fc5483b390cdb8e56be89378809b729)
- [feat] 资源文件使用相对路径 [`9eeb6a5`](https://e.coding.net/seer-group/meta-v/commit/9eeb6a50652330e945ee7f91cd1e8ae96bed3402)

#### [v0.1.4](https://e.coding.net/seer-group/meta-v/compare/v0.1.3...v0.1.4)

> 2 July 2024

- [feat] 添加皮带组件 [`45dffbf`](https://e.coding.net/seer-group/meta-v/commit/45dffbf4b75185f1edd00f250840879f95f682f2)
- [feat] 支持 isTransparent 透明标记 [`8564231`](https://e.coding.net/seer-group/meta-v/commit/8564231b3349c85bec27f81a2c77ead3b3de64ec)
- [feat] 添加料箱车背篓数量 setShelfLevel [`8ec8124`](https://e.coding.net/seer-group/meta-v/commit/8ec81244f3611096acad4870c161c73d28a7f4d6)

#### [v0.1.3](https://e.coding.net/seer-group/meta-v/compare/v0.1.2...v0.1.3)

> 1 July 2024

#### [v0.1.2](https://e.coding.net/seer-group/meta-v/compare/v0.1.1...v0.1.2)

> 1 July 2024

#### v0.1.1

> 1 July 2024

- [feat] update lock file [`b299afa`](https://e.coding.net/seer-group/meta-v/commit/b299afae0a35dfa9057f21de0b12cd69958c605b)
- init [`cf9dd2a`](https://e.coding.net/seer-group/meta-v/commit/cf9dd2a3186841a42e45be69b1a4ee04442653ad)
- [feat] [`34ee5ac`](https://e.coding.net/seer-group/meta-v/commit/34ee5ac9756d4c0c45b1bd37b8ca943800d242de)
