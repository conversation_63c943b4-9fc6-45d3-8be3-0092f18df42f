
* 使用说明

** 安装

```cronexp

yarn add shop-components

```

** 使用步骤

*** 初始化场景
```ts
const scene = new Scene3D({
  useSound: true
})
scene.appendTo(this.container)

// 销毁 场景
scene.destroy()
```

*** 获取插槽，支持换肤的对象，Logo
```ts
this.colors = this.scene.amr.skins
this.logos = this.scene.amr.logos
this.slots = this.scene.amr.slots
```

*** 换颜色
```ts
this.scene.amr.changeSkin(s, value)
```

*** 插槽
```ts
/*
export const SLOT_EVENTS = {
  ON_DROP: 'slot-on-drop',
  ON_BEFORE_SELECTED: 'slot-on-before-selected',
  ON_SELECTED: 'slot-on-selected',
  ON_DEVICE_SELECTED: 'slot-on-device-selected',
  ON_DROP_ERROR: 'slot-on-drop-error',
  ON_ERROR_STATE_CHANGE: 'slot-on-error-state-change',
  ON_FUNCTION_REMOVED: 'slot-on-function-removed',
  ON_FUNCTION_ADDED: 'slot-on-function-added'
}
*/

this.scene.amr.slots.forEach(s => {
  s.addEventListener(SLOT_EVENTS.ON_FUNCTION_REMOVED, f => {
    console.log('ON_FUNCTION_REMOVED', f)
  })
  s.addEventListener(SLOT_EVENTS.ON_FUNCTION_ADDED, f => {
    console.log('ON_FUNCTION_ADDED', f)
  })
})

// 设置插槽功能清单
const fs = [
  '单激光定位导航',
  '激光反光板导航',
  '普通栈板识别',
  '2D 障碍物停障',
  '2D 障碍物绕障1',
  '2D 障碍物绕障2',
  '2D 障碍物绕障3',
  '2D 障碍物绕障4',
  '2D 障碍物绕障5'
]
s.allFunctions = [...fs]
fs.pop()
fs.pop()
fs.pop()
fs.pop()
fs.pop()
s.addFunctions(fs)

dragstart = (e: DragEvent) => {
  const deviceId = (e.target as HTMLElement)?.getAttribute('data-id')
  let url = 'https://cdn2-1304552240.cos.ap-shanghai.myqcloud.com/rp/devices/Mid-70.sglb'
  let bracketUrl = 'https://cdn2-1304552240.cos.ap-shanghai.myqcloud.com/rp/brackets/Mid-70_Bracket.sglb'
  const id = (e.target as HTMLImageElement).getAttribute('data-id')
  const jsonDataString = JSON.stringify({
    id, name: 'H1E0-02C',
    url,
    bracketUrl
  } as DeviceInfo) // 转换为字符串
  e.dataTransfer?.setData('application/json', jsonDataString)

  this.scene.amr.slots.forEach((s, i) => s.canInstall(Math.random() > 0.5))
  this.scene.amr.bodyOpacity = 0.2
}

dragend = (e: DragEvent) => {
  this.drag?.classList.remove('dragging')
  this.scene.amr.slots.forEach(s => s.canInstall(false))
}

// 获取百分百 或者 自己写 requestAnimationFrame 获取 this.scene.sampleCount
this.scene.renderer.domElement.addEventListener('ticker', () => {
  const percent = this.scene.sampleCount / this.scene.maxSample
  if (this.progressBar) {
    this.progressBar.style.width = percent * 100 + '%'
  }
})


```

*** 初始化 Cropper
```ts
const cropper = new Cropper()
if (this.cropperContainer) {
  this.cropperContainer.append(this.cropper)
  this.cropper.resize(220, 220)
  // 画布图片变动事件回调，设置 Logo 对象
  this.cropper.addEventListener('image-change', (e: any) => {
    if (e.detail && e.detail.image) {
      if (!this.selectedLogo) return
      const configs = this.cropper.getConfigs()
      this.scene.amr.changeLogo(this.selectedLogo, configs)

      // 获取图片实际尺寸，用于给业务做打印用
      this.logoSize = this.cropper.imageRealSize
    }
  })
}

// 清空画布
this.cropper.clear()

// 清空车体 logo 图片
logo.clear()

// 恢复默认 logo（每个车体 logo 虽然都是 seer 但是 uv 不同，所以自带的图片都是 base64 保存，新上传的都是 url 形式，如果未修改 logo，建议数据解析成固定的一张 seer logo url）
logo.reset()

// 根据数据修改画布 用于二次打开画布编辑 logo 图片
this.cropper.updateConfigs(configs).then()

// 获取画布配置信息 用户保存或者 changeLogo
const configs = this.cropper.getConfigs()
```


*** 模式切换
```ts
this.scene.amr.bodyOpacity = 0.2  // 透明模式
this.scene.amr.bodyOpacity = 1 // 默认模式
```

*** 开启渲染
```ts
this.scene.generateBVH()
```

*** 关闭渲染
```ts
this.scene.usePathTracing = false
```

*** 恢复到最佳视角
```ts
this.scene.fit()

// 当车体高度变化时 建议调用该方法
```

*** 设置车体属性
```ts
// 通过 shapeKeys 返回车体支持的所有自定义属性，如 height，length，liftOuterWidth（插齿外宽）
const shapeKeys = scene.amr.shapeKeys

// 可以根据业务 params key 匹配对应的 shapeKey
// shapeKey = 'width'|'length'|'height'
scene.amr.setShapeKey(shapeKey)

// 设置车体货物尺寸
scene.amr.setGoodsSize({ length: 0, width: 0, height: 0 })

// 设置最大起升高度
scene.amr.setForkMaxHeight(value)

// 设置顶升车行程
scene.amr.setJackMaxHeight(value)

// 设置料箱车行程
scene.amr.setLiftMaxHeight(value)

// 设置料箱料斗层数
scene.amr.setShelfLevel(value)


```








