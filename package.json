{"name": "shop-components", "version": "0.1.68", "description": "Shop components", "main": "dist/shop-components.umd.js", "module": "dist/shop-components.mjs", "types": "dist/main.d.ts", "scripts": {"dts": "tsc --emitDeclarationOnly & tsc & storybook build -o dist", "test": "echo \"Error: no test specified\" && exit 1", "dev": "storybook dev -p 6006", "build": "storybook build -o dist", "vite": "vite build", "vitedev": "vite dev", "version": "auto-changelog -p && git add CHANGELOG.md"}, "files": ["dist", "package.json"], "keywords": [], "author": "machengda<<EMAIL>>", "license": "ISC", "devDependencies": {"@storybook/addon-essentials": "^8.0.6", "@storybook/addon-links": "^8.0.6", "@storybook/addon-mdx-gfm": "^8.0.6", "@storybook/blocks": "^8.0.6", "@storybook/web-components": "^8.0.6", "@storybook/web-components-vite": "^8.0.6", "@storybook/addon-themes": "^8.0.6", "auto-changelog": "^2.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "storybook": "^8.0.6", "vite": "^5.1.6", "vite-plugin-dts": "^3.7.3"}, "resolutions": {"string-width": "4.2.3"}, "dependencies": {"@nodetoy/three-nodetoy": "^0.1.36", "@tweakpane/core": "^2.0.3", "@tweenjs/tween.js": "^23.1.2", "@types/bezier-js": "^4.1.1", "@types/howler": "^2.2.11", "@types/three": "^0.164.0", "bezier-js": "^6.1.4", "eventemitter3": "^5.0.1", "howler": "^2.2.4", "pixi-viewport": "^5.0.2", "pixi.js": "7.4.0", "stats-fps.js": "0.0.6", "stylus": "^0.63.0", "lit": "^3.1.0", "three": "^0.164.0", "three-gpu-pathtracer": "0.0.23", "three-mesh-bvh": "^0.7.5", "troika-three-text": "^0.49.1", "tweakpane": "^4.0.3"}}