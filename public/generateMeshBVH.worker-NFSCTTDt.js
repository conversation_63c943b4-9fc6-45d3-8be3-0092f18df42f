(function(){"use strict";/**
 * @license
 * Copyright 2010-2024 Three.js Authors
 * SPDX-License-Identifier: MIT
 */const Bi="164",ns="";class Xe{addEventListener(t,e){this._listeners===void 0&&(this._listeners={});const i=this._listeners;i[t]===void 0&&(i[t]=[]),i[t].indexOf(e)===-1&&i[t].push(e)}hasEventListener(t,e){if(this._listeners===void 0)return!1;const i=this._listeners;return i[t]!==void 0&&i[t].indexOf(e)!==-1}removeEventListener(t,e){if(this._listeners===void 0)return;const s=this._listeners[t];if(s!==void 0){const n=s.indexOf(e);n!==-1&&s.splice(n,1)}}dispatchEvent(t){if(this._listeners===void 0)return;const i=this._listeners[t.type];if(i!==void 0){t.target=this;const s=i.slice(0);for(let n=0,r=s.length;n<r;n++)s[n].call(this,t);t.target=null}}}const q=["00","01","02","03","04","05","06","07","08","09","0a","0b","0c","0d","0e","0f","10","11","12","13","14","15","16","17","18","19","1a","1b","1c","1d","1e","1f","20","21","22","23","24","25","26","27","28","29","2a","2b","2c","2d","2e","2f","30","31","32","33","34","35","36","37","38","39","3a","3b","3c","3d","3e","3f","40","41","42","43","44","45","46","47","48","49","4a","4b","4c","4d","4e","4f","50","51","52","53","54","55","56","57","58","59","5a","5b","5c","5d","5e","5f","60","61","62","63","64","65","66","67","68","69","6a","6b","6c","6d","6e","6f","70","71","72","73","74","75","76","77","78","79","7a","7b","7c","7d","7e","7f","80","81","82","83","84","85","86","87","88","89","8a","8b","8c","8d","8e","8f","90","91","92","93","94","95","96","97","98","99","9a","9b","9c","9d","9e","9f","a0","a1","a2","a3","a4","a5","a6","a7","a8","a9","aa","ab","ac","ad","ae","af","b0","b1","b2","b3","b4","b5","b6","b7","b8","b9","ba","bb","bc","bd","be","bf","c0","c1","c2","c3","c4","c5","c6","c7","c8","c9","ca","cb","cc","cd","ce","cf","d0","d1","d2","d3","d4","d5","d6","d7","d8","d9","da","db","dc","dd","de","df","e0","e1","e2","e3","e4","e5","e6","e7","e8","e9","ea","eb","ec","ed","ee","ef","f0","f1","f2","f3","f4","f5","f6","f7","f8","f9","fa","fb","fc","fd","fe","ff"];function de(){const u=Math.random()*4294967295|0,t=Math.random()*4294967295|0,e=Math.random()*4294967295|0,i=Math.random()*4294967295|0;return(q[u&255]+q[u>>8&255]+q[u>>16&255]+q[u>>24&255]+"-"+q[t&255]+q[t>>8&255]+"-"+q[t>>16&15|64]+q[t>>24&255]+"-"+q[e&63|128]+q[e>>8&255]+"-"+q[e>>16&255]+q[e>>24&255]+q[i&255]+q[i>>8&255]+q[i>>16&255]+q[i>>24&255]).toLowerCase()}function at(u,t,e){return Math.max(t,Math.min(e,u))}function Qt(u,t){switch(t.constructor){case Float32Array:return u;case Uint32Array:return u/4294967295;case Uint16Array:return u/65535;case Uint8Array:return u/255;case Int32Array:return Math.max(u/2147483647,-1);case Int16Array:return Math.max(u/32767,-1);case Int8Array:return Math.max(u/127,-1);default:throw new Error("Invalid component type.")}}function O(u,t){switch(t.constructor){case Float32Array:return u;case Uint32Array:return Math.round(u*4294967295);case Uint16Array:return Math.round(u*65535);case Uint8Array:return Math.round(u*255);case Int32Array:return Math.round(u*2147483647);case Int16Array:return Math.round(u*32767);case Int8Array:return Math.round(u*127);default:throw new Error("Invalid component type.")}}class v{constructor(t=0,e=0){v.prototype.isVector2=!0,this.x=t,this.y=e}get width(){return this.x}set width(t){this.x=t}get height(){return this.y}set height(t){this.y=t}set(t,e){return this.x=t,this.y=e,this}setScalar(t){return this.x=t,this.y=t,this}setX(t){return this.x=t,this}setY(t){return this.y=t,this}setComponent(t,e){switch(t){case 0:this.x=e;break;case 1:this.y=e;break;default:throw new Error("index is out of range: "+t)}return this}getComponent(t){switch(t){case 0:return this.x;case 1:return this.y;default:throw new Error("index is out of range: "+t)}}clone(){return new this.constructor(this.x,this.y)}copy(t){return this.x=t.x,this.y=t.y,this}add(t){return this.x+=t.x,this.y+=t.y,this}addScalar(t){return this.x+=t,this.y+=t,this}addVectors(t,e){return this.x=t.x+e.x,this.y=t.y+e.y,this}addScaledVector(t,e){return this.x+=t.x*e,this.y+=t.y*e,this}sub(t){return this.x-=t.x,this.y-=t.y,this}subScalar(t){return this.x-=t,this.y-=t,this}subVectors(t,e){return this.x=t.x-e.x,this.y=t.y-e.y,this}multiply(t){return this.x*=t.x,this.y*=t.y,this}multiplyScalar(t){return this.x*=t,this.y*=t,this}divide(t){return this.x/=t.x,this.y/=t.y,this}divideScalar(t){return this.multiplyScalar(1/t)}applyMatrix3(t){const e=this.x,i=this.y,s=t.elements;return this.x=s[0]*e+s[3]*i+s[6],this.y=s[1]*e+s[4]*i+s[7],this}min(t){return this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this}max(t){return this.x=Math.max(this.x,t.x),this.y=Math.max(this.y,t.y),this}clamp(t,e){return this.x=Math.max(t.x,Math.min(e.x,this.x)),this.y=Math.max(t.y,Math.min(e.y,this.y)),this}clampScalar(t,e){return this.x=Math.max(t,Math.min(e,this.x)),this.y=Math.max(t,Math.min(e,this.y)),this}clampLength(t,e){const i=this.length();return this.divideScalar(i||1).multiplyScalar(Math.max(t,Math.min(e,i)))}floor(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this}ceil(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this}round(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this}roundToZero(){return this.x=Math.trunc(this.x),this.y=Math.trunc(this.y),this}negate(){return this.x=-this.x,this.y=-this.y,this}dot(t){return this.x*t.x+this.y*t.y}cross(t){return this.x*t.y-this.y*t.x}lengthSq(){return this.x*this.x+this.y*this.y}length(){return Math.sqrt(this.x*this.x+this.y*this.y)}manhattanLength(){return Math.abs(this.x)+Math.abs(this.y)}normalize(){return this.divideScalar(this.length()||1)}angle(){return Math.atan2(-this.y,-this.x)+Math.PI}angleTo(t){const e=Math.sqrt(this.lengthSq()*t.lengthSq());if(e===0)return Math.PI/2;const i=this.dot(t)/e;return Math.acos(at(i,-1,1))}distanceTo(t){return Math.sqrt(this.distanceToSquared(t))}distanceToSquared(t){const e=this.x-t.x,i=this.y-t.y;return e*e+i*i}manhattanDistanceTo(t){return Math.abs(this.x-t.x)+Math.abs(this.y-t.y)}setLength(t){return this.normalize().multiplyScalar(t)}lerp(t,e){return this.x+=(t.x-this.x)*e,this.y+=(t.y-this.y)*e,this}lerpVectors(t,e,i){return this.x=t.x+(e.x-t.x)*i,this.y=t.y+(e.y-t.y)*i,this}equals(t){return t.x===this.x&&t.y===this.y}fromArray(t,e=0){return this.x=t[e],this.y=t[e+1],this}toArray(t=[],e=0){return t[e]=this.x,t[e+1]=this.y,t}fromBufferAttribute(t,e){return this.x=t.getX(e),this.y=t.getY(e),this}rotateAround(t,e){const i=Math.cos(e),s=Math.sin(e),n=this.x-t.x,r=this.y-t.y;return this.x=n*i-r*s+t.x,this.y=n*s+r*i+t.y,this}random(){return this.x=Math.random(),this.y=Math.random(),this}*[Symbol.iterator](){yield this.x,yield this.y}}class Tt{constructor(t,e,i,s,n,r,a,o,h){Tt.prototype.isMatrix3=!0,this.elements=[1,0,0,0,1,0,0,0,1],t!==void 0&&this.set(t,e,i,s,n,r,a,o,h)}set(t,e,i,s,n,r,a,o,h){const c=this.elements;return c[0]=t,c[1]=s,c[2]=a,c[3]=e,c[4]=n,c[5]=o,c[6]=i,c[7]=r,c[8]=h,this}identity(){return this.set(1,0,0,0,1,0,0,0,1),this}copy(t){const e=this.elements,i=t.elements;return e[0]=i[0],e[1]=i[1],e[2]=i[2],e[3]=i[3],e[4]=i[4],e[5]=i[5],e[6]=i[6],e[7]=i[7],e[8]=i[8],this}extractBasis(t,e,i){return t.setFromMatrix3Column(this,0),e.setFromMatrix3Column(this,1),i.setFromMatrix3Column(this,2),this}setFromMatrix4(t){const e=t.elements;return this.set(e[0],e[4],e[8],e[1],e[5],e[9],e[2],e[6],e[10]),this}multiply(t){return this.multiplyMatrices(this,t)}premultiply(t){return this.multiplyMatrices(t,this)}multiplyMatrices(t,e){const i=t.elements,s=e.elements,n=this.elements,r=i[0],a=i[3],o=i[6],h=i[1],c=i[4],f=i[7],l=i[2],d=i[5],p=i[8],y=s[0],x=s[3],m=s[6],g=s[1],w=s[4],M=s[7],b=s[2],z=s[5],_=s[8];return n[0]=r*y+a*g+o*b,n[3]=r*x+a*w+o*z,n[6]=r*m+a*M+o*_,n[1]=h*y+c*g+f*b,n[4]=h*x+c*w+f*z,n[7]=h*m+c*M+f*_,n[2]=l*y+d*g+p*b,n[5]=l*x+d*w+p*z,n[8]=l*m+d*M+p*_,this}multiplyScalar(t){const e=this.elements;return e[0]*=t,e[3]*=t,e[6]*=t,e[1]*=t,e[4]*=t,e[7]*=t,e[2]*=t,e[5]*=t,e[8]*=t,this}determinant(){const t=this.elements,e=t[0],i=t[1],s=t[2],n=t[3],r=t[4],a=t[5],o=t[6],h=t[7],c=t[8];return e*r*c-e*a*h-i*n*c+i*a*o+s*n*h-s*r*o}invert(){const t=this.elements,e=t[0],i=t[1],s=t[2],n=t[3],r=t[4],a=t[5],o=t[6],h=t[7],c=t[8],f=c*r-a*h,l=a*o-c*n,d=h*n-r*o,p=e*f+i*l+s*d;if(p===0)return this.set(0,0,0,0,0,0,0,0,0);const y=1/p;return t[0]=f*y,t[1]=(s*h-c*i)*y,t[2]=(a*i-s*r)*y,t[3]=l*y,t[4]=(c*e-s*o)*y,t[5]=(s*n-a*e)*y,t[6]=d*y,t[7]=(i*o-h*e)*y,t[8]=(r*e-i*n)*y,this}transpose(){let t;const e=this.elements;return t=e[1],e[1]=e[3],e[3]=t,t=e[2],e[2]=e[6],e[6]=t,t=e[5],e[5]=e[7],e[7]=t,this}getNormalMatrix(t){return this.setFromMatrix4(t).invert().transpose()}transposeIntoArray(t){const e=this.elements;return t[0]=e[0],t[1]=e[3],t[2]=e[6],t[3]=e[1],t[4]=e[4],t[5]=e[7],t[6]=e[2],t[7]=e[5],t[8]=e[8],this}setUvTransform(t,e,i,s,n,r,a){const o=Math.cos(n),h=Math.sin(n);return this.set(i*o,i*h,-i*(o*r+h*a)+r+t,-s*h,s*o,-s*(-h*r+o*a)+a+e,0,0,1),this}scale(t,e){return this.premultiply(Oe.makeScale(t,e)),this}rotate(t){return this.premultiply(Oe.makeRotation(-t)),this}translate(t,e){return this.premultiply(Oe.makeTranslation(t,e)),this}makeTranslation(t,e){return t.isVector2?this.set(1,0,t.x,0,1,t.y,0,0,1):this.set(1,0,t,0,1,e,0,0,1),this}makeRotation(t){const e=Math.cos(t),i=Math.sin(t);return this.set(e,-i,0,i,e,0,0,0,1),this}makeScale(t,e){return this.set(t,0,0,0,e,0,0,0,1),this}equals(t){const e=this.elements,i=t.elements;for(let s=0;s<9;s++)if(e[s]!==i[s])return!1;return!0}fromArray(t,e=0){for(let i=0;i<9;i++)this.elements[i]=t[i+e];return this}toArray(t=[],e=0){const i=this.elements;return t[e]=i[0],t[e+1]=i[1],t[e+2]=i[2],t[e+3]=i[3],t[e+4]=i[4],t[e+5]=i[5],t[e+6]=i[6],t[e+7]=i[7],t[e+8]=i[8],t}clone(){return new this.constructor().fromArray(this.elements)}}const Oe=new Tt;function rs(u){for(let t=u.length-1;t>=0;--t)if(u[t]>=65535)return!0;return!1}function Ti(u){return document.createElementNS("http://www.w3.org/1999/xhtml",u)}const Pi={};function os(u){u in Pi||(Pi[u]=!0,console.warn(u))}function Ye(u){return u<.04045?u*.0773993808:Math.pow(u*.9478672986+.0521327014,2.4)}let Et;class as{static getDataURL(t){if(/^data:/i.test(t.src)||typeof HTMLCanvasElement>"u")return t.src;let e;if(t instanceof HTMLCanvasElement)e=t;else{Et===void 0&&(Et=Ti("canvas")),Et.width=t.width,Et.height=t.height;const i=Et.getContext("2d");t instanceof ImageData?i.putImageData(t,0,0):i.drawImage(t,0,0,t.width,t.height),e=Et}return e.width>2048||e.height>2048?(console.warn("THREE.ImageUtils.getDataURL: Image converted to jpg for performance reasons",t),e.toDataURL("image/jpeg",.6)):e.toDataURL("image/png")}static sRGBToLinear(t){if(typeof HTMLImageElement<"u"&&t instanceof HTMLImageElement||typeof HTMLCanvasElement<"u"&&t instanceof HTMLCanvasElement||typeof ImageBitmap<"u"&&t instanceof ImageBitmap){const e=Ti("canvas");e.width=t.width,e.height=t.height;const i=e.getContext("2d");i.drawImage(t,0,0,t.width,t.height);const s=i.getImageData(0,0,t.width,t.height),n=s.data;for(let r=0;r<n.length;r++)n[r]=Ye(n[r]/255)*255;return i.putImageData(s,0,0),e}else if(t.data){const e=t.data.slice(0);for(let i=0;i<e.length;i++)e instanceof Uint8Array||e instanceof Uint8ClampedArray?e[i]=Math.floor(Ye(e[i]/255)*255):e[i]=Ye(e[i]);return{data:e,width:t.width,height:t.height}}else return console.warn("THREE.ImageUtils.sRGBToLinear(): Unsupported image type. No color space conversion applied."),t}}let cs=0;class hs{constructor(t=null){this.isSource=!0,Object.defineProperty(this,"id",{value:cs++}),this.uuid=de(),this.data=t,this.dataReady=!0,this.version=0}set needsUpdate(t){t===!0&&this.version++}toJSON(t){const e=t===void 0||typeof t=="string";if(!e&&t.images[this.uuid]!==void 0)return t.images[this.uuid];const i={uuid:this.uuid,url:""},s=this.data;if(s!==null){let n;if(Array.isArray(s)){n=[];for(let r=0,a=s.length;r<a;r++)s[r].isDataTexture?n.push(Ze(s[r].image)):n.push(Ze(s[r]))}else n=Ze(s);i.url=n}return e||(t.images[this.uuid]=i),i}}function Ze(u){return typeof HTMLImageElement<"u"&&u instanceof HTMLImageElement||typeof HTMLCanvasElement<"u"&&u instanceof HTMLCanvasElement||typeof ImageBitmap<"u"&&u instanceof ImageBitmap?as.getDataURL(u):u.data?{data:Array.from(u.data),width:u.width,height:u.height,type:u.data.constructor.name}:(console.warn("THREE.Texture: Unable to serialize Texture."),{})}let ls=0;class gt extends Xe{constructor(t=gt.DEFAULT_IMAGE,e=gt.DEFAULT_MAPPING,i=1001,s=1001,n=1006,r=1008,a=1023,o=1009,h=gt.DEFAULT_ANISOTROPY,c=ns){super(),this.isTexture=!0,Object.defineProperty(this,"id",{value:ls++}),this.uuid=de(),this.name="",this.source=new hs(t),this.mipmaps=[],this.mapping=e,this.channel=0,this.wrapS=i,this.wrapT=s,this.magFilter=n,this.minFilter=r,this.anisotropy=h,this.format=a,this.internalFormat=null,this.type=o,this.offset=new v(0,0),this.repeat=new v(1,1),this.center=new v(0,0),this.rotation=0,this.matrixAutoUpdate=!0,this.matrix=new Tt,this.generateMipmaps=!0,this.premultiplyAlpha=!1,this.flipY=!0,this.unpackAlignment=4,this.colorSpace=c,this.userData={},this.version=0,this.onUpdate=null,this.isRenderTargetTexture=!1,this.pmremVersion=0}get image(){return this.source.data}set image(t=null){this.source.data=t}updateMatrix(){this.matrix.setUvTransform(this.offset.x,this.offset.y,this.repeat.x,this.repeat.y,this.rotation,this.center.x,this.center.y)}clone(){return new this.constructor().copy(this)}copy(t){return this.name=t.name,this.source=t.source,this.mipmaps=t.mipmaps.slice(0),this.mapping=t.mapping,this.channel=t.channel,this.wrapS=t.wrapS,this.wrapT=t.wrapT,this.magFilter=t.magFilter,this.minFilter=t.minFilter,this.anisotropy=t.anisotropy,this.format=t.format,this.internalFormat=t.internalFormat,this.type=t.type,this.offset.copy(t.offset),this.repeat.copy(t.repeat),this.center.copy(t.center),this.rotation=t.rotation,this.matrixAutoUpdate=t.matrixAutoUpdate,this.matrix.copy(t.matrix),this.generateMipmaps=t.generateMipmaps,this.premultiplyAlpha=t.premultiplyAlpha,this.flipY=t.flipY,this.unpackAlignment=t.unpackAlignment,this.colorSpace=t.colorSpace,this.userData=JSON.parse(JSON.stringify(t.userData)),this.needsUpdate=!0,this}toJSON(t){const e=t===void 0||typeof t=="string";if(!e&&t.textures[this.uuid]!==void 0)return t.textures[this.uuid];const i={metadata:{version:4.6,type:"Texture",generator:"Texture.toJSON"},uuid:this.uuid,name:this.name,image:this.source.toJSON(t).uuid,mapping:this.mapping,channel:this.channel,repeat:[this.repeat.x,this.repeat.y],offset:[this.offset.x,this.offset.y],center:[this.center.x,this.center.y],rotation:this.rotation,wrap:[this.wrapS,this.wrapT],format:this.format,internalFormat:this.internalFormat,type:this.type,colorSpace:this.colorSpace,minFilter:this.minFilter,magFilter:this.magFilter,anisotropy:this.anisotropy,flipY:this.flipY,generateMipmaps:this.generateMipmaps,premultiplyAlpha:this.premultiplyAlpha,unpackAlignment:this.unpackAlignment};return Object.keys(this.userData).length>0&&(i.userData=this.userData),e||(t.textures[this.uuid]=i),i}dispose(){this.dispatchEvent({type:"dispose"})}transformUv(t){if(this.mapping!==300)return t;if(t.applyMatrix3(this.matrix),t.x<0||t.x>1)switch(this.wrapS){case 1e3:t.x=t.x-Math.floor(t.x);break;case 1001:t.x=t.x<0?0:1;break;case 1002:Math.abs(Math.floor(t.x)%2)===1?t.x=Math.ceil(t.x)-t.x:t.x=t.x-Math.floor(t.x);break}if(t.y<0||t.y>1)switch(this.wrapT){case 1e3:t.y=t.y-Math.floor(t.y);break;case 1001:t.y=t.y<0?0:1;break;case 1002:Math.abs(Math.floor(t.y)%2)===1?t.y=Math.ceil(t.y)-t.y:t.y=t.y-Math.floor(t.y);break}return this.flipY&&(t.y=1-t.y),t}set needsUpdate(t){t===!0&&(this.version++,this.source.needsUpdate=!0)}set needsPMREMUpdate(t){t===!0&&this.pmremVersion++}}gt.DEFAULT_IMAGE=null,gt.DEFAULT_MAPPING=300,gt.DEFAULT_ANISOTROPY=1;class Kt{constructor(t=0,e=0,i=0,s=1){this.isQuaternion=!0,this._x=t,this._y=e,this._z=i,this._w=s}static slerpFlat(t,e,i,s,n,r,a){let o=i[s+0],h=i[s+1],c=i[s+2],f=i[s+3];const l=n[r+0],d=n[r+1],p=n[r+2],y=n[r+3];if(a===0){t[e+0]=o,t[e+1]=h,t[e+2]=c,t[e+3]=f;return}if(a===1){t[e+0]=l,t[e+1]=d,t[e+2]=p,t[e+3]=y;return}if(f!==y||o!==l||h!==d||c!==p){let x=1-a;const m=o*l+h*d+c*p+f*y,g=m>=0?1:-1,w=1-m*m;if(w>Number.EPSILON){const b=Math.sqrt(w),z=Math.atan2(b,m*g);x=Math.sin(x*z)/b,a=Math.sin(a*z)/b}const M=a*g;if(o=o*x+l*M,h=h*x+d*M,c=c*x+p*M,f=f*x+y*M,x===1-a){const b=1/Math.sqrt(o*o+h*h+c*c+f*f);o*=b,h*=b,c*=b,f*=b}}t[e]=o,t[e+1]=h,t[e+2]=c,t[e+3]=f}static multiplyQuaternionsFlat(t,e,i,s,n,r){const a=i[s],o=i[s+1],h=i[s+2],c=i[s+3],f=n[r],l=n[r+1],d=n[r+2],p=n[r+3];return t[e]=a*p+c*f+o*d-h*l,t[e+1]=o*p+c*l+h*f-a*d,t[e+2]=h*p+c*d+a*l-o*f,t[e+3]=c*p-a*f-o*l-h*d,t}get x(){return this._x}set x(t){this._x=t,this._onChangeCallback()}get y(){return this._y}set y(t){this._y=t,this._onChangeCallback()}get z(){return this._z}set z(t){this._z=t,this._onChangeCallback()}get w(){return this._w}set w(t){this._w=t,this._onChangeCallback()}set(t,e,i,s){return this._x=t,this._y=e,this._z=i,this._w=s,this._onChangeCallback(),this}clone(){return new this.constructor(this._x,this._y,this._z,this._w)}copy(t){return this._x=t.x,this._y=t.y,this._z=t.z,this._w=t.w,this._onChangeCallback(),this}setFromEuler(t,e=!0){const i=t._x,s=t._y,n=t._z,r=t._order,a=Math.cos,o=Math.sin,h=a(i/2),c=a(s/2),f=a(n/2),l=o(i/2),d=o(s/2),p=o(n/2);switch(r){case"XYZ":this._x=l*c*f+h*d*p,this._y=h*d*f-l*c*p,this._z=h*c*p+l*d*f,this._w=h*c*f-l*d*p;break;case"YXZ":this._x=l*c*f+h*d*p,this._y=h*d*f-l*c*p,this._z=h*c*p-l*d*f,this._w=h*c*f+l*d*p;break;case"ZXY":this._x=l*c*f-h*d*p,this._y=h*d*f+l*c*p,this._z=h*c*p+l*d*f,this._w=h*c*f-l*d*p;break;case"ZYX":this._x=l*c*f-h*d*p,this._y=h*d*f+l*c*p,this._z=h*c*p-l*d*f,this._w=h*c*f+l*d*p;break;case"YZX":this._x=l*c*f+h*d*p,this._y=h*d*f+l*c*p,this._z=h*c*p-l*d*f,this._w=h*c*f-l*d*p;break;case"XZY":this._x=l*c*f-h*d*p,this._y=h*d*f-l*c*p,this._z=h*c*p+l*d*f,this._w=h*c*f+l*d*p;break;default:console.warn("THREE.Quaternion: .setFromEuler() encountered an unknown order: "+r)}return e===!0&&this._onChangeCallback(),this}setFromAxisAngle(t,e){const i=e/2,s=Math.sin(i);return this._x=t.x*s,this._y=t.y*s,this._z=t.z*s,this._w=Math.cos(i),this._onChangeCallback(),this}setFromRotationMatrix(t){const e=t.elements,i=e[0],s=e[4],n=e[8],r=e[1],a=e[5],o=e[9],h=e[2],c=e[6],f=e[10],l=i+a+f;if(l>0){const d=.5/Math.sqrt(l+1);this._w=.25/d,this._x=(c-o)*d,this._y=(n-h)*d,this._z=(r-s)*d}else if(i>a&&i>f){const d=2*Math.sqrt(1+i-a-f);this._w=(c-o)/d,this._x=.25*d,this._y=(s+r)/d,this._z=(n+h)/d}else if(a>f){const d=2*Math.sqrt(1+a-i-f);this._w=(n-h)/d,this._x=(s+r)/d,this._y=.25*d,this._z=(o+c)/d}else{const d=2*Math.sqrt(1+f-i-a);this._w=(r-s)/d,this._x=(n+h)/d,this._y=(o+c)/d,this._z=.25*d}return this._onChangeCallback(),this}setFromUnitVectors(t,e){let i=t.dot(e)+1;return i<Number.EPSILON?(i=0,Math.abs(t.x)>Math.abs(t.z)?(this._x=-t.y,this._y=t.x,this._z=0,this._w=i):(this._x=0,this._y=-t.z,this._z=t.y,this._w=i)):(this._x=t.y*e.z-t.z*e.y,this._y=t.z*e.x-t.x*e.z,this._z=t.x*e.y-t.y*e.x,this._w=i),this.normalize()}angleTo(t){return 2*Math.acos(Math.abs(at(this.dot(t),-1,1)))}rotateTowards(t,e){const i=this.angleTo(t);if(i===0)return this;const s=Math.min(1,e/i);return this.slerp(t,s),this}identity(){return this.set(0,0,0,1)}invert(){return this.conjugate()}conjugate(){return this._x*=-1,this._y*=-1,this._z*=-1,this._onChangeCallback(),this}dot(t){return this._x*t._x+this._y*t._y+this._z*t._z+this._w*t._w}lengthSq(){return this._x*this._x+this._y*this._y+this._z*this._z+this._w*this._w}length(){return Math.sqrt(this._x*this._x+this._y*this._y+this._z*this._z+this._w*this._w)}normalize(){let t=this.length();return t===0?(this._x=0,this._y=0,this._z=0,this._w=1):(t=1/t,this._x=this._x*t,this._y=this._y*t,this._z=this._z*t,this._w=this._w*t),this._onChangeCallback(),this}multiply(t){return this.multiplyQuaternions(this,t)}premultiply(t){return this.multiplyQuaternions(t,this)}multiplyQuaternions(t,e){const i=t._x,s=t._y,n=t._z,r=t._w,a=e._x,o=e._y,h=e._z,c=e._w;return this._x=i*c+r*a+s*h-n*o,this._y=s*c+r*o+n*a-i*h,this._z=n*c+r*h+i*o-s*a,this._w=r*c-i*a-s*o-n*h,this._onChangeCallback(),this}slerp(t,e){if(e===0)return this;if(e===1)return this.copy(t);const i=this._x,s=this._y,n=this._z,r=this._w;let a=r*t._w+i*t._x+s*t._y+n*t._z;if(a<0?(this._w=-t._w,this._x=-t._x,this._y=-t._y,this._z=-t._z,a=-a):this.copy(t),a>=1)return this._w=r,this._x=i,this._y=s,this._z=n,this;const o=1-a*a;if(o<=Number.EPSILON){const d=1-e;return this._w=d*r+e*this._w,this._x=d*i+e*this._x,this._y=d*s+e*this._y,this._z=d*n+e*this._z,this.normalize(),this}const h=Math.sqrt(o),c=Math.atan2(h,a),f=Math.sin((1-e)*c)/h,l=Math.sin(e*c)/h;return this._w=r*f+this._w*l,this._x=i*f+this._x*l,this._y=s*f+this._y*l,this._z=n*f+this._z*l,this._onChangeCallback(),this}slerpQuaternions(t,e,i){return this.copy(t).slerp(e,i)}random(){const t=2*Math.PI*Math.random(),e=2*Math.PI*Math.random(),i=Math.random(),s=Math.sqrt(1-i),n=Math.sqrt(i);return this.set(s*Math.sin(t),s*Math.cos(t),n*Math.sin(e),n*Math.cos(e))}equals(t){return t._x===this._x&&t._y===this._y&&t._z===this._z&&t._w===this._w}fromArray(t,e=0){return this._x=t[e],this._y=t[e+1],this._z=t[e+2],this._w=t[e+3],this._onChangeCallback(),this}toArray(t=[],e=0){return t[e]=this._x,t[e+1]=this._y,t[e+2]=this._z,t[e+3]=this._w,t}fromBufferAttribute(t,e){return this._x=t.getX(e),this._y=t.getY(e),this._z=t.getZ(e),this._w=t.getW(e),this._onChangeCallback(),this}toJSON(){return this.toArray()}_onChange(t){return this._onChangeCallback=t,this}_onChangeCallback(){}*[Symbol.iterator](){yield this._x,yield this._y,yield this._z,yield this._w}}class A{constructor(t=0,e=0,i=0){A.prototype.isVector3=!0,this.x=t,this.y=e,this.z=i}set(t,e,i){return i===void 0&&(i=this.z),this.x=t,this.y=e,this.z=i,this}setScalar(t){return this.x=t,this.y=t,this.z=t,this}setX(t){return this.x=t,this}setY(t){return this.y=t,this}setZ(t){return this.z=t,this}setComponent(t,e){switch(t){case 0:this.x=e;break;case 1:this.y=e;break;case 2:this.z=e;break;default:throw new Error("index is out of range: "+t)}return this}getComponent(t){switch(t){case 0:return this.x;case 1:return this.y;case 2:return this.z;default:throw new Error("index is out of range: "+t)}}clone(){return new this.constructor(this.x,this.y,this.z)}copy(t){return this.x=t.x,this.y=t.y,this.z=t.z,this}add(t){return this.x+=t.x,this.y+=t.y,this.z+=t.z,this}addScalar(t){return this.x+=t,this.y+=t,this.z+=t,this}addVectors(t,e){return this.x=t.x+e.x,this.y=t.y+e.y,this.z=t.z+e.z,this}addScaledVector(t,e){return this.x+=t.x*e,this.y+=t.y*e,this.z+=t.z*e,this}sub(t){return this.x-=t.x,this.y-=t.y,this.z-=t.z,this}subScalar(t){return this.x-=t,this.y-=t,this.z-=t,this}subVectors(t,e){return this.x=t.x-e.x,this.y=t.y-e.y,this.z=t.z-e.z,this}multiply(t){return this.x*=t.x,this.y*=t.y,this.z*=t.z,this}multiplyScalar(t){return this.x*=t,this.y*=t,this.z*=t,this}multiplyVectors(t,e){return this.x=t.x*e.x,this.y=t.y*e.y,this.z=t.z*e.z,this}applyEuler(t){return this.applyQuaternion(Ci.setFromEuler(t))}applyAxisAngle(t,e){return this.applyQuaternion(Ci.setFromAxisAngle(t,e))}applyMatrix3(t){const e=this.x,i=this.y,s=this.z,n=t.elements;return this.x=n[0]*e+n[3]*i+n[6]*s,this.y=n[1]*e+n[4]*i+n[7]*s,this.z=n[2]*e+n[5]*i+n[8]*s,this}applyNormalMatrix(t){return this.applyMatrix3(t).normalize()}applyMatrix4(t){const e=this.x,i=this.y,s=this.z,n=t.elements,r=1/(n[3]*e+n[7]*i+n[11]*s+n[15]);return this.x=(n[0]*e+n[4]*i+n[8]*s+n[12])*r,this.y=(n[1]*e+n[5]*i+n[9]*s+n[13])*r,this.z=(n[2]*e+n[6]*i+n[10]*s+n[14])*r,this}applyQuaternion(t){const e=this.x,i=this.y,s=this.z,n=t.x,r=t.y,a=t.z,o=t.w,h=2*(r*s-a*i),c=2*(a*e-n*s),f=2*(n*i-r*e);return this.x=e+o*h+r*f-a*c,this.y=i+o*c+a*h-n*f,this.z=s+o*f+n*c-r*h,this}project(t){return this.applyMatrix4(t.matrixWorldInverse).applyMatrix4(t.projectionMatrix)}unproject(t){return this.applyMatrix4(t.projectionMatrixInverse).applyMatrix4(t.matrixWorld)}transformDirection(t){const e=this.x,i=this.y,s=this.z,n=t.elements;return this.x=n[0]*e+n[4]*i+n[8]*s,this.y=n[1]*e+n[5]*i+n[9]*s,this.z=n[2]*e+n[6]*i+n[10]*s,this.normalize()}divide(t){return this.x/=t.x,this.y/=t.y,this.z/=t.z,this}divideScalar(t){return this.multiplyScalar(1/t)}min(t){return this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this.z=Math.min(this.z,t.z),this}max(t){return this.x=Math.max(this.x,t.x),this.y=Math.max(this.y,t.y),this.z=Math.max(this.z,t.z),this}clamp(t,e){return this.x=Math.max(t.x,Math.min(e.x,this.x)),this.y=Math.max(t.y,Math.min(e.y,this.y)),this.z=Math.max(t.z,Math.min(e.z,this.z)),this}clampScalar(t,e){return this.x=Math.max(t,Math.min(e,this.x)),this.y=Math.max(t,Math.min(e,this.y)),this.z=Math.max(t,Math.min(e,this.z)),this}clampLength(t,e){const i=this.length();return this.divideScalar(i||1).multiplyScalar(Math.max(t,Math.min(e,i)))}floor(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this.z=Math.floor(this.z),this}ceil(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this.z=Math.ceil(this.z),this}round(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this.z=Math.round(this.z),this}roundToZero(){return this.x=Math.trunc(this.x),this.y=Math.trunc(this.y),this.z=Math.trunc(this.z),this}negate(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this}dot(t){return this.x*t.x+this.y*t.y+this.z*t.z}lengthSq(){return this.x*this.x+this.y*this.y+this.z*this.z}length(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z)}manhattanLength(){return Math.abs(this.x)+Math.abs(this.y)+Math.abs(this.z)}normalize(){return this.divideScalar(this.length()||1)}setLength(t){return this.normalize().multiplyScalar(t)}lerp(t,e){return this.x+=(t.x-this.x)*e,this.y+=(t.y-this.y)*e,this.z+=(t.z-this.z)*e,this}lerpVectors(t,e,i){return this.x=t.x+(e.x-t.x)*i,this.y=t.y+(e.y-t.y)*i,this.z=t.z+(e.z-t.z)*i,this}cross(t){return this.crossVectors(this,t)}crossVectors(t,e){const i=t.x,s=t.y,n=t.z,r=e.x,a=e.y,o=e.z;return this.x=s*o-n*a,this.y=n*r-i*o,this.z=i*a-s*r,this}projectOnVector(t){const e=t.lengthSq();if(e===0)return this.set(0,0,0);const i=t.dot(this)/e;return this.copy(t).multiplyScalar(i)}projectOnPlane(t){return $e.copy(this).projectOnVector(t),this.sub($e)}reflect(t){return this.sub($e.copy(t).multiplyScalar(2*this.dot(t)))}angleTo(t){const e=Math.sqrt(this.lengthSq()*t.lengthSq());if(e===0)return Math.PI/2;const i=this.dot(t)/e;return Math.acos(at(i,-1,1))}distanceTo(t){return Math.sqrt(this.distanceToSquared(t))}distanceToSquared(t){const e=this.x-t.x,i=this.y-t.y,s=this.z-t.z;return e*e+i*i+s*s}manhattanDistanceTo(t){return Math.abs(this.x-t.x)+Math.abs(this.y-t.y)+Math.abs(this.z-t.z)}setFromSpherical(t){return this.setFromSphericalCoords(t.radius,t.phi,t.theta)}setFromSphericalCoords(t,e,i){const s=Math.sin(e)*t;return this.x=s*Math.sin(i),this.y=Math.cos(e)*t,this.z=s*Math.cos(i),this}setFromCylindrical(t){return this.setFromCylindricalCoords(t.radius,t.theta,t.y)}setFromCylindricalCoords(t,e,i){return this.x=t*Math.sin(e),this.y=i,this.z=t*Math.cos(e),this}setFromMatrixPosition(t){const e=t.elements;return this.x=e[12],this.y=e[13],this.z=e[14],this}setFromMatrixScale(t){const e=this.setFromMatrixColumn(t,0).length(),i=this.setFromMatrixColumn(t,1).length(),s=this.setFromMatrixColumn(t,2).length();return this.x=e,this.y=i,this.z=s,this}setFromMatrixColumn(t,e){return this.fromArray(t.elements,e*4)}setFromMatrix3Column(t,e){return this.fromArray(t.elements,e*3)}setFromEuler(t){return this.x=t._x,this.y=t._y,this.z=t._z,this}setFromColor(t){return this.x=t.r,this.y=t.g,this.z=t.b,this}equals(t){return t.x===this.x&&t.y===this.y&&t.z===this.z}fromArray(t,e=0){return this.x=t[e],this.y=t[e+1],this.z=t[e+2],this}toArray(t=[],e=0){return t[e]=this.x,t[e+1]=this.y,t[e+2]=this.z,t}fromBufferAttribute(t,e){return this.x=t.getX(e),this.y=t.getY(e),this.z=t.getZ(e),this}random(){return this.x=Math.random(),this.y=Math.random(),this.z=Math.random(),this}randomDirection(){const t=Math.random()*Math.PI*2,e=Math.random()*2-1,i=Math.sqrt(1-e*e);return this.x=i*Math.cos(t),this.y=e,this.z=i*Math.sin(t),this}*[Symbol.iterator](){yield this.x,yield this.y,yield this.z}}const $e=new A,Ci=new Kt;class W{constructor(t=new A(1/0,1/0,1/0),e=new A(-1/0,-1/0,-1/0)){this.isBox3=!0,this.min=t,this.max=e}set(t,e){return this.min.copy(t),this.max.copy(e),this}setFromArray(t){this.makeEmpty();for(let e=0,i=t.length;e<i;e+=3)this.expandByPoint(et.fromArray(t,e));return this}setFromBufferAttribute(t){this.makeEmpty();for(let e=0,i=t.count;e<i;e++)this.expandByPoint(et.fromBufferAttribute(t,e));return this}setFromPoints(t){this.makeEmpty();for(let e=0,i=t.length;e<i;e++)this.expandByPoint(t[e]);return this}setFromCenterAndSize(t,e){const i=et.copy(e).multiplyScalar(.5);return this.min.copy(t).sub(i),this.max.copy(t).add(i),this}setFromObject(t,e=!1){return this.makeEmpty(),this.expandByObject(t,e)}clone(){return new this.constructor().copy(this)}copy(t){return this.min.copy(t.min),this.max.copy(t.max),this}makeEmpty(){return this.min.x=this.min.y=this.min.z=1/0,this.max.x=this.max.y=this.max.z=-1/0,this}isEmpty(){return this.max.x<this.min.x||this.max.y<this.min.y||this.max.z<this.min.z}getCenter(t){return this.isEmpty()?t.set(0,0,0):t.addVectors(this.min,this.max).multiplyScalar(.5)}getSize(t){return this.isEmpty()?t.set(0,0,0):t.subVectors(this.max,this.min)}expandByPoint(t){return this.min.min(t),this.max.max(t),this}expandByVector(t){return this.min.sub(t),this.max.add(t),this}expandByScalar(t){return this.min.addScalar(-t),this.max.addScalar(t),this}expandByObject(t,e=!1){t.updateWorldMatrix(!1,!1);const i=t.geometry;if(i!==void 0){const n=i.getAttribute("position");if(e===!0&&n!==void 0&&t.isInstancedMesh!==!0)for(let r=0,a=n.count;r<a;r++)t.isMesh===!0?t.getVertexPosition(r,et):et.fromBufferAttribute(n,r),et.applyMatrix4(t.matrixWorld),this.expandByPoint(et);else t.boundingBox!==void 0?(t.boundingBox===null&&t.computeBoundingBox(),pe.copy(t.boundingBox)):(i.boundingBox===null&&i.computeBoundingBox(),pe.copy(i.boundingBox)),pe.applyMatrix4(t.matrixWorld),this.union(pe)}const s=t.children;for(let n=0,r=s.length;n<r;n++)this.expandByObject(s[n],e);return this}containsPoint(t){return!(t.x<this.min.x||t.x>this.max.x||t.y<this.min.y||t.y>this.max.y||t.z<this.min.z||t.z>this.max.z)}containsBox(t){return this.min.x<=t.min.x&&t.max.x<=this.max.x&&this.min.y<=t.min.y&&t.max.y<=this.max.y&&this.min.z<=t.min.z&&t.max.z<=this.max.z}getParameter(t,e){return e.set((t.x-this.min.x)/(this.max.x-this.min.x),(t.y-this.min.y)/(this.max.y-this.min.y),(t.z-this.min.z)/(this.max.z-this.min.z))}intersectsBox(t){return!(t.max.x<this.min.x||t.min.x>this.max.x||t.max.y<this.min.y||t.min.y>this.max.y||t.max.z<this.min.z||t.min.z>this.max.z)}intersectsSphere(t){return this.clampPoint(t.center,et),et.distanceToSquared(t.center)<=t.radius*t.radius}intersectsPlane(t){let e,i;return t.normal.x>0?(e=t.normal.x*this.min.x,i=t.normal.x*this.max.x):(e=t.normal.x*this.max.x,i=t.normal.x*this.min.x),t.normal.y>0?(e+=t.normal.y*this.min.y,i+=t.normal.y*this.max.y):(e+=t.normal.y*this.max.y,i+=t.normal.y*this.min.y),t.normal.z>0?(e+=t.normal.z*this.min.z,i+=t.normal.z*this.max.z):(e+=t.normal.z*this.max.z,i+=t.normal.z*this.min.z),e<=-t.constant&&i>=-t.constant}intersectsTriangle(t){if(this.isEmpty())return!1;this.getCenter(te),me.subVectors(this.max,te),Ft.subVectors(t.a,te),Ut.subVectors(t.b,te),Rt.subVectors(t.c,te),bt.subVectors(Ut,Ft),wt.subVectors(Rt,Ut),Pt.subVectors(Ft,Rt);let e=[0,-bt.z,bt.y,0,-wt.z,wt.y,0,-Pt.z,Pt.y,bt.z,0,-bt.x,wt.z,0,-wt.x,Pt.z,0,-Pt.x,-bt.y,bt.x,0,-wt.y,wt.x,0,-Pt.y,Pt.x,0];return!He(e,Ft,Ut,Rt,me)||(e=[1,0,0,0,1,0,0,0,1],!He(e,Ft,Ut,Rt,me))?!1:(ye.crossVectors(bt,wt),e=[ye.x,ye.y,ye.z],He(e,Ft,Ut,Rt,me))}clampPoint(t,e){return e.copy(t).clamp(this.min,this.max)}distanceToPoint(t){return this.clampPoint(t,et).distanceTo(t)}getBoundingSphere(t){return this.isEmpty()?t.makeEmpty():(this.getCenter(t.center),t.radius=this.getSize(et).length()*.5),t}intersect(t){return this.min.max(t.min),this.max.min(t.max),this.isEmpty()&&this.makeEmpty(),this}union(t){return this.min.min(t.min),this.max.max(t.max),this}applyMatrix4(t){return this.isEmpty()?this:(ut[0].set(this.min.x,this.min.y,this.min.z).applyMatrix4(t),ut[1].set(this.min.x,this.min.y,this.max.z).applyMatrix4(t),ut[2].set(this.min.x,this.max.y,this.min.z).applyMatrix4(t),ut[3].set(this.min.x,this.max.y,this.max.z).applyMatrix4(t),ut[4].set(this.max.x,this.min.y,this.min.z).applyMatrix4(t),ut[5].set(this.max.x,this.min.y,this.max.z).applyMatrix4(t),ut[6].set(this.max.x,this.max.y,this.min.z).applyMatrix4(t),ut[7].set(this.max.x,this.max.y,this.max.z).applyMatrix4(t),this.setFromPoints(ut),this)}translate(t){return this.min.add(t),this.max.add(t),this}equals(t){return t.min.equals(this.min)&&t.max.equals(this.max)}}const ut=[new A,new A,new A,new A,new A,new A,new A,new A],et=new A,pe=new W,Ft=new A,Ut=new A,Rt=new A,bt=new A,wt=new A,Pt=new A,te=new A,me=new A,ye=new A,Ct=new A;function He(u,t,e,i,s){for(let n=0,r=u.length-3;n<=r;n+=3){Ct.fromArray(u,n);const a=s.x*Math.abs(Ct.x)+s.y*Math.abs(Ct.y)+s.z*Math.abs(Ct.z),o=t.dot(Ct),h=e.dot(Ct),c=i.dot(Ct);if(Math.max(-Math.max(o,h,c),Math.min(o,h,c))>a)return!1}return!0}const us=new W,ee=new A,je=new A;class Ei{constructor(t=new A,e=-1){this.isSphere=!0,this.center=t,this.radius=e}set(t,e){return this.center.copy(t),this.radius=e,this}setFromPoints(t,e){const i=this.center;e!==void 0?i.copy(e):us.setFromPoints(t).getCenter(i);let s=0;for(let n=0,r=t.length;n<r;n++)s=Math.max(s,i.distanceToSquared(t[n]));return this.radius=Math.sqrt(s),this}copy(t){return this.center.copy(t.center),this.radius=t.radius,this}isEmpty(){return this.radius<0}makeEmpty(){return this.center.set(0,0,0),this.radius=-1,this}containsPoint(t){return t.distanceToSquared(this.center)<=this.radius*this.radius}distanceToPoint(t){return t.distanceTo(this.center)-this.radius}intersectsSphere(t){const e=this.radius+t.radius;return t.center.distanceToSquared(this.center)<=e*e}intersectsBox(t){return t.intersectsSphere(this)}intersectsPlane(t){return Math.abs(t.distanceToPoint(this.center))<=this.radius}clampPoint(t,e){const i=this.center.distanceToSquared(t);return e.copy(t),i>this.radius*this.radius&&(e.sub(this.center).normalize(),e.multiplyScalar(this.radius).add(this.center)),e}getBoundingBox(t){return this.isEmpty()?(t.makeEmpty(),t):(t.set(this.center,this.center),t.expandByScalar(this.radius),t)}applyMatrix4(t){return this.center.applyMatrix4(t),this.radius=this.radius*t.getMaxScaleOnAxis(),this}translate(t){return this.center.add(t),this}expandByPoint(t){if(this.isEmpty())return this.center.copy(t),this.radius=0,this;ee.subVectors(t,this.center);const e=ee.lengthSq();if(e>this.radius*this.radius){const i=Math.sqrt(e),s=(i-this.radius)*.5;this.center.addScaledVector(ee,s/i),this.radius+=s}return this}union(t){return t.isEmpty()?this:this.isEmpty()?(this.copy(t),this):(this.center.equals(t.center)===!0?this.radius=Math.max(this.radius,t.radius):(je.subVectors(t.center,this.center).setLength(t.radius),this.expandByPoint(ee.copy(t.center).add(je)),this.expandByPoint(ee.copy(t.center).sub(je))),this)}equals(t){return t.center.equals(this.center)&&t.radius===this.radius}clone(){return new this.constructor().copy(this)}}class N{constructor(t,e,i,s,n,r,a,o,h,c,f,l,d,p,y,x){N.prototype.isMatrix4=!0,this.elements=[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1],t!==void 0&&this.set(t,e,i,s,n,r,a,o,h,c,f,l,d,p,y,x)}set(t,e,i,s,n,r,a,o,h,c,f,l,d,p,y,x){const m=this.elements;return m[0]=t,m[4]=e,m[8]=i,m[12]=s,m[1]=n,m[5]=r,m[9]=a,m[13]=o,m[2]=h,m[6]=c,m[10]=f,m[14]=l,m[3]=d,m[7]=p,m[11]=y,m[15]=x,this}identity(){return this.set(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1),this}clone(){return new N().fromArray(this.elements)}copy(t){const e=this.elements,i=t.elements;return e[0]=i[0],e[1]=i[1],e[2]=i[2],e[3]=i[3],e[4]=i[4],e[5]=i[5],e[6]=i[6],e[7]=i[7],e[8]=i[8],e[9]=i[9],e[10]=i[10],e[11]=i[11],e[12]=i[12],e[13]=i[13],e[14]=i[14],e[15]=i[15],this}copyPosition(t){const e=this.elements,i=t.elements;return e[12]=i[12],e[13]=i[13],e[14]=i[14],this}setFromMatrix3(t){const e=t.elements;return this.set(e[0],e[3],e[6],0,e[1],e[4],e[7],0,e[2],e[5],e[8],0,0,0,0,1),this}extractBasis(t,e,i){return t.setFromMatrixColumn(this,0),e.setFromMatrixColumn(this,1),i.setFromMatrixColumn(this,2),this}makeBasis(t,e,i){return this.set(t.x,e.x,i.x,0,t.y,e.y,i.y,0,t.z,e.z,i.z,0,0,0,0,1),this}extractRotation(t){const e=this.elements,i=t.elements,s=1/kt.setFromMatrixColumn(t,0).length(),n=1/kt.setFromMatrixColumn(t,1).length(),r=1/kt.setFromMatrixColumn(t,2).length();return e[0]=i[0]*s,e[1]=i[1]*s,e[2]=i[2]*s,e[3]=0,e[4]=i[4]*n,e[5]=i[5]*n,e[6]=i[6]*n,e[7]=0,e[8]=i[8]*r,e[9]=i[9]*r,e[10]=i[10]*r,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,this}makeRotationFromEuler(t){const e=this.elements,i=t.x,s=t.y,n=t.z,r=Math.cos(i),a=Math.sin(i),o=Math.cos(s),h=Math.sin(s),c=Math.cos(n),f=Math.sin(n);if(t.order==="XYZ"){const l=r*c,d=r*f,p=a*c,y=a*f;e[0]=o*c,e[4]=-o*f,e[8]=h,e[1]=d+p*h,e[5]=l-y*h,e[9]=-a*o,e[2]=y-l*h,e[6]=p+d*h,e[10]=r*o}else if(t.order==="YXZ"){const l=o*c,d=o*f,p=h*c,y=h*f;e[0]=l+y*a,e[4]=p*a-d,e[8]=r*h,e[1]=r*f,e[5]=r*c,e[9]=-a,e[2]=d*a-p,e[6]=y+l*a,e[10]=r*o}else if(t.order==="ZXY"){const l=o*c,d=o*f,p=h*c,y=h*f;e[0]=l-y*a,e[4]=-r*f,e[8]=p+d*a,e[1]=d+p*a,e[5]=r*c,e[9]=y-l*a,e[2]=-r*h,e[6]=a,e[10]=r*o}else if(t.order==="ZYX"){const l=r*c,d=r*f,p=a*c,y=a*f;e[0]=o*c,e[4]=p*h-d,e[8]=l*h+y,e[1]=o*f,e[5]=y*h+l,e[9]=d*h-p,e[2]=-h,e[6]=a*o,e[10]=r*o}else if(t.order==="YZX"){const l=r*o,d=r*h,p=a*o,y=a*h;e[0]=o*c,e[4]=y-l*f,e[8]=p*f+d,e[1]=f,e[5]=r*c,e[9]=-a*c,e[2]=-h*c,e[6]=d*f+p,e[10]=l-y*f}else if(t.order==="XZY"){const l=r*o,d=r*h,p=a*o,y=a*h;e[0]=o*c,e[4]=-f,e[8]=h*c,e[1]=l*f+y,e[5]=r*c,e[9]=d*f-p,e[2]=p*f-d,e[6]=a*c,e[10]=y*f+l}return e[3]=0,e[7]=0,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,this}makeRotationFromQuaternion(t){return this.compose(fs,t,ds)}lookAt(t,e,i){const s=this.elements;return Z.subVectors(t,e),Z.lengthSq()===0&&(Z.z=1),Z.normalize(),_t.crossVectors(i,Z),_t.lengthSq()===0&&(Math.abs(i.z)===1?Z.x+=1e-4:Z.z+=1e-4,Z.normalize(),_t.crossVectors(i,Z)),_t.normalize(),xe.crossVectors(Z,_t),s[0]=_t.x,s[4]=xe.x,s[8]=Z.x,s[1]=_t.y,s[5]=xe.y,s[9]=Z.y,s[2]=_t.z,s[6]=xe.z,s[10]=Z.z,this}multiply(t){return this.multiplyMatrices(this,t)}premultiply(t){return this.multiplyMatrices(t,this)}multiplyMatrices(t,e){const i=t.elements,s=e.elements,n=this.elements,r=i[0],a=i[4],o=i[8],h=i[12],c=i[1],f=i[5],l=i[9],d=i[13],p=i[2],y=i[6],x=i[10],m=i[14],g=i[3],w=i[7],M=i[11],b=i[15],z=s[0],_=s[4],S=s[8],B=s[12],T=s[1],C=s[5],P=s[9],E=s[13],F=s[2],U=s[6],R=s[10],j=s[14],ht=s[3],lt=s[7],ve=s[11],We=s[15];return n[0]=r*z+a*T+o*F+h*ht,n[4]=r*_+a*C+o*U+h*lt,n[8]=r*S+a*P+o*R+h*ve,n[12]=r*B+a*E+o*j+h*We,n[1]=c*z+f*T+l*F+d*ht,n[5]=c*_+f*C+l*U+d*lt,n[9]=c*S+f*P+l*R+d*ve,n[13]=c*B+f*E+l*j+d*We,n[2]=p*z+y*T+x*F+m*ht,n[6]=p*_+y*C+x*U+m*lt,n[10]=p*S+y*P+x*R+m*ve,n[14]=p*B+y*E+x*j+m*We,n[3]=g*z+w*T+M*F+b*ht,n[7]=g*_+w*C+M*U+b*lt,n[11]=g*S+w*P+M*R+b*ve,n[15]=g*B+w*E+M*j+b*We,this}multiplyScalar(t){const e=this.elements;return e[0]*=t,e[4]*=t,e[8]*=t,e[12]*=t,e[1]*=t,e[5]*=t,e[9]*=t,e[13]*=t,e[2]*=t,e[6]*=t,e[10]*=t,e[14]*=t,e[3]*=t,e[7]*=t,e[11]*=t,e[15]*=t,this}determinant(){const t=this.elements,e=t[0],i=t[4],s=t[8],n=t[12],r=t[1],a=t[5],o=t[9],h=t[13],c=t[2],f=t[6],l=t[10],d=t[14],p=t[3],y=t[7],x=t[11],m=t[15];return p*(+n*o*f-s*h*f-n*a*l+i*h*l+s*a*d-i*o*d)+y*(+e*o*d-e*h*l+n*r*l-s*r*d+s*h*c-n*o*c)+x*(+e*h*f-e*a*d-n*r*f+i*r*d+n*a*c-i*h*c)+m*(-s*a*c-e*o*f+e*a*l+s*r*f-i*r*l+i*o*c)}transpose(){const t=this.elements;let e;return e=t[1],t[1]=t[4],t[4]=e,e=t[2],t[2]=t[8],t[8]=e,e=t[6],t[6]=t[9],t[9]=e,e=t[3],t[3]=t[12],t[12]=e,e=t[7],t[7]=t[13],t[13]=e,e=t[11],t[11]=t[14],t[14]=e,this}setPosition(t,e,i){const s=this.elements;return t.isVector3?(s[12]=t.x,s[13]=t.y,s[14]=t.z):(s[12]=t,s[13]=e,s[14]=i),this}invert(){const t=this.elements,e=t[0],i=t[1],s=t[2],n=t[3],r=t[4],a=t[5],o=t[6],h=t[7],c=t[8],f=t[9],l=t[10],d=t[11],p=t[12],y=t[13],x=t[14],m=t[15],g=f*x*h-y*l*h+y*o*d-a*x*d-f*o*m+a*l*m,w=p*l*h-c*x*h-p*o*d+r*x*d+c*o*m-r*l*m,M=c*y*h-p*f*h+p*a*d-r*y*d-c*a*m+r*f*m,b=p*f*o-c*y*o-p*a*l+r*y*l+c*a*x-r*f*x,z=e*g+i*w+s*M+n*b;if(z===0)return this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0);const _=1/z;return t[0]=g*_,t[1]=(y*l*n-f*x*n-y*s*d+i*x*d+f*s*m-i*l*m)*_,t[2]=(a*x*n-y*o*n+y*s*h-i*x*h-a*s*m+i*o*m)*_,t[3]=(f*o*n-a*l*n-f*s*h+i*l*h+a*s*d-i*o*d)*_,t[4]=w*_,t[5]=(c*x*n-p*l*n+p*s*d-e*x*d-c*s*m+e*l*m)*_,t[6]=(p*o*n-r*x*n-p*s*h+e*x*h+r*s*m-e*o*m)*_,t[7]=(r*l*n-c*o*n+c*s*h-e*l*h-r*s*d+e*o*d)*_,t[8]=M*_,t[9]=(p*f*n-c*y*n-p*i*d+e*y*d+c*i*m-e*f*m)*_,t[10]=(r*y*n-p*a*n+p*i*h-e*y*h-r*i*m+e*a*m)*_,t[11]=(c*a*n-r*f*n-c*i*h+e*f*h+r*i*d-e*a*d)*_,t[12]=b*_,t[13]=(c*y*s-p*f*s+p*i*l-e*y*l-c*i*x+e*f*x)*_,t[14]=(p*a*s-r*y*s-p*i*o+e*y*o+r*i*x-e*a*x)*_,t[15]=(r*f*s-c*a*s+c*i*o-e*f*o-r*i*l+e*a*l)*_,this}scale(t){const e=this.elements,i=t.x,s=t.y,n=t.z;return e[0]*=i,e[4]*=s,e[8]*=n,e[1]*=i,e[5]*=s,e[9]*=n,e[2]*=i,e[6]*=s,e[10]*=n,e[3]*=i,e[7]*=s,e[11]*=n,this}getMaxScaleOnAxis(){const t=this.elements,e=t[0]*t[0]+t[1]*t[1]+t[2]*t[2],i=t[4]*t[4]+t[5]*t[5]+t[6]*t[6],s=t[8]*t[8]+t[9]*t[9]+t[10]*t[10];return Math.sqrt(Math.max(e,i,s))}makeTranslation(t,e,i){return t.isVector3?this.set(1,0,0,t.x,0,1,0,t.y,0,0,1,t.z,0,0,0,1):this.set(1,0,0,t,0,1,0,e,0,0,1,i,0,0,0,1),this}makeRotationX(t){const e=Math.cos(t),i=Math.sin(t);return this.set(1,0,0,0,0,e,-i,0,0,i,e,0,0,0,0,1),this}makeRotationY(t){const e=Math.cos(t),i=Math.sin(t);return this.set(e,0,i,0,0,1,0,0,-i,0,e,0,0,0,0,1),this}makeRotationZ(t){const e=Math.cos(t),i=Math.sin(t);return this.set(e,-i,0,0,i,e,0,0,0,0,1,0,0,0,0,1),this}makeRotationAxis(t,e){const i=Math.cos(e),s=Math.sin(e),n=1-i,r=t.x,a=t.y,o=t.z,h=n*r,c=n*a;return this.set(h*r+i,h*a-s*o,h*o+s*a,0,h*a+s*o,c*a+i,c*o-s*r,0,h*o-s*a,c*o+s*r,n*o*o+i,0,0,0,0,1),this}makeScale(t,e,i){return this.set(t,0,0,0,0,e,0,0,0,0,i,0,0,0,0,1),this}makeShear(t,e,i,s,n,r){return this.set(1,i,n,0,t,1,r,0,e,s,1,0,0,0,0,1),this}compose(t,e,i){const s=this.elements,n=e._x,r=e._y,a=e._z,o=e._w,h=n+n,c=r+r,f=a+a,l=n*h,d=n*c,p=n*f,y=r*c,x=r*f,m=a*f,g=o*h,w=o*c,M=o*f,b=i.x,z=i.y,_=i.z;return s[0]=(1-(y+m))*b,s[1]=(d+M)*b,s[2]=(p-w)*b,s[3]=0,s[4]=(d-M)*z,s[5]=(1-(l+m))*z,s[6]=(x+g)*z,s[7]=0,s[8]=(p+w)*_,s[9]=(x-g)*_,s[10]=(1-(l+y))*_,s[11]=0,s[12]=t.x,s[13]=t.y,s[14]=t.z,s[15]=1,this}decompose(t,e,i){const s=this.elements;let n=kt.set(s[0],s[1],s[2]).length();const r=kt.set(s[4],s[5],s[6]).length(),a=kt.set(s[8],s[9],s[10]).length();this.determinant()<0&&(n=-n),t.x=s[12],t.y=s[13],t.z=s[14],it.copy(this);const h=1/n,c=1/r,f=1/a;return it.elements[0]*=h,it.elements[1]*=h,it.elements[2]*=h,it.elements[4]*=c,it.elements[5]*=c,it.elements[6]*=c,it.elements[8]*=f,it.elements[9]*=f,it.elements[10]*=f,e.setFromRotationMatrix(it),i.x=n,i.y=r,i.z=a,this}makePerspective(t,e,i,s,n,r,a=2e3){const o=this.elements,h=2*n/(e-t),c=2*n/(i-s),f=(e+t)/(e-t),l=(i+s)/(i-s);let d,p;if(a===2e3)d=-(r+n)/(r-n),p=-2*r*n/(r-n);else if(a===2001)d=-r/(r-n),p=-r*n/(r-n);else throw new Error("THREE.Matrix4.makePerspective(): Invalid coordinate system: "+a);return o[0]=h,o[4]=0,o[8]=f,o[12]=0,o[1]=0,o[5]=c,o[9]=l,o[13]=0,o[2]=0,o[6]=0,o[10]=d,o[14]=p,o[3]=0,o[7]=0,o[11]=-1,o[15]=0,this}makeOrthographic(t,e,i,s,n,r,a=2e3){const o=this.elements,h=1/(e-t),c=1/(i-s),f=1/(r-n),l=(e+t)*h,d=(i+s)*c;let p,y;if(a===2e3)p=(r+n)*f,y=-2*f;else if(a===2001)p=n*f,y=-1*f;else throw new Error("THREE.Matrix4.makeOrthographic(): Invalid coordinate system: "+a);return o[0]=2*h,o[4]=0,o[8]=0,o[12]=-l,o[1]=0,o[5]=2*c,o[9]=0,o[13]=-d,o[2]=0,o[6]=0,o[10]=y,o[14]=-p,o[3]=0,o[7]=0,o[11]=0,o[15]=1,this}equals(t){const e=this.elements,i=t.elements;for(let s=0;s<16;s++)if(e[s]!==i[s])return!1;return!0}fromArray(t,e=0){for(let i=0;i<16;i++)this.elements[i]=t[i+e];return this}toArray(t=[],e=0){const i=this.elements;return t[e]=i[0],t[e+1]=i[1],t[e+2]=i[2],t[e+3]=i[3],t[e+4]=i[4],t[e+5]=i[5],t[e+6]=i[6],t[e+7]=i[7],t[e+8]=i[8],t[e+9]=i[9],t[e+10]=i[10],t[e+11]=i[11],t[e+12]=i[12],t[e+13]=i[13],t[e+14]=i[14],t[e+15]=i[15],t}}const kt=new A,it=new N,fs=new A(0,0,0),ds=new A(1,1,1),_t=new A,xe=new A,Z=new A,Fi=new N,Ui=new Kt;class ge{constructor(t=0,e=0,i=0,s=ge.DEFAULT_ORDER){this.isEuler=!0,this._x=t,this._y=e,this._z=i,this._order=s}get x(){return this._x}set x(t){this._x=t,this._onChangeCallback()}get y(){return this._y}set y(t){this._y=t,this._onChangeCallback()}get z(){return this._z}set z(t){this._z=t,this._onChangeCallback()}get order(){return this._order}set order(t){this._order=t,this._onChangeCallback()}set(t,e,i,s=this._order){return this._x=t,this._y=e,this._z=i,this._order=s,this._onChangeCallback(),this}clone(){return new this.constructor(this._x,this._y,this._z,this._order)}copy(t){return this._x=t._x,this._y=t._y,this._z=t._z,this._order=t._order,this._onChangeCallback(),this}setFromRotationMatrix(t,e=this._order,i=!0){const s=t.elements,n=s[0],r=s[4],a=s[8],o=s[1],h=s[5],c=s[9],f=s[2],l=s[6],d=s[10];switch(e){case"XYZ":this._y=Math.asin(at(a,-1,1)),Math.abs(a)<.9999999?(this._x=Math.atan2(-c,d),this._z=Math.atan2(-r,n)):(this._x=Math.atan2(l,h),this._z=0);break;case"YXZ":this._x=Math.asin(-at(c,-1,1)),Math.abs(c)<.9999999?(this._y=Math.atan2(a,d),this._z=Math.atan2(o,h)):(this._y=Math.atan2(-f,n),this._z=0);break;case"ZXY":this._x=Math.asin(at(l,-1,1)),Math.abs(l)<.9999999?(this._y=Math.atan2(-f,d),this._z=Math.atan2(-r,h)):(this._y=0,this._z=Math.atan2(o,n));break;case"ZYX":this._y=Math.asin(-at(f,-1,1)),Math.abs(f)<.9999999?(this._x=Math.atan2(l,d),this._z=Math.atan2(o,n)):(this._x=0,this._z=Math.atan2(-r,h));break;case"YZX":this._z=Math.asin(at(o,-1,1)),Math.abs(o)<.9999999?(this._x=Math.atan2(-c,h),this._y=Math.atan2(-f,n)):(this._x=0,this._y=Math.atan2(a,d));break;case"XZY":this._z=Math.asin(-at(r,-1,1)),Math.abs(r)<.9999999?(this._x=Math.atan2(l,h),this._y=Math.atan2(a,n)):(this._x=Math.atan2(-c,d),this._y=0);break;default:console.warn("THREE.Euler: .setFromRotationMatrix() encountered an unknown order: "+e)}return this._order=e,i===!0&&this._onChangeCallback(),this}setFromQuaternion(t,e,i){return Fi.makeRotationFromQuaternion(t),this.setFromRotationMatrix(Fi,e,i)}setFromVector3(t,e=this._order){return this.set(t.x,t.y,t.z,e)}reorder(t){return Ui.setFromEuler(this),this.setFromQuaternion(Ui,t)}equals(t){return t._x===this._x&&t._y===this._y&&t._z===this._z&&t._order===this._order}fromArray(t){return this._x=t[0],this._y=t[1],this._z=t[2],t[3]!==void 0&&(this._order=t[3]),this._onChangeCallback(),this}toArray(t=[],e=0){return t[e]=this._x,t[e+1]=this._y,t[e+2]=this._z,t[e+3]=this._order,t}_onChange(t){return this._onChangeCallback=t,this}_onChangeCallback(){}*[Symbol.iterator](){yield this._x,yield this._y,yield this._z,yield this._order}}ge.DEFAULT_ORDER="XYZ";class ps{constructor(){this.mask=1}set(t){this.mask=(1<<t|0)>>>0}enable(t){this.mask|=1<<t|0}enableAll(){this.mask=-1}toggle(t){this.mask^=1<<t|0}disable(t){this.mask&=~(1<<t|0)}disableAll(){this.mask=0}test(t){return(this.mask&t.mask)!==0}isEnabled(t){return(this.mask&(1<<t|0))!==0}}let ms=0;const Ri=new A,Lt=new Kt,ft=new N,be=new A,ie=new A,ys=new A,xs=new Kt,ki=new A(1,0,0),Li=new A(0,1,0),Ii=new A(0,0,1),Di={type:"added"},gs={type:"removed"},It={type:"childadded",child:null},Ge={type:"childremoved",child:null};class Mt extends Xe{constructor(){super(),this.isObject3D=!0,Object.defineProperty(this,"id",{value:ms++}),this.uuid=de(),this.name="",this.type="Object3D",this.parent=null,this.children=[],this.up=Mt.DEFAULT_UP.clone();const t=new A,e=new ge,i=new Kt,s=new A(1,1,1);function n(){i.setFromEuler(e,!1)}function r(){e.setFromQuaternion(i,void 0,!1)}e._onChange(n),i._onChange(r),Object.defineProperties(this,{position:{configurable:!0,enumerable:!0,value:t},rotation:{configurable:!0,enumerable:!0,value:e},quaternion:{configurable:!0,enumerable:!0,value:i},scale:{configurable:!0,enumerable:!0,value:s},modelViewMatrix:{value:new N},normalMatrix:{value:new Tt}}),this.matrix=new N,this.matrixWorld=new N,this.matrixAutoUpdate=Mt.DEFAULT_MATRIX_AUTO_UPDATE,this.matrixWorldAutoUpdate=Mt.DEFAULT_MATRIX_WORLD_AUTO_UPDATE,this.matrixWorldNeedsUpdate=!1,this.layers=new ps,this.visible=!0,this.castShadow=!1,this.receiveShadow=!1,this.frustumCulled=!0,this.renderOrder=0,this.animations=[],this.userData={}}onBeforeShadow(){}onAfterShadow(){}onBeforeRender(){}onAfterRender(){}applyMatrix4(t){this.matrixAutoUpdate&&this.updateMatrix(),this.matrix.premultiply(t),this.matrix.decompose(this.position,this.quaternion,this.scale)}applyQuaternion(t){return this.quaternion.premultiply(t),this}setRotationFromAxisAngle(t,e){this.quaternion.setFromAxisAngle(t,e)}setRotationFromEuler(t){this.quaternion.setFromEuler(t,!0)}setRotationFromMatrix(t){this.quaternion.setFromRotationMatrix(t)}setRotationFromQuaternion(t){this.quaternion.copy(t)}rotateOnAxis(t,e){return Lt.setFromAxisAngle(t,e),this.quaternion.multiply(Lt),this}rotateOnWorldAxis(t,e){return Lt.setFromAxisAngle(t,e),this.quaternion.premultiply(Lt),this}rotateX(t){return this.rotateOnAxis(ki,t)}rotateY(t){return this.rotateOnAxis(Li,t)}rotateZ(t){return this.rotateOnAxis(Ii,t)}translateOnAxis(t,e){return Ri.copy(t).applyQuaternion(this.quaternion),this.position.add(Ri.multiplyScalar(e)),this}translateX(t){return this.translateOnAxis(ki,t)}translateY(t){return this.translateOnAxis(Li,t)}translateZ(t){return this.translateOnAxis(Ii,t)}localToWorld(t){return this.updateWorldMatrix(!0,!1),t.applyMatrix4(this.matrixWorld)}worldToLocal(t){return this.updateWorldMatrix(!0,!1),t.applyMatrix4(ft.copy(this.matrixWorld).invert())}lookAt(t,e,i){t.isVector3?be.copy(t):be.set(t,e,i);const s=this.parent;this.updateWorldMatrix(!0,!1),ie.setFromMatrixPosition(this.matrixWorld),this.isCamera||this.isLight?ft.lookAt(ie,be,this.up):ft.lookAt(be,ie,this.up),this.quaternion.setFromRotationMatrix(ft),s&&(ft.extractRotation(s.matrixWorld),Lt.setFromRotationMatrix(ft),this.quaternion.premultiply(Lt.invert()))}add(t){if(arguments.length>1){for(let e=0;e<arguments.length;e++)this.add(arguments[e]);return this}return t===this?(console.error("THREE.Object3D.add: object can't be added as a child of itself.",t),this):(t&&t.isObject3D?(t.removeFromParent(),t.parent=this,this.children.push(t),t.dispatchEvent(Di),It.child=t,this.dispatchEvent(It),It.child=null):console.error("THREE.Object3D.add: object not an instance of THREE.Object3D.",t),this)}remove(t){if(arguments.length>1){for(let i=0;i<arguments.length;i++)this.remove(arguments[i]);return this}const e=this.children.indexOf(t);return e!==-1&&(t.parent=null,this.children.splice(e,1),t.dispatchEvent(gs),Ge.child=t,this.dispatchEvent(Ge),Ge.child=null),this}removeFromParent(){const t=this.parent;return t!==null&&t.remove(this),this}clear(){return this.remove(...this.children)}attach(t){return this.updateWorldMatrix(!0,!1),ft.copy(this.matrixWorld).invert(),t.parent!==null&&(t.parent.updateWorldMatrix(!0,!1),ft.multiply(t.parent.matrixWorld)),t.applyMatrix4(ft),t.removeFromParent(),t.parent=this,this.children.push(t),t.updateWorldMatrix(!1,!0),t.dispatchEvent(Di),It.child=t,this.dispatchEvent(It),It.child=null,this}getObjectById(t){return this.getObjectByProperty("id",t)}getObjectByName(t){return this.getObjectByProperty("name",t)}getObjectByProperty(t,e){if(this[t]===e)return this;for(let i=0,s=this.children.length;i<s;i++){const r=this.children[i].getObjectByProperty(t,e);if(r!==void 0)return r}}getObjectsByProperty(t,e,i=[]){this[t]===e&&i.push(this);const s=this.children;for(let n=0,r=s.length;n<r;n++)s[n].getObjectsByProperty(t,e,i);return i}getWorldPosition(t){return this.updateWorldMatrix(!0,!1),t.setFromMatrixPosition(this.matrixWorld)}getWorldQuaternion(t){return this.updateWorldMatrix(!0,!1),this.matrixWorld.decompose(ie,t,ys),t}getWorldScale(t){return this.updateWorldMatrix(!0,!1),this.matrixWorld.decompose(ie,xs,t),t}getWorldDirection(t){this.updateWorldMatrix(!0,!1);const e=this.matrixWorld.elements;return t.set(e[8],e[9],e[10]).normalize()}raycast(){}traverse(t){t(this);const e=this.children;for(let i=0,s=e.length;i<s;i++)e[i].traverse(t)}traverseVisible(t){if(this.visible===!1)return;t(this);const e=this.children;for(let i=0,s=e.length;i<s;i++)e[i].traverseVisible(t)}traverseAncestors(t){const e=this.parent;e!==null&&(t(e),e.traverseAncestors(t))}updateMatrix(){this.matrix.compose(this.position,this.quaternion,this.scale),this.matrixWorldNeedsUpdate=!0}updateMatrixWorld(t){this.matrixAutoUpdate&&this.updateMatrix(),(this.matrixWorldNeedsUpdate||t)&&(this.parent===null?this.matrixWorld.copy(this.matrix):this.matrixWorld.multiplyMatrices(this.parent.matrixWorld,this.matrix),this.matrixWorldNeedsUpdate=!1,t=!0);const e=this.children;for(let i=0,s=e.length;i<s;i++){const n=e[i];(n.matrixWorldAutoUpdate===!0||t===!0)&&n.updateMatrixWorld(t)}}updateWorldMatrix(t,e){const i=this.parent;if(t===!0&&i!==null&&i.matrixWorldAutoUpdate===!0&&i.updateWorldMatrix(!0,!1),this.matrixAutoUpdate&&this.updateMatrix(),this.parent===null?this.matrixWorld.copy(this.matrix):this.matrixWorld.multiplyMatrices(this.parent.matrixWorld,this.matrix),e===!0){const s=this.children;for(let n=0,r=s.length;n<r;n++){const a=s[n];a.matrixWorldAutoUpdate===!0&&a.updateWorldMatrix(!1,!0)}}}toJSON(t){const e=t===void 0||typeof t=="string",i={};e&&(t={geometries:{},materials:{},textures:{},images:{},shapes:{},skeletons:{},animations:{},nodes:{}},i.metadata={version:4.6,type:"Object",generator:"Object3D.toJSON"});const s={};s.uuid=this.uuid,s.type=this.type,this.name!==""&&(s.name=this.name),this.castShadow===!0&&(s.castShadow=!0),this.receiveShadow===!0&&(s.receiveShadow=!0),this.visible===!1&&(s.visible=!1),this.frustumCulled===!1&&(s.frustumCulled=!1),this.renderOrder!==0&&(s.renderOrder=this.renderOrder),Object.keys(this.userData).length>0&&(s.userData=this.userData),s.layers=this.layers.mask,s.matrix=this.matrix.toArray(),s.up=this.up.toArray(),this.matrixAutoUpdate===!1&&(s.matrixAutoUpdate=!1),this.isInstancedMesh&&(s.type="InstancedMesh",s.count=this.count,s.instanceMatrix=this.instanceMatrix.toJSON(),this.instanceColor!==null&&(s.instanceColor=this.instanceColor.toJSON())),this.isBatchedMesh&&(s.type="BatchedMesh",s.perObjectFrustumCulled=this.perObjectFrustumCulled,s.sortObjects=this.sortObjects,s.drawRanges=this._drawRanges,s.reservedRanges=this._reservedRanges,s.visibility=this._visibility,s.active=this._active,s.bounds=this._bounds.map(a=>({boxInitialized:a.boxInitialized,boxMin:a.box.min.toArray(),boxMax:a.box.max.toArray(),sphereInitialized:a.sphereInitialized,sphereRadius:a.sphere.radius,sphereCenter:a.sphere.center.toArray()})),s.maxGeometryCount=this._maxGeometryCount,s.maxVertexCount=this._maxVertexCount,s.maxIndexCount=this._maxIndexCount,s.geometryInitialized=this._geometryInitialized,s.geometryCount=this._geometryCount,s.matricesTexture=this._matricesTexture.toJSON(t),this.boundingSphere!==null&&(s.boundingSphere={center:s.boundingSphere.center.toArray(),radius:s.boundingSphere.radius}),this.boundingBox!==null&&(s.boundingBox={min:s.boundingBox.min.toArray(),max:s.boundingBox.max.toArray()}));function n(a,o){return a[o.uuid]===void 0&&(a[o.uuid]=o.toJSON(t)),o.uuid}if(this.isScene)this.background&&(this.background.isColor?s.background=this.background.toJSON():this.background.isTexture&&(s.background=this.background.toJSON(t).uuid)),this.environment&&this.environment.isTexture&&this.environment.isRenderTargetTexture!==!0&&(s.environment=this.environment.toJSON(t).uuid);else if(this.isMesh||this.isLine||this.isPoints){s.geometry=n(t.geometries,this.geometry);const a=this.geometry.parameters;if(a!==void 0&&a.shapes!==void 0){const o=a.shapes;if(Array.isArray(o))for(let h=0,c=o.length;h<c;h++){const f=o[h];n(t.shapes,f)}else n(t.shapes,o)}}if(this.isSkinnedMesh&&(s.bindMode=this.bindMode,s.bindMatrix=this.bindMatrix.toArray(),this.skeleton!==void 0&&(n(t.skeletons,this.skeleton),s.skeleton=this.skeleton.uuid)),this.material!==void 0)if(Array.isArray(this.material)){const a=[];for(let o=0,h=this.material.length;o<h;o++)a.push(n(t.materials,this.material[o]));s.material=a}else s.material=n(t.materials,this.material);if(this.children.length>0){s.children=[];for(let a=0;a<this.children.length;a++)s.children.push(this.children[a].toJSON(t).object)}if(this.animations.length>0){s.animations=[];for(let a=0;a<this.animations.length;a++){const o=this.animations[a];s.animations.push(n(t.animations,o))}}if(e){const a=r(t.geometries),o=r(t.materials),h=r(t.textures),c=r(t.images),f=r(t.shapes),l=r(t.skeletons),d=r(t.animations),p=r(t.nodes);a.length>0&&(i.geometries=a),o.length>0&&(i.materials=o),h.length>0&&(i.textures=h),c.length>0&&(i.images=c),f.length>0&&(i.shapes=f),l.length>0&&(i.skeletons=l),d.length>0&&(i.animations=d),p.length>0&&(i.nodes=p)}return i.object=s,i;function r(a){const o=[];for(const h in a){const c=a[h];delete c.metadata,o.push(c)}return o}}clone(t){return new this.constructor().copy(this,t)}copy(t,e=!0){if(this.name=t.name,this.up.copy(t.up),this.position.copy(t.position),this.rotation.order=t.rotation.order,this.quaternion.copy(t.quaternion),this.scale.copy(t.scale),this.matrix.copy(t.matrix),this.matrixWorld.copy(t.matrixWorld),this.matrixAutoUpdate=t.matrixAutoUpdate,this.matrixWorldAutoUpdate=t.matrixWorldAutoUpdate,this.matrixWorldNeedsUpdate=t.matrixWorldNeedsUpdate,this.layers.mask=t.layers.mask,this.visible=t.visible,this.castShadow=t.castShadow,this.receiveShadow=t.receiveShadow,this.frustumCulled=t.frustumCulled,this.renderOrder=t.renderOrder,this.animations=t.animations.slice(),this.userData=JSON.parse(JSON.stringify(t.userData)),e===!0)for(let i=0;i<t.children.length;i++){const s=t.children[i];this.add(s.clone())}return this}}Mt.DEFAULT_UP=new A(0,1,0),Mt.DEFAULT_MATRIX_AUTO_UPDATE=!0,Mt.DEFAULT_MATRIX_WORLD_AUTO_UPDATE=!0;const st=new A,dt=new A,Je=new A,pt=new A,Dt=new A,Vt=new A,Vi=new A,Qe=new A,Ke=new A,ti=new A;class nt{constructor(t=new A,e=new A,i=new A){this.a=t,this.b=e,this.c=i}static getNormal(t,e,i,s){s.subVectors(i,e),st.subVectors(t,e),s.cross(st);const n=s.lengthSq();return n>0?s.multiplyScalar(1/Math.sqrt(n)):s.set(0,0,0)}static getBarycoord(t,e,i,s,n){st.subVectors(s,e),dt.subVectors(i,e),Je.subVectors(t,e);const r=st.dot(st),a=st.dot(dt),o=st.dot(Je),h=dt.dot(dt),c=dt.dot(Je),f=r*h-a*a;if(f===0)return n.set(0,0,0),null;const l=1/f,d=(h*o-a*c)*l,p=(r*c-a*o)*l;return n.set(1-d-p,p,d)}static containsPoint(t,e,i,s){return this.getBarycoord(t,e,i,s,pt)===null?!1:pt.x>=0&&pt.y>=0&&pt.x+pt.y<=1}static getInterpolation(t,e,i,s,n,r,a,o){return this.getBarycoord(t,e,i,s,pt)===null?(o.x=0,o.y=0,"z"in o&&(o.z=0),"w"in o&&(o.w=0),null):(o.setScalar(0),o.addScaledVector(n,pt.x),o.addScaledVector(r,pt.y),o.addScaledVector(a,pt.z),o)}static isFrontFacing(t,e,i,s){return st.subVectors(i,e),dt.subVectors(t,e),st.cross(dt).dot(s)<0}set(t,e,i){return this.a.copy(t),this.b.copy(e),this.c.copy(i),this}setFromPointsAndIndices(t,e,i,s){return this.a.copy(t[e]),this.b.copy(t[i]),this.c.copy(t[s]),this}setFromAttributeAndIndices(t,e,i,s){return this.a.fromBufferAttribute(t,e),this.b.fromBufferAttribute(t,i),this.c.fromBufferAttribute(t,s),this}clone(){return new this.constructor().copy(this)}copy(t){return this.a.copy(t.a),this.b.copy(t.b),this.c.copy(t.c),this}getArea(){return st.subVectors(this.c,this.b),dt.subVectors(this.a,this.b),st.cross(dt).length()*.5}getMidpoint(t){return t.addVectors(this.a,this.b).add(this.c).multiplyScalar(1/3)}getNormal(t){return nt.getNormal(this.a,this.b,this.c,t)}getPlane(t){return t.setFromCoplanarPoints(this.a,this.b,this.c)}getBarycoord(t,e){return nt.getBarycoord(t,this.a,this.b,this.c,e)}getInterpolation(t,e,i,s,n){return nt.getInterpolation(t,this.a,this.b,this.c,e,i,s,n)}containsPoint(t){return nt.containsPoint(t,this.a,this.b,this.c)}isFrontFacing(t){return nt.isFrontFacing(this.a,this.b,this.c,t)}intersectsBox(t){return t.intersectsTriangle(this)}closestPointToPoint(t,e){const i=this.a,s=this.b,n=this.c;let r,a;Dt.subVectors(s,i),Vt.subVectors(n,i),Qe.subVectors(t,i);const o=Dt.dot(Qe),h=Vt.dot(Qe);if(o<=0&&h<=0)return e.copy(i);Ke.subVectors(t,s);const c=Dt.dot(Ke),f=Vt.dot(Ke);if(c>=0&&f<=c)return e.copy(s);const l=o*f-c*h;if(l<=0&&o>=0&&c<=0)return r=o/(o-c),e.copy(i).addScaledVector(Dt,r);ti.subVectors(t,n);const d=Dt.dot(ti),p=Vt.dot(ti);if(p>=0&&d<=p)return e.copy(n);const y=d*h-o*p;if(y<=0&&h>=0&&p<=0)return a=h/(h-p),e.copy(i).addScaledVector(Vt,a);const x=c*p-d*f;if(x<=0&&f-c>=0&&d-p>=0)return Vi.subVectors(n,s),a=(f-c)/(f-c+(d-p)),e.copy(s).addScaledVector(Vi,a);const m=1/(x+y+l);return r=y*m,a=l*m,e.copy(i).addScaledVector(Dt,r).addScaledVector(Vt,a)}equals(t){return t.a.equals(this.a)&&t.b.equals(this.b)&&t.c.equals(this.c)}}const I=new A,we=new v;class ct{constructor(t,e,i=!1){if(Array.isArray(t))throw new TypeError("THREE.BufferAttribute: array should be a Typed Array.");this.isBufferAttribute=!0,this.name="",this.array=t,this.itemSize=e,this.count=t!==void 0?t.length/e:0,this.normalized=i,this.usage=35044,this._updateRange={offset:0,count:-1},this.updateRanges=[],this.gpuType=1015,this.version=0}onUploadCallback(){}set needsUpdate(t){t===!0&&this.version++}get updateRange(){return os("THREE.BufferAttribute: updateRange() is deprecated and will be removed in r169. Use addUpdateRange() instead."),this._updateRange}setUsage(t){return this.usage=t,this}addUpdateRange(t,e){this.updateRanges.push({start:t,count:e})}clearUpdateRanges(){this.updateRanges.length=0}copy(t){return this.name=t.name,this.array=new t.array.constructor(t.array),this.itemSize=t.itemSize,this.count=t.count,this.normalized=t.normalized,this.usage=t.usage,this.gpuType=t.gpuType,this}copyAt(t,e,i){t*=this.itemSize,i*=e.itemSize;for(let s=0,n=this.itemSize;s<n;s++)this.array[t+s]=e.array[i+s];return this}copyArray(t){return this.array.set(t),this}applyMatrix3(t){if(this.itemSize===2)for(let e=0,i=this.count;e<i;e++)we.fromBufferAttribute(this,e),we.applyMatrix3(t),this.setXY(e,we.x,we.y);else if(this.itemSize===3)for(let e=0,i=this.count;e<i;e++)I.fromBufferAttribute(this,e),I.applyMatrix3(t),this.setXYZ(e,I.x,I.y,I.z);return this}applyMatrix4(t){for(let e=0,i=this.count;e<i;e++)I.fromBufferAttribute(this,e),I.applyMatrix4(t),this.setXYZ(e,I.x,I.y,I.z);return this}applyNormalMatrix(t){for(let e=0,i=this.count;e<i;e++)I.fromBufferAttribute(this,e),I.applyNormalMatrix(t),this.setXYZ(e,I.x,I.y,I.z);return this}transformDirection(t){for(let e=0,i=this.count;e<i;e++)I.fromBufferAttribute(this,e),I.transformDirection(t),this.setXYZ(e,I.x,I.y,I.z);return this}set(t,e=0){return this.array.set(t,e),this}getComponent(t,e){let i=this.array[t*this.itemSize+e];return this.normalized&&(i=Qt(i,this.array)),i}setComponent(t,e,i){return this.normalized&&(i=O(i,this.array)),this.array[t*this.itemSize+e]=i,this}getX(t){let e=this.array[t*this.itemSize];return this.normalized&&(e=Qt(e,this.array)),e}setX(t,e){return this.normalized&&(e=O(e,this.array)),this.array[t*this.itemSize]=e,this}getY(t){let e=this.array[t*this.itemSize+1];return this.normalized&&(e=Qt(e,this.array)),e}setY(t,e){return this.normalized&&(e=O(e,this.array)),this.array[t*this.itemSize+1]=e,this}getZ(t){let e=this.array[t*this.itemSize+2];return this.normalized&&(e=Qt(e,this.array)),e}setZ(t,e){return this.normalized&&(e=O(e,this.array)),this.array[t*this.itemSize+2]=e,this}getW(t){let e=this.array[t*this.itemSize+3];return this.normalized&&(e=Qt(e,this.array)),e}setW(t,e){return this.normalized&&(e=O(e,this.array)),this.array[t*this.itemSize+3]=e,this}setXY(t,e,i){return t*=this.itemSize,this.normalized&&(e=O(e,this.array),i=O(i,this.array)),this.array[t+0]=e,this.array[t+1]=i,this}setXYZ(t,e,i,s){return t*=this.itemSize,this.normalized&&(e=O(e,this.array),i=O(i,this.array),s=O(s,this.array)),this.array[t+0]=e,this.array[t+1]=i,this.array[t+2]=s,this}setXYZW(t,e,i,s,n){return t*=this.itemSize,this.normalized&&(e=O(e,this.array),i=O(i,this.array),s=O(s,this.array),n=O(n,this.array)),this.array[t+0]=e,this.array[t+1]=i,this.array[t+2]=s,this.array[t+3]=n,this}onUpload(t){return this.onUploadCallback=t,this}clone(){return new this.constructor(this.array,this.itemSize).copy(this)}toJSON(){const t={itemSize:this.itemSize,type:this.array.constructor.name,array:Array.from(this.array),normalized:this.normalized};return this.name!==""&&(t.name=this.name),this.usage!==35044&&(t.usage=this.usage),t}}class bs extends ct{constructor(t,e,i){super(new Uint16Array(t),e,i)}}class ws extends ct{constructor(t,e,i){super(new Uint32Array(t),e,i)}}class _s extends ct{constructor(t,e,i){super(new Float32Array(t),e,i)}}let Ms=0;const G=new N,ei=new Mt,Nt=new A,$=new W,se=new W,V=new A;class ii extends Xe{constructor(){super(),this.isBufferGeometry=!0,Object.defineProperty(this,"id",{value:Ms++}),this.uuid=de(),this.name="",this.type="BufferGeometry",this.index=null,this.attributes={},this.morphAttributes={},this.morphTargetsRelative=!1,this.groups=[],this.boundingBox=null,this.boundingSphere=null,this.drawRange={start:0,count:1/0},this.userData={}}getIndex(){return this.index}setIndex(t){return Array.isArray(t)?this.index=new(rs(t)?ws:bs)(t,1):this.index=t,this}getAttribute(t){return this.attributes[t]}setAttribute(t,e){return this.attributes[t]=e,this}deleteAttribute(t){return delete this.attributes[t],this}hasAttribute(t){return this.attributes[t]!==void 0}addGroup(t,e,i=0){this.groups.push({start:t,count:e,materialIndex:i})}clearGroups(){this.groups=[]}setDrawRange(t,e){this.drawRange.start=t,this.drawRange.count=e}applyMatrix4(t){const e=this.attributes.position;e!==void 0&&(e.applyMatrix4(t),e.needsUpdate=!0);const i=this.attributes.normal;if(i!==void 0){const n=new Tt().getNormalMatrix(t);i.applyNormalMatrix(n),i.needsUpdate=!0}const s=this.attributes.tangent;return s!==void 0&&(s.transformDirection(t),s.needsUpdate=!0),this.boundingBox!==null&&this.computeBoundingBox(),this.boundingSphere!==null&&this.computeBoundingSphere(),this}applyQuaternion(t){return G.makeRotationFromQuaternion(t),this.applyMatrix4(G),this}rotateX(t){return G.makeRotationX(t),this.applyMatrix4(G),this}rotateY(t){return G.makeRotationY(t),this.applyMatrix4(G),this}rotateZ(t){return G.makeRotationZ(t),this.applyMatrix4(G),this}translate(t,e,i){return G.makeTranslation(t,e,i),this.applyMatrix4(G),this}scale(t,e,i){return G.makeScale(t,e,i),this.applyMatrix4(G),this}lookAt(t){return ei.lookAt(t),ei.updateMatrix(),this.applyMatrix4(ei.matrix),this}center(){return this.computeBoundingBox(),this.boundingBox.getCenter(Nt).negate(),this.translate(Nt.x,Nt.y,Nt.z),this}setFromPoints(t){const e=[];for(let i=0,s=t.length;i<s;i++){const n=t[i];e.push(n.x,n.y,n.z||0)}return this.setAttribute("position",new _s(e,3)),this}computeBoundingBox(){this.boundingBox===null&&(this.boundingBox=new W);const t=this.attributes.position,e=this.morphAttributes.position;if(t&&t.isGLBufferAttribute){console.error("THREE.BufferGeometry.computeBoundingBox(): GLBufferAttribute requires a manual bounding box.",this),this.boundingBox.set(new A(-1/0,-1/0,-1/0),new A(1/0,1/0,1/0));return}if(t!==void 0){if(this.boundingBox.setFromBufferAttribute(t),e)for(let i=0,s=e.length;i<s;i++){const n=e[i];$.setFromBufferAttribute(n),this.morphTargetsRelative?(V.addVectors(this.boundingBox.min,$.min),this.boundingBox.expandByPoint(V),V.addVectors(this.boundingBox.max,$.max),this.boundingBox.expandByPoint(V)):(this.boundingBox.expandByPoint($.min),this.boundingBox.expandByPoint($.max))}}else this.boundingBox.makeEmpty();(isNaN(this.boundingBox.min.x)||isNaN(this.boundingBox.min.y)||isNaN(this.boundingBox.min.z))&&console.error('THREE.BufferGeometry.computeBoundingBox(): Computed min/max have NaN values. The "position" attribute is likely to have NaN values.',this)}computeBoundingSphere(){this.boundingSphere===null&&(this.boundingSphere=new Ei);const t=this.attributes.position,e=this.morphAttributes.position;if(t&&t.isGLBufferAttribute){console.error("THREE.BufferGeometry.computeBoundingSphere(): GLBufferAttribute requires a manual bounding sphere.",this),this.boundingSphere.set(new A,1/0);return}if(t){const i=this.boundingSphere.center;if($.setFromBufferAttribute(t),e)for(let n=0,r=e.length;n<r;n++){const a=e[n];se.setFromBufferAttribute(a),this.morphTargetsRelative?(V.addVectors($.min,se.min),$.expandByPoint(V),V.addVectors($.max,se.max),$.expandByPoint(V)):($.expandByPoint(se.min),$.expandByPoint(se.max))}$.getCenter(i);let s=0;for(let n=0,r=t.count;n<r;n++)V.fromBufferAttribute(t,n),s=Math.max(s,i.distanceToSquared(V));if(e)for(let n=0,r=e.length;n<r;n++){const a=e[n],o=this.morphTargetsRelative;for(let h=0,c=a.count;h<c;h++)V.fromBufferAttribute(a,h),o&&(Nt.fromBufferAttribute(t,h),V.add(Nt)),s=Math.max(s,i.distanceToSquared(V))}this.boundingSphere.radius=Math.sqrt(s),isNaN(this.boundingSphere.radius)&&console.error('THREE.BufferGeometry.computeBoundingSphere(): Computed radius is NaN. The "position" attribute is likely to have NaN values.',this)}}computeTangents(){const t=this.index,e=this.attributes;if(t===null||e.position===void 0||e.normal===void 0||e.uv===void 0){console.error("THREE.BufferGeometry: .computeTangents() failed. Missing required attributes (index, position, normal or uv)");return}const i=e.position,s=e.normal,n=e.uv;this.hasAttribute("tangent")===!1&&this.setAttribute("tangent",new ct(new Float32Array(4*i.count),4));const r=this.getAttribute("tangent"),a=[],o=[];for(let S=0;S<i.count;S++)a[S]=new A,o[S]=new A;const h=new A,c=new A,f=new A,l=new v,d=new v,p=new v,y=new A,x=new A;function m(S,B,T){h.fromBufferAttribute(i,S),c.fromBufferAttribute(i,B),f.fromBufferAttribute(i,T),l.fromBufferAttribute(n,S),d.fromBufferAttribute(n,B),p.fromBufferAttribute(n,T),c.sub(h),f.sub(h),d.sub(l),p.sub(l);const C=1/(d.x*p.y-p.x*d.y);isFinite(C)&&(y.copy(c).multiplyScalar(p.y).addScaledVector(f,-d.y).multiplyScalar(C),x.copy(f).multiplyScalar(d.x).addScaledVector(c,-p.x).multiplyScalar(C),a[S].add(y),a[B].add(y),a[T].add(y),o[S].add(x),o[B].add(x),o[T].add(x))}let g=this.groups;g.length===0&&(g=[{start:0,count:t.count}]);for(let S=0,B=g.length;S<B;++S){const T=g[S],C=T.start,P=T.count;for(let E=C,F=C+P;E<F;E+=3)m(t.getX(E+0),t.getX(E+1),t.getX(E+2))}const w=new A,M=new A,b=new A,z=new A;function _(S){b.fromBufferAttribute(s,S),z.copy(b);const B=a[S];w.copy(B),w.sub(b.multiplyScalar(b.dot(B))).normalize(),M.crossVectors(z,B);const C=M.dot(o[S])<0?-1:1;r.setXYZW(S,w.x,w.y,w.z,C)}for(let S=0,B=g.length;S<B;++S){const T=g[S],C=T.start,P=T.count;for(let E=C,F=C+P;E<F;E+=3)_(t.getX(E+0)),_(t.getX(E+1)),_(t.getX(E+2))}}computeVertexNormals(){const t=this.index,e=this.getAttribute("position");if(e!==void 0){let i=this.getAttribute("normal");if(i===void 0)i=new ct(new Float32Array(e.count*3),3),this.setAttribute("normal",i);else for(let l=0,d=i.count;l<d;l++)i.setXYZ(l,0,0,0);const s=new A,n=new A,r=new A,a=new A,o=new A,h=new A,c=new A,f=new A;if(t)for(let l=0,d=t.count;l<d;l+=3){const p=t.getX(l+0),y=t.getX(l+1),x=t.getX(l+2);s.fromBufferAttribute(e,p),n.fromBufferAttribute(e,y),r.fromBufferAttribute(e,x),c.subVectors(r,n),f.subVectors(s,n),c.cross(f),a.fromBufferAttribute(i,p),o.fromBufferAttribute(i,y),h.fromBufferAttribute(i,x),a.add(c),o.add(c),h.add(c),i.setXYZ(p,a.x,a.y,a.z),i.setXYZ(y,o.x,o.y,o.z),i.setXYZ(x,h.x,h.y,h.z)}else for(let l=0,d=e.count;l<d;l+=3)s.fromBufferAttribute(e,l+0),n.fromBufferAttribute(e,l+1),r.fromBufferAttribute(e,l+2),c.subVectors(r,n),f.subVectors(s,n),c.cross(f),i.setXYZ(l+0,c.x,c.y,c.z),i.setXYZ(l+1,c.x,c.y,c.z),i.setXYZ(l+2,c.x,c.y,c.z);this.normalizeNormals(),i.needsUpdate=!0}}normalizeNormals(){const t=this.attributes.normal;for(let e=0,i=t.count;e<i;e++)V.fromBufferAttribute(t,e),V.normalize(),t.setXYZ(e,V.x,V.y,V.z)}toNonIndexed(){function t(a,o){const h=a.array,c=a.itemSize,f=a.normalized,l=new h.constructor(o.length*c);let d=0,p=0;for(let y=0,x=o.length;y<x;y++){a.isInterleavedBufferAttribute?d=o[y]*a.data.stride+a.offset:d=o[y]*c;for(let m=0;m<c;m++)l[p++]=h[d++]}return new ct(l,c,f)}if(this.index===null)return console.warn("THREE.BufferGeometry.toNonIndexed(): BufferGeometry is already non-indexed."),this;const e=new ii,i=this.index.array,s=this.attributes;for(const a in s){const o=s[a],h=t(o,i);e.setAttribute(a,h)}const n=this.morphAttributes;for(const a in n){const o=[],h=n[a];for(let c=0,f=h.length;c<f;c++){const l=h[c],d=t(l,i);o.push(d)}e.morphAttributes[a]=o}e.morphTargetsRelative=this.morphTargetsRelative;const r=this.groups;for(let a=0,o=r.length;a<o;a++){const h=r[a];e.addGroup(h.start,h.count,h.materialIndex)}return e}toJSON(){const t={metadata:{version:4.6,type:"BufferGeometry",generator:"BufferGeometry.toJSON"}};if(t.uuid=this.uuid,t.type=this.type,this.name!==""&&(t.name=this.name),Object.keys(this.userData).length>0&&(t.userData=this.userData),this.parameters!==void 0){const o=this.parameters;for(const h in o)o[h]!==void 0&&(t[h]=o[h]);return t}t.data={attributes:{}};const e=this.index;e!==null&&(t.data.index={type:e.array.constructor.name,array:Array.prototype.slice.call(e.array)});const i=this.attributes;for(const o in i){const h=i[o];t.data.attributes[o]=h.toJSON(t.data)}const s={};let n=!1;for(const o in this.morphAttributes){const h=this.morphAttributes[o],c=[];for(let f=0,l=h.length;f<l;f++){const d=h[f];c.push(d.toJSON(t.data))}c.length>0&&(s[o]=c,n=!0)}n&&(t.data.morphAttributes=s,t.data.morphTargetsRelative=this.morphTargetsRelative);const r=this.groups;r.length>0&&(t.data.groups=JSON.parse(JSON.stringify(r)));const a=this.boundingSphere;return a!==null&&(t.data.boundingSphere={center:a.center.toArray(),radius:a.radius}),t}clone(){return new this.constructor().copy(this)}copy(t){this.index=null,this.attributes={},this.morphAttributes={},this.groups=[],this.boundingBox=null,this.boundingSphere=null;const e={};this.name=t.name;const i=t.index;i!==null&&this.setIndex(i.clone(e));const s=t.attributes;for(const h in s){const c=s[h];this.setAttribute(h,c.clone(e))}const n=t.morphAttributes;for(const h in n){const c=[],f=n[h];for(let l=0,d=f.length;l<d;l++)c.push(f[l].clone(e));this.morphAttributes[h]=c}this.morphTargetsRelative=t.morphTargetsRelative;const r=t.groups;for(let h=0,c=r.length;h<c;h++){const f=r[h];this.addGroup(f.start,f.count,f.materialIndex)}const a=t.boundingBox;a!==null&&(this.boundingBox=a.clone());const o=t.boundingSphere;return o!==null&&(this.boundingSphere=o.clone()),this.drawRange.start=t.drawRange.start,this.drawRange.count=t.drawRange.count,this.userData=t.userData,this}dispose(){this.dispatchEvent({type:"dispose"})}}const si=new A,As=new A,zs=new Tt;class Ni{constructor(t=new A(1,0,0),e=0){this.isPlane=!0,this.normal=t,this.constant=e}set(t,e){return this.normal.copy(t),this.constant=e,this}setComponents(t,e,i,s){return this.normal.set(t,e,i),this.constant=s,this}setFromNormalAndCoplanarPoint(t,e){return this.normal.copy(t),this.constant=-e.dot(this.normal),this}setFromCoplanarPoints(t,e,i){const s=si.subVectors(i,e).cross(As.subVectors(t,e)).normalize();return this.setFromNormalAndCoplanarPoint(s,t),this}copy(t){return this.normal.copy(t.normal),this.constant=t.constant,this}normalize(){const t=1/this.normal.length();return this.normal.multiplyScalar(t),this.constant*=t,this}negate(){return this.constant*=-1,this.normal.negate(),this}distanceToPoint(t){return this.normal.dot(t)+this.constant}distanceToSphere(t){return this.distanceToPoint(t.center)-t.radius}projectPoint(t,e){return e.copy(t).addScaledVector(this.normal,-this.distanceToPoint(t))}intersectLine(t,e){const i=t.delta(si),s=this.normal.dot(i);if(s===0)return this.distanceToPoint(t.start)===0?e.copy(t.start):null;const n=-(t.start.dot(this.normal)+this.constant)/s;return n<0||n>1?null:e.copy(t.start).addScaledVector(i,n)}intersectsLine(t){const e=this.distanceToPoint(t.start),i=this.distanceToPoint(t.end);return e<0&&i>0||i<0&&e>0}intersectsBox(t){return t.intersectsPlane(this)}intersectsSphere(t){return t.intersectsPlane(this)}coplanarPoint(t){return t.copy(this.normal).multiplyScalar(-this.constant)}applyMatrix4(t,e){const i=e||zs.getNormalMatrix(t),s=this.coplanarPoint(si).applyMatrix4(t),n=this.normal.applyMatrix3(i).normalize();return this.constant=-s.dot(n),this}translate(t){return this.constant-=t.dot(this.normal),this}equals(t){return t.normal.equals(this.normal)&&t.constant===this.constant}clone(){return new this.constructor().copy(this)}}class Ss extends gt{constructor(t,e,i,s,n,r,a,o,h,c){if(c=c!==void 0?c:1026,c!==1026&&c!==1027)throw new Error("DepthTexture format must be either THREE.DepthFormat or THREE.DepthStencilFormat");i===void 0&&c===1026&&(i=1014),i===void 0&&c===1027&&(i=1020),super(null,s,n,r,a,o,c,i,h),this.isDepthTexture=!0,this.image={width:t,height:e},this.magFilter=a!==void 0?a:1003,this.minFilter=o!==void 0?o:1003,this.flipY=!1,this.generateMipmaps=!1,this.compareFunction=null}copy(t){return super.copy(t),this.compareFunction=t.compareFunction,this}toJSON(t){const e=super.toJSON(t);return this.compareFunction!==null&&(e.compareFunction=this.compareFunction),e}}const Bs=new Ss(1,1);Bs.compareFunction=515;const qi=new A,_e=new A;class mt{constructor(t=new A,e=new A){this.start=t,this.end=e}set(t,e){return this.start.copy(t),this.end.copy(e),this}copy(t){return this.start.copy(t.start),this.end.copy(t.end),this}getCenter(t){return t.addVectors(this.start,this.end).multiplyScalar(.5)}delta(t){return t.subVectors(this.end,this.start)}distanceSq(){return this.start.distanceToSquared(this.end)}distance(){return this.start.distanceTo(this.end)}at(t,e){return this.delta(e).multiplyScalar(t).add(this.start)}closestPointToPointParameter(t,e){qi.subVectors(t,this.start),_e.subVectors(this.end,this.start);const i=_e.dot(_e);let n=_e.dot(qi)/i;return e&&(n=at(n,0,1)),n}closestPointToPoint(t,e,i){const s=this.closestPointToPointParameter(t,e);return this.delta(i).multiplyScalar(s).add(this.start)}applyMatrix4(t){return this.start.applyMatrix4(t),this.end.applyMatrix4(t),this}equals(t){return t.start.equals(this.start)&&t.end.equals(this.end)}clone(){return new this.constructor().copy(this)}}typeof __THREE_DEVTOOLS__<"u"&&__THREE_DEVTOOLS__.dispatchEvent(new CustomEvent("register",{detail:{revision:Bi}})),typeof window<"u"&&(window.__THREE__?console.warn("WARNING: Multiple instances of Three.js being imported."):window.__THREE__=Bi);const vi=0,Ts=1,Ps=2,Wi=2,ni=1.25,Xi=1,ne=6*4+4+4,Me=65535,Cs=Math.pow(2,-24),ri=Symbol("SKIP_GENERATION");function Es(u){return u.index?u.index.count:u.attributes.position.count}function qt(u){return Es(u)/3}function Fs(u,t=ArrayBuffer){return u>65535?new Uint32Array(new t(4*u)):new Uint16Array(new t(2*u))}function Us(u,t){if(!u.index){const e=u.attributes.position.count,i=t.useSharedArrayBuffer?SharedArrayBuffer:ArrayBuffer,s=Fs(e,i);u.setIndex(new ct(s,1));for(let n=0;n<e;n++)s[n]=n}}function Oi(u){const t=qt(u),e=u.drawRange,i=e.start/3,s=(e.start+e.count)/3,n=Math.max(0,i),r=Math.min(t,s)-n;return[{offset:Math.floor(n),count:Math.floor(r)}]}function Yi(u){if(!u.groups||!u.groups.length)return Oi(u);const t=[],e=new Set,i=u.drawRange,s=i.start/3,n=(i.start+i.count)/3;for(const a of u.groups){const o=a.start/3,h=(a.start+a.count)/3;e.add(Math.max(s,o)),e.add(Math.min(n,h))}const r=Array.from(e.values()).sort((a,o)=>a-o);for(let a=0;a<r.length-1;a++){const o=r[a],h=r[a+1];t.push({offset:Math.floor(o),count:Math.floor(h-o)})}return t}function Rs(u){if(u.groups.length===0)return!1;const t=qt(u),e=Yi(u).sort((n,r)=>n.offset-r.offset),i=e[e.length-1];i.count=Math.min(t-i.offset,i.count);let s=0;return e.forEach(({count:n})=>s+=n),t!==s}function oi(u,t,e,i,s){let n=1/0,r=1/0,a=1/0,o=-1/0,h=-1/0,c=-1/0,f=1/0,l=1/0,d=1/0,p=-1/0,y=-1/0,x=-1/0;for(let m=t*6,g=(t+e)*6;m<g;m+=6){const w=u[m+0],M=u[m+1],b=w-M,z=w+M;b<n&&(n=b),z>o&&(o=z),w<f&&(f=w),w>p&&(p=w);const _=u[m+2],S=u[m+3],B=_-S,T=_+S;B<r&&(r=B),T>h&&(h=T),_<l&&(l=_),_>y&&(y=_);const C=u[m+4],P=u[m+5],E=C-P,F=C+P;E<a&&(a=E),F>c&&(c=F),C<d&&(d=C),C>x&&(x=C)}i[0]=n,i[1]=r,i[2]=a,i[3]=o,i[4]=h,i[5]=c,s[0]=f,s[1]=l,s[2]=d,s[3]=p,s[4]=y,s[5]=x}function ks(u,t=null,e=null,i=null){const s=u.attributes.position,n=u.index?u.index.array:null,r=qt(u),a=s.normalized;let o;t===null?(o=new Float32Array(r*6*4),e=0,i=r):(o=t,e=e||0,i=i||r);const h=s.array,c=s.offset||0;let f=3;s.isInterleavedBufferAttribute&&(f=s.data.stride);const l=["getX","getY","getZ"];for(let d=e;d<e+i;d++){const p=d*3,y=d*6;let x=p+0,m=p+1,g=p+2;n&&(x=n[x],m=n[m],g=n[g]),a||(x=x*f+c,m=m*f+c,g=g*f+c);for(let w=0;w<3;w++){let M,b,z;a?(M=s[l[w]](x),b=s[l[w]](m),z=s[l[w]](g)):(M=h[x+w],b=h[m+w],z=h[g+w]);let _=M;b<_&&(_=b),z<_&&(_=z);let S=M;b>S&&(S=b),z>S&&(S=z);const B=(S-_)/2,T=w*2;o[y+T+0]=_+B,o[y+T+1]=B+(Math.abs(_)+B)*Cs}}return o}function L(u,t,e){return e.min.x=t[u],e.min.y=t[u+1],e.min.z=t[u+2],e.max.x=t[u+3],e.max.y=t[u+4],e.max.z=t[u+5],e}function Zi(u){let t=-1,e=-1/0;for(let i=0;i<3;i++){const s=u[i+3]-u[i];s>e&&(e=s,t=i)}return t}function $i(u,t){t.set(u)}function Hi(u,t,e){let i,s;for(let n=0;n<3;n++){const r=n+3;i=u[n],s=t[n],e[n]=i<s?i:s,i=u[r],s=t[r],e[r]=i>s?i:s}}function Ae(u,t,e){for(let i=0;i<3;i++){const s=t[u+2*i],n=t[u+2*i+1],r=s-n,a=s+n;r<e[i]&&(e[i]=r),a>e[i+3]&&(e[i+3]=a)}}function re(u){const t=u[3]-u[0],e=u[4]-u[1],i=u[5]-u[2];return 2*(t*e+e*i+i*t)}const yt=32,Ls=(u,t)=>u.candidate-t.candidate,At=new Array(yt).fill().map(()=>({count:0,bounds:new Float32Array(6),rightCacheBounds:new Float32Array(6),leftCacheBounds:new Float32Array(6),candidate:0})),ze=new Float32Array(6);function Is(u,t,e,i,s,n){let r=-1,a=0;if(n===vi)r=Zi(t),r!==-1&&(a=(t[r]+t[r+3])/2);else if(n===Ts)r=Zi(u),r!==-1&&(a=Ds(e,i,s,r));else if(n===Ps){const o=re(u);let h=ni*s;const c=i*6,f=(i+s)*6;for(let l=0;l<3;l++){const d=t[l],x=(t[l+3]-d)/yt;if(s<yt/4){const m=[...At];m.length=s;let g=0;for(let M=c;M<f;M+=6,g++){const b=m[g];b.candidate=e[M+2*l],b.count=0;const{bounds:z,leftCacheBounds:_,rightCacheBounds:S}=b;for(let B=0;B<3;B++)S[B]=1/0,S[B+3]=-1/0,_[B]=1/0,_[B+3]=-1/0,z[B]=1/0,z[B+3]=-1/0;Ae(M,e,z)}m.sort(Ls);let w=s;for(let M=0;M<w;M++){const b=m[M];for(;M+1<w&&m[M+1].candidate===b.candidate;)m.splice(M+1,1),w--}for(let M=c;M<f;M+=6){const b=e[M+2*l];for(let z=0;z<w;z++){const _=m[z];b>=_.candidate?Ae(M,e,_.rightCacheBounds):(Ae(M,e,_.leftCacheBounds),_.count++)}}for(let M=0;M<w;M++){const b=m[M],z=b.count,_=s-b.count,S=b.leftCacheBounds,B=b.rightCacheBounds;let T=0;z!==0&&(T=re(S)/o);let C=0;_!==0&&(C=re(B)/o);const P=Xi+ni*(T*z+C*_);P<h&&(r=l,h=P,a=b.candidate)}}else{for(let w=0;w<yt;w++){const M=At[w];M.count=0,M.candidate=d+x+w*x;const b=M.bounds;for(let z=0;z<3;z++)b[z]=1/0,b[z+3]=-1/0}for(let w=c;w<f;w+=6){let z=~~((e[w+2*l]-d)/x);z>=yt&&(z=yt-1);const _=At[z];_.count++,Ae(w,e,_.bounds)}const m=At[yt-1];$i(m.bounds,m.rightCacheBounds);for(let w=yt-2;w>=0;w--){const M=At[w],b=At[w+1];Hi(M.bounds,b.rightCacheBounds,M.rightCacheBounds)}let g=0;for(let w=0;w<yt-1;w++){const M=At[w],b=M.count,z=M.bounds,S=At[w+1].rightCacheBounds;b!==0&&(g===0?$i(z,ze):Hi(z,ze,ze)),g+=b;let B=0,T=0;g!==0&&(B=re(ze)/o);const C=s-g;C!==0&&(T=re(S)/o);const P=Xi+ni*(B*g+T*C);P<h&&(r=l,h=P,a=M.candidate)}}}}else console.warn(`MeshBVH: Invalid build strategy value ${n} used.`);return{axis:r,pos:a}}function Ds(u,t,e,i){let s=0;for(let n=t,r=t+e;n<r;n++)s+=u[n*6+i*2];return s/e}class ai{constructor(){this.boundingData=new Float32Array(6)}}function Vs(u,t,e,i,s,n){let r=i,a=i+s-1;const o=n.pos,h=n.axis*2;for(;;){for(;r<=a&&e[r*6+h]<o;)r++;for(;r<=a&&e[a*6+h]>=o;)a--;if(r<a){for(let c=0;c<3;c++){let f=t[r*3+c];t[r*3+c]=t[a*3+c],t[a*3+c]=f}for(let c=0;c<6;c++){let f=e[r*6+c];e[r*6+c]=e[a*6+c],e[a*6+c]=f}r++,a--}else return r}}function Ns(u,t,e,i,s,n){let r=i,a=i+s-1;const o=n.pos,h=n.axis*2;for(;;){for(;r<=a&&e[r*6+h]<o;)r++;for(;r<=a&&e[a*6+h]>=o;)a--;if(r<a){let c=u[r];u[r]=u[a],u[a]=c;for(let f=0;f<6;f++){let l=e[r*6+f];e[r*6+f]=e[a*6+f],e[a*6+f]=l}r++,a--}else return r}}function Y(u,t){return t[u+15]===65535}function H(u,t){return t[u+6]}function J(u,t){return t[u+14]}function Q(u){return u+8}function K(u,t){return t[u+6]}function ji(u,t){return t[u+7]}function Jn(u){return u}let Gi,oe,Se,Ji;const qs=Math.pow(2,32);function ci(u){return"count"in u?1:1+ci(u.left)+ci(u.right)}function vs(u,t,e){return Gi=new Float32Array(e),oe=new Uint32Array(e),Se=new Uint16Array(e),Ji=new Uint8Array(e),hi(u,t)}function hi(u,t){const e=u/4,i=u/2,s="count"in t,n=t.boundingData;for(let r=0;r<6;r++)Gi[e+r]=n[r];if(s)if(t.buffer){const r=t.buffer;Ji.set(new Uint8Array(r),u);for(let a=u,o=u+r.byteLength;a<o;a+=ne){const h=a/2;Y(h,Se)||(oe[a/4+6]+=e)}return u+r.byteLength}else{const r=t.offset,a=t.count;return oe[e+6]=r,Se[i+14]=a,Se[i+15]=Me,u+ne}else{const r=t.left,a=t.right,o=t.splitAxis;let h;if(h=hi(u+ne,r),h/4>qs)throw new Error("MeshBVH: Cannot store child pointer greater than 32 bits.");return oe[e+6]=h/4,h=hi(h,a),oe[e+7]=o,h}}function Ws(u,t){const e=(u.index?u.index.count:u.attributes.position.count)/3,i=e>2**16,s=i?4:2,n=t?new SharedArrayBuffer(e*s):new ArrayBuffer(e*s),r=i?new Uint32Array(n):new Uint16Array(n);for(let a=0,o=r.length;a<o;a++)r[a]=a;return r}function Xs(u,t,e,i,s){const{maxDepth:n,verbose:r,maxLeafTris:a,strategy:o,onProgress:h,indirect:c}=s,f=u._indirectBuffer,l=u.geometry,d=l.index?l.index.array:null,p=c?Ns:Vs,y=qt(l),x=new Float32Array(6);let m=!1;const g=new ai;return oi(t,e,i,g.boundingData,x),M(g,e,i,x),g;function w(b){h&&h(b/y)}function M(b,z,_,S=null,B=0){if(!m&&B>=n&&(m=!0,r&&(console.warn(`MeshBVH: Max depth of ${n} reached when generating BVH. Consider increasing maxDepth.`),console.warn(l))),_<=a||B>=n)return w(z+_),b.offset=z,b.count=_,b;const T=Is(b.boundingData,S,t,z,_,o);if(T.axis===-1)return w(z+_),b.offset=z,b.count=_,b;const C=p(f,d,t,z,_,T);if(C===z||C===z+_)w(z+_),b.offset=z,b.count=_;else{b.splitAxis=T.axis;const P=new ai,E=z,F=C-z;b.left=P,oi(t,E,F,P.boundingData,x),M(P,E,F,x,B+1);const U=new ai,R=C,j=_-F;b.right=U,oi(t,R,j,U.boundingData,x),M(U,R,j,x,B+1)}return b}}function Os(u,t){const e=u.geometry;t.indirect&&(u._indirectBuffer=Ws(e,t.useSharedArrayBuffer),Rs(e)&&!t.verbose&&console.warn('MeshBVH: Provided geometry contains groups that do not fully span the vertex contents while using the "indirect" option. BVH may incorrectly report intersections on unrendered portions of the geometry.')),u._indirectBuffer||Us(e,t);const i=t.useSharedArrayBuffer?SharedArrayBuffer:ArrayBuffer,s=ks(e),n=t.indirect?Oi(e):Yi(e);u._roots=n.map(r=>{const a=Xs(u,s,r.offset,r.count,t),o=ci(a),h=new i(ne*o);return vs(0,a,h),h})}class xt{constructor(){this.min=1/0,this.max=-1/0}setFromPointsField(t,e){let i=1/0,s=-1/0;for(let n=0,r=t.length;n<r;n++){const o=t[n][e];i=o<i?o:i,s=o>s?o:s}this.min=i,this.max=s}setFromPoints(t,e){let i=1/0,s=-1/0;for(let n=0,r=e.length;n<r;n++){const a=e[n],o=t.dot(a);i=o<i?o:i,s=o>s?o:s}this.min=i,this.max=s}isSeparated(t){return this.min>t.max||t.min>this.max}}xt.prototype.setFromBox=function(){const u=new A;return function(e,i){const s=i.min,n=i.max;let r=1/0,a=-1/0;for(let o=0;o<=1;o++)for(let h=0;h<=1;h++)for(let c=0;c<=1;c++){u.x=s.x*o+n.x*(1-o),u.y=s.y*h+n.y*(1-h),u.z=s.z*c+n.z*(1-c);const f=e.dot(u);r=Math.min(f,r),a=Math.max(f,a)}this.min=r,this.max=a}}();const Ys=function(){const u=new A,t=new A,e=new A;return function(s,n,r){const a=s.start,o=u,h=n.start,c=t;e.subVectors(a,h),u.subVectors(s.end,s.start),t.subVectors(n.end,n.start);const f=e.dot(c),l=c.dot(o),d=c.dot(c),p=e.dot(o),x=o.dot(o)*d-l*l;let m,g;x!==0?m=(f*l-p*d)/x:m=0,g=(f+m*l)/d,r.x=m,r.y=g}}(),li=function(){const u=new v,t=new A,e=new A;return function(s,n,r,a){Ys(s,n,u);let o=u.x,h=u.y;if(o>=0&&o<=1&&h>=0&&h<=1){s.at(o,r),n.at(h,a);return}else if(o>=0&&o<=1){h<0?n.at(0,a):n.at(1,a),s.closestPointToPoint(a,!0,r);return}else if(h>=0&&h<=1){o<0?s.at(0,r):s.at(1,r),n.closestPointToPoint(r,!0,a);return}else{let c;o<0?c=s.start:c=s.end;let f;h<0?f=n.start:f=n.end;const l=t,d=e;if(s.closestPointToPoint(f,!0,t),n.closestPointToPoint(c,!0,e),l.distanceToSquared(f)<=d.distanceToSquared(c)){r.copy(l),a.copy(f);return}else{r.copy(c),a.copy(d);return}}}}(),Zs=function(){const u=new A,t=new A,e=new Ni,i=new mt;return function(n,r){const{radius:a,center:o}=n,{a:h,b:c,c:f}=r;if(i.start=h,i.end=c,i.closestPointToPoint(o,!0,u).distanceTo(o)<=a||(i.start=h,i.end=f,i.closestPointToPoint(o,!0,u).distanceTo(o)<=a)||(i.start=c,i.end=f,i.closestPointToPoint(o,!0,u).distanceTo(o)<=a))return!0;const y=r.getPlane(e);if(Math.abs(y.distanceToPoint(o))<=a){const m=y.projectPoint(o,t);if(r.containsPoint(m))return!0}return!1}}(),$s=1e-15;function ui(u){return Math.abs(u)<$s}class rt extends nt{constructor(...t){super(...t),this.isExtendedTriangle=!0,this.satAxes=new Array(4).fill().map(()=>new A),this.satBounds=new Array(4).fill().map(()=>new xt),this.points=[this.a,this.b,this.c],this.sphere=new Ei,this.plane=new Ni,this.needsUpdate=!0}intersectsSphere(t){return Zs(t,this)}update(){const t=this.a,e=this.b,i=this.c,s=this.points,n=this.satAxes,r=this.satBounds,a=n[0],o=r[0];this.getNormal(a),o.setFromPoints(a,s);const h=n[1],c=r[1];h.subVectors(t,e),c.setFromPoints(h,s);const f=n[2],l=r[2];f.subVectors(e,i),l.setFromPoints(f,s);const d=n[3],p=r[3];d.subVectors(i,t),p.setFromPoints(d,s),this.sphere.setFromPoints(this.points),this.plane.setFromNormalAndCoplanarPoint(a,t),this.needsUpdate=!1}}rt.prototype.closestPointToSegment=function(){const u=new A,t=new A,e=new mt;return function(s,n=null,r=null){const{start:a,end:o}=s,h=this.points;let c,f=1/0;for(let l=0;l<3;l++){const d=(l+1)%3;e.start.copy(h[l]),e.end.copy(h[d]),li(e,s,u,t),c=u.distanceToSquared(t),c<f&&(f=c,n&&n.copy(u),r&&r.copy(t))}return this.closestPointToPoint(a,u),c=a.distanceToSquared(u),c<f&&(f=c,n&&n.copy(u),r&&r.copy(a)),this.closestPointToPoint(o,u),c=o.distanceToSquared(u),c<f&&(f=c,n&&n.copy(u),r&&r.copy(o)),Math.sqrt(f)}}(),rt.prototype.intersectsTriangle=function(){const u=new rt,t=new Array(3),e=new Array(3),i=new xt,s=new xt,n=new A,r=new A,a=new A,o=new A,h=new A,c=new mt,f=new mt,l=new mt,d=new A;function p(y,x,m){const g=y.points;let w=0,M=-1;for(let b=0;b<3;b++){const{start:z,end:_}=c;z.copy(g[b]),_.copy(g[(b+1)%3]),c.delta(r);const S=ui(x.distanceToPoint(z));if(ui(x.normal.dot(r))&&S){m.copy(c),w=2;break}const B=x.intersectLine(c,d);if(!B&&S&&d.copy(z),(B||S)&&!ui(d.distanceTo(_))){if(w<=1)(w===1?m.start:m.end).copy(d),S&&(M=w);else if(w>=2){(M===1?m.start:m.end).copy(d),w=2;break}if(w++,w===2&&M===-1)break}}return w}return function(x,m=null,g=!1){this.needsUpdate&&this.update(),x.isExtendedTriangle?x.needsUpdate&&x.update():(u.copy(x),u.update(),x=u);const w=this.plane,M=x.plane;if(Math.abs(w.normal.dot(M.normal))>1-1e-10){const b=this.satBounds,z=this.satAxes;e[0]=x.a,e[1]=x.b,e[2]=x.c;for(let B=0;B<4;B++){const T=b[B],C=z[B];if(i.setFromPoints(C,e),T.isSeparated(i))return!1}const _=x.satBounds,S=x.satAxes;t[0]=this.a,t[1]=this.b,t[2]=this.c;for(let B=0;B<4;B++){const T=_[B],C=S[B];if(i.setFromPoints(C,t),T.isSeparated(i))return!1}for(let B=0;B<4;B++){const T=z[B];for(let C=0;C<4;C++){const P=S[C];if(n.crossVectors(T,P),i.setFromPoints(n,t),s.setFromPoints(n,e),i.isSeparated(s))return!1}}return m&&(g||console.warn("ExtendedTriangle.intersectsTriangle: Triangles are coplanar which does not support an output edge. Setting edge to 0, 0, 0."),m.start.set(0,0,0),m.end.set(0,0,0)),!0}else{const b=p(this,M,f);if(b===1&&x.containsPoint(f.end))return m&&(m.start.copy(f.end),m.end.copy(f.end)),!0;if(b!==2)return!1;const z=p(x,w,l);if(z===1&&this.containsPoint(l.end))return m&&(m.start.copy(l.end),m.end.copy(l.end)),!0;if(z!==2)return!1;if(f.delta(a),l.delta(o),a.dot(o)<0){let E=l.start;l.start=l.end,l.end=E}const _=f.start.dot(a),S=f.end.dot(a),B=l.start.dot(a),T=l.end.dot(a),C=S<B,P=_<T;return _!==T&&B!==S&&C===P?!1:(m&&(h.subVectors(f.start,l.start),h.dot(a)>0?m.start.copy(f.start):m.start.copy(l.start),h.subVectors(f.end,l.end),h.dot(a)<0?m.end.copy(f.end):m.end.copy(l.end)),!0)}}}(),rt.prototype.distanceToPoint=function(){const u=new A;return function(e){return this.closestPointToPoint(e,u),e.distanceTo(u)}}(),rt.prototype.distanceToTriangle=function(){const u=new A,t=new A,e=["a","b","c"],i=new mt,s=new mt;return function(r,a=null,o=null){const h=a||o?i:null;if(this.intersectsTriangle(r,h))return(a||o)&&(a&&h.getCenter(a),o&&h.getCenter(o)),0;let c=1/0;for(let f=0;f<3;f++){let l;const d=e[f],p=r[d];this.closestPointToPoint(p,u),l=p.distanceToSquared(u),l<c&&(c=l,a&&a.copy(u),o&&o.copy(p));const y=this[d];r.closestPointToPoint(y,u),l=y.distanceToSquared(u),l<c&&(c=l,a&&a.copy(y),o&&o.copy(u))}for(let f=0;f<3;f++){const l=e[f],d=e[(f+1)%3];i.set(this[l],this[d]);for(let p=0;p<3;p++){const y=e[p],x=e[(p+1)%3];s.set(r[y],r[x]),li(i,s,u,t);const m=u.distanceToSquared(t);m<c&&(c=m,a&&a.copy(u),o&&o.copy(t))}}return Math.sqrt(c)}}();class X{constructor(t,e,i){this.isOrientedBox=!0,this.min=new A,this.max=new A,this.matrix=new N,this.invMatrix=new N,this.points=new Array(8).fill().map(()=>new A),this.satAxes=new Array(3).fill().map(()=>new A),this.satBounds=new Array(3).fill().map(()=>new xt),this.alignedSatBounds=new Array(3).fill().map(()=>new xt),this.needsUpdate=!1,t&&this.min.copy(t),e&&this.max.copy(e),i&&this.matrix.copy(i)}set(t,e,i){this.min.copy(t),this.max.copy(e),this.matrix.copy(i),this.needsUpdate=!0}copy(t){this.min.copy(t.min),this.max.copy(t.max),this.matrix.copy(t.matrix),this.needsUpdate=!0}}X.prototype.update=function(){return function(){const t=this.matrix,e=this.min,i=this.max,s=this.points;for(let h=0;h<=1;h++)for(let c=0;c<=1;c++)for(let f=0;f<=1;f++){const l=1*h|2*c|4*f,d=s[l];d.x=h?i.x:e.x,d.y=c?i.y:e.y,d.z=f?i.z:e.z,d.applyMatrix4(t)}const n=this.satBounds,r=this.satAxes,a=s[0];for(let h=0;h<3;h++){const c=r[h],f=n[h],l=1<<h,d=s[l];c.subVectors(a,d),f.setFromPoints(c,s)}const o=this.alignedSatBounds;o[0].setFromPointsField(s,"x"),o[1].setFromPointsField(s,"y"),o[2].setFromPointsField(s,"z"),this.invMatrix.copy(this.matrix).invert(),this.needsUpdate=!1}}(),X.prototype.intersectsBox=function(){const u=new xt;return function(e){this.needsUpdate&&this.update();const i=e.min,s=e.max,n=this.satBounds,r=this.satAxes,a=this.alignedSatBounds;if(u.min=i.x,u.max=s.x,a[0].isSeparated(u)||(u.min=i.y,u.max=s.y,a[1].isSeparated(u))||(u.min=i.z,u.max=s.z,a[2].isSeparated(u)))return!1;for(let o=0;o<3;o++){const h=r[o],c=n[o];if(u.setFromBox(h,e),c.isSeparated(u))return!1}return!0}}(),X.prototype.intersectsTriangle=function(){const u=new rt,t=new Array(3),e=new xt,i=new xt,s=new A;return function(r){this.needsUpdate&&this.update(),r.isExtendedTriangle?r.needsUpdate&&r.update():(u.copy(r),u.update(),r=u);const a=this.satBounds,o=this.satAxes;t[0]=r.a,t[1]=r.b,t[2]=r.c;for(let l=0;l<3;l++){const d=a[l],p=o[l];if(e.setFromPoints(p,t),d.isSeparated(e))return!1}const h=r.satBounds,c=r.satAxes,f=this.points;for(let l=0;l<3;l++){const d=h[l],p=c[l];if(e.setFromPoints(p,f),d.isSeparated(e))return!1}for(let l=0;l<3;l++){const d=o[l];for(let p=0;p<4;p++){const y=c[p];if(s.crossVectors(d,y),e.setFromPoints(s,t),i.setFromPoints(s,f),e.isSeparated(i))return!1}}return!0}}(),X.prototype.closestPointToPoint=function(){return function(t,e){return this.needsUpdate&&this.update(),e.copy(t).applyMatrix4(this.invMatrix).clamp(this.min,this.max).applyMatrix4(this.matrix),e}}(),X.prototype.distanceToPoint=function(){const u=new A;return function(e){return this.closestPointToPoint(e,u),e.distanceTo(u)}}(),X.prototype.distanceToBox=function(){const u=["x","y","z"],t=new Array(12).fill().map(()=>new mt),e=new Array(12).fill().map(()=>new mt),i=new A,s=new A;return function(r,a=0,o=null,h=null){if(this.needsUpdate&&this.update(),this.intersectsBox(r))return(o||h)&&(r.getCenter(s),this.closestPointToPoint(s,i),r.closestPointToPoint(i,s),o&&o.copy(i),h&&h.copy(s)),0;const c=a*a,f=r.min,l=r.max,d=this.points;let p=1/0;for(let x=0;x<8;x++){const m=d[x];s.copy(m).clamp(f,l);const g=m.distanceToSquared(s);if(g<p&&(p=g,o&&o.copy(m),h&&h.copy(s),g<c))return Math.sqrt(g)}let y=0;for(let x=0;x<3;x++)for(let m=0;m<=1;m++)for(let g=0;g<=1;g++){const w=(x+1)%3,M=(x+2)%3,b=m<<w|g<<M,z=1<<x|m<<w|g<<M,_=d[b],S=d[z];t[y].set(_,S);const T=u[x],C=u[w],P=u[M],E=e[y],F=E.start,U=E.end;F[T]=f[T],F[C]=m?f[C]:l[C],F[P]=g?f[P]:l[C],U[T]=l[T],U[C]=m?f[C]:l[C],U[P]=g?f[P]:l[C],y++}for(let x=0;x<=1;x++)for(let m=0;m<=1;m++)for(let g=0;g<=1;g++){s.x=x?l.x:f.x,s.y=m?l.y:f.y,s.z=g?l.z:f.z,this.closestPointToPoint(s,i);const w=s.distanceToSquared(i);if(w<p&&(p=w,o&&o.copy(i),h&&h.copy(s),w<c))return Math.sqrt(w)}for(let x=0;x<12;x++){const m=t[x];for(let g=0;g<12;g++){const w=e[g];li(m,w,i,s);const M=i.distanceToSquared(s);if(M<p&&(p=M,o&&o.copy(i),h&&h.copy(s),M<c))return Math.sqrt(M)}}return Math.sqrt(p)}}();class fi{constructor(t){this._getNewPrimitive=t,this._primitives=[]}getPrimitive(){const t=this._primitives;return t.length===0?this._getNewPrimitive():t.pop()}releasePrimitive(t){this._primitives.push(t)}}class Hs extends fi{constructor(){super(()=>new rt)}}const tt=new Hs;class js{constructor(){this.float32Array=null,this.uint16Array=null,this.uint32Array=null;const t=[];let e=null;this.setBuffer=i=>{e&&t.push(e),e=i,this.float32Array=new Float32Array(i),this.uint16Array=new Uint16Array(i),this.uint32Array=new Uint32Array(i)},this.clearBuffer=()=>{e=null,this.float32Array=null,this.uint16Array=null,this.uint32Array=null,t.length!==0&&this.setBuffer(t.pop())}}}const k=new js;let zt,vt;const Wt=[],Be=new fi(()=>new W);function Gs(u,t,e,i,s,n){zt=Be.getPrimitive(),vt=Be.getPrimitive(),Wt.push(zt,vt),k.setBuffer(u._roots[t]);const r=di(0,u.geometry,e,i,s,n);k.clearBuffer(),Be.releasePrimitive(zt),Be.releasePrimitive(vt),Wt.pop(),Wt.pop();const a=Wt.length;return a>0&&(vt=Wt[a-1],zt=Wt[a-2]),r}function di(u,t,e,i,s=null,n=0,r=0){const{float32Array:a,uint16Array:o,uint32Array:h}=k;let c=u*2;if(Y(c,o)){const l=H(u,h),d=J(c,o);return L(u,a,zt),i(l,d,!1,r,n+u,zt)}else{let T=function(P){const{uint16Array:E,uint32Array:F}=k;let U=P*2;for(;!Y(U,E);)P=Q(P),U=P*2;return H(P,F)},C=function(P){const{uint16Array:E,uint32Array:F}=k;let U=P*2;for(;!Y(U,E);)P=K(P,F),U=P*2;return H(P,F)+J(U,E)};const l=Q(u),d=K(u,h);let p=l,y=d,x,m,g,w;if(s&&(g=zt,w=vt,L(p,a,g),L(y,a,w),x=s(g),m=s(w),m<x)){p=d,y=l;const P=x;x=m,m=P,g=w}g||(g=zt,L(p,a,g));const M=Y(p*2,o),b=e(g,M,x,r+1,n+p);let z;if(b===Wi){const P=T(p),F=C(p)-P;z=i(P,F,!0,r+1,n+p,g)}else z=b&&di(p,t,e,i,s,n,r+1);if(z)return!0;w=vt,L(y,a,w);const _=Y(y*2,o),S=e(w,_,m,r+1,n+y);let B;if(S===Wi){const P=T(y),F=C(y)-P;B=i(P,F,!0,r+1,n+y,w)}else B=S&&di(y,t,e,i,s,n,r+1);return!!B}}const ae=new A,pi=new A;function Js(u,t,e={},i=0,s=1/0){const n=i*i,r=s*s;let a=1/0,o=null;if(u.shapecast({boundsTraverseOrder:c=>(ae.copy(t).clamp(c.min,c.max),ae.distanceToSquared(t)),intersectsBounds:(c,f,l)=>l<a&&l<r,intersectsTriangle:(c,f)=>{c.closestPointToPoint(t,ae);const l=t.distanceToSquared(ae);return l<a&&(pi.copy(ae),a=l,o=f),l<n}}),a===1/0)return null;const h=Math.sqrt(a);return e.point?e.point.copy(pi):e.point=pi.clone(),e.distance=h,e.faceIndex=o,e}const Xt=new A,Ot=new A,Yt=new A,Te=new v,Pe=new v,Ce=new v,Qi=new A,Ki=new A,ts=new A,Ee=new A;function Qs(u,t,e,i,s,n,r,a){let o;if(n===1?o=u.intersectTriangle(i,e,t,!0,s):o=u.intersectTriangle(t,e,i,n!==2,s),o===null)return null;const h=u.origin.distanceTo(s);return h<r||h>a?null:{distance:h,point:s.clone()}}function Ks(u,t,e,i,s,n,r,a,o,h,c){Xt.fromBufferAttribute(t,n),Ot.fromBufferAttribute(t,r),Yt.fromBufferAttribute(t,a);const f=Qs(u,Xt,Ot,Yt,Ee,o,h,c);if(f){i&&(Te.fromBufferAttribute(i,n),Pe.fromBufferAttribute(i,r),Ce.fromBufferAttribute(i,a),f.uv=nt.getInterpolation(Ee,Xt,Ot,Yt,Te,Pe,Ce,new v)),s&&(Te.fromBufferAttribute(s,n),Pe.fromBufferAttribute(s,r),Ce.fromBufferAttribute(s,a),f.uv1=nt.getInterpolation(Ee,Xt,Ot,Yt,Te,Pe,Ce,new v)),e&&(Qi.fromBufferAttribute(e,n),Ki.fromBufferAttribute(e,r),ts.fromBufferAttribute(e,a),f.normal=nt.getInterpolation(Ee,Xt,Ot,Yt,Qi,Ki,ts,new A),f.normal.dot(u.direction)>0&&f.normal.multiplyScalar(-1));const l={a:n,b:r,c:a,normal:new A,materialIndex:0};nt.getNormal(Xt,Ot,Yt,l.normal),f.face=l,f.faceIndex=n}return f}function Fe(u,t,e,i,s,n,r){const a=i*3;let o=a+0,h=a+1,c=a+2;const f=u.index;u.index&&(o=f.getX(o),h=f.getX(h),c=f.getX(c));const{position:l,normal:d,uv:p,uv1:y}=u.attributes,x=Ks(e,l,d,p,y,o,h,c,t,n,r);return x?(x.faceIndex=i,s&&s.push(x),x):null}function D(u,t,e,i){const s=u.a,n=u.b,r=u.c;let a=t,o=t+1,h=t+2;e&&(a=e.getX(a),o=e.getX(o),h=e.getX(h)),s.x=i.getX(a),s.y=i.getY(a),s.z=i.getZ(a),n.x=i.getX(o),n.y=i.getY(o),n.z=i.getZ(o),r.x=i.getX(h),r.y=i.getY(h),r.z=i.getZ(h)}function tn(u,t,e,i,s,n,r,a){const{geometry:o,_indirectBuffer:h}=u;for(let c=i,f=i+s;c<f;c++)Fe(o,t,e,c,n,r,a)}function en(u,t,e,i,s,n,r){const{geometry:a,_indirectBuffer:o}=u;let h=1/0,c=null;for(let f=i,l=i+s;f<l;f++){let d;d=Fe(a,t,e,f,null,n,r),d&&d.distance<h&&(c=d,h=d.distance)}return c}function sn(u,t,e,i,s,n,r){const{geometry:a}=e,{index:o}=a,h=a.attributes.position;for(let c=u,f=t+u;c<f;c++){let l;if(l=c,D(r,l*3,o,h),r.needsUpdate=!0,i(r,l,s,n))return!0}return!1}function nn(u,t=null){t&&Array.isArray(t)&&(t=new Set(t));const e=u.geometry,i=e.index?e.index.array:null,s=e.attributes.position;let n,r,a,o,h=0;const c=u._roots;for(let l=0,d=c.length;l<d;l++)n=c[l],r=new Uint32Array(n),a=new Uint16Array(n),o=new Float32Array(n),f(0,h),h+=n.byteLength;function f(l,d,p=!1){const y=l*2;if(a[y+15]===Me){const m=r[l+6],g=a[y+14];let w=1/0,M=1/0,b=1/0,z=-1/0,_=-1/0,S=-1/0;for(let B=3*m,T=3*(m+g);B<T;B++){let C=i[B];const P=s.getX(C),E=s.getY(C),F=s.getZ(C);P<w&&(w=P),P>z&&(z=P),E<M&&(M=E),E>_&&(_=E),F<b&&(b=F),F>S&&(S=F)}return o[l+0]!==w||o[l+1]!==M||o[l+2]!==b||o[l+3]!==z||o[l+4]!==_||o[l+5]!==S?(o[l+0]=w,o[l+1]=M,o[l+2]=b,o[l+3]=z,o[l+4]=_,o[l+5]=S,!0):!1}else{const m=l+8,g=r[l+6],w=m+d,M=g+d;let b=p,z=!1,_=!1;t?b||(z=t.has(w),_=t.has(M),b=!z&&!_):(z=!0,_=!0);const S=b||z,B=b||_;let T=!1;S&&(T=f(m,d,b));let C=!1;B&&(C=f(g,d,b));const P=T||C;if(P)for(let E=0;E<3;E++){const F=m+E,U=g+E,R=o[F],j=o[F+3],ht=o[U],lt=o[U+3];o[l+E]=R<ht?R:ht,o[l+E+3]=j>lt?j:lt}return P}}}function St(u,t,e,i,s){let n,r,a,o,h,c;const f=1/e.direction.x,l=1/e.direction.y,d=1/e.direction.z,p=e.origin.x,y=e.origin.y,x=e.origin.z;let m=t[u],g=t[u+3],w=t[u+1],M=t[u+3+1],b=t[u+2],z=t[u+3+2];return f>=0?(n=(m-p)*f,r=(g-p)*f):(n=(g-p)*f,r=(m-p)*f),l>=0?(a=(w-y)*l,o=(M-y)*l):(a=(M-y)*l,o=(w-y)*l),n>o||a>r||((a>n||isNaN(n))&&(n=a),(o<r||isNaN(r))&&(r=o),d>=0?(h=(b-x)*d,c=(z-x)*d):(h=(z-x)*d,c=(b-x)*d),n>c||h>r)?!1:((h>n||n!==n)&&(n=h),(c<r||r!==r)&&(r=c),n<=s&&r>=i)}function rn(u,t,e,i,s,n,r,a){const{geometry:o,_indirectBuffer:h}=u;for(let c=i,f=i+s;c<f;c++){let l=h?h[c]:c;Fe(o,t,e,l,n,r,a)}}function on(u,t,e,i,s,n,r){const{geometry:a,_indirectBuffer:o}=u;let h=1/0,c=null;for(let f=i,l=i+s;f<l;f++){let d;d=Fe(a,t,e,o?o[f]:f,null,n,r),d&&d.distance<h&&(c=d,h=d.distance)}return c}function an(u,t,e,i,s,n,r){const{geometry:a}=e,{index:o}=a,h=a.attributes.position;for(let c=u,f=t+u;c<f;c++){let l;if(l=e.resolveTriangleIndex(c),D(r,l*3,o,h),r.needsUpdate=!0,i(r,l,s,n))return!0}return!1}function cn(u,t,e,i,s,n,r){k.setBuffer(u._roots[t]),mi(0,u,e,i,s,n,r),k.clearBuffer()}function mi(u,t,e,i,s,n,r){const{float32Array:a,uint16Array:o,uint32Array:h}=k,c=u*2;if(Y(c,o)){const l=H(u,h),d=J(c,o);tn(t,e,i,l,d,s,n,r)}else{const l=Q(u);St(l,a,i,n,r)&&mi(l,t,e,i,s,n,r);const d=K(u,h);St(d,a,i,n,r)&&mi(d,t,e,i,s,n,r)}}const hn=["x","y","z"];function ln(u,t,e,i,s,n){k.setBuffer(u._roots[t]);const r=yi(0,u,e,i,s,n);return k.clearBuffer(),r}function yi(u,t,e,i,s,n){const{float32Array:r,uint16Array:a,uint32Array:o}=k;let h=u*2;if(Y(h,a)){const f=H(u,o),l=J(h,a);return en(t,e,i,f,l,s,n)}else{const f=ji(u,o),l=hn[f],p=i.direction[l]>=0;let y,x;p?(y=Q(u),x=K(u,o)):(y=K(u,o),x=Q(u));const g=St(y,r,i,s,n)?yi(y,t,e,i,s,n):null;if(g){const b=g.point[l];if(p?b<=r[x+f]:b>=r[x+f+3])return g}const M=St(x,r,i,s,n)?yi(x,t,e,i,s,n):null;return g&&M?g.distance<=M.distance?g:M:g||M||null}}const Ue=new W,Zt=new rt,$t=new rt,ce=new N,es=new X,Re=new X;function un(u,t,e,i){k.setBuffer(u._roots[t]);const s=xi(0,u,e,i);return k.clearBuffer(),s}function xi(u,t,e,i,s=null){const{float32Array:n,uint16Array:r,uint32Array:a}=k;let o=u*2;if(s===null&&(e.boundingBox||e.computeBoundingBox(),es.set(e.boundingBox.min,e.boundingBox.max,i),s=es),Y(o,r)){const c=t.geometry,f=c.index,l=c.attributes.position,d=e.index,p=e.attributes.position,y=H(u,a),x=J(o,r);if(ce.copy(i).invert(),e.boundsTree)return L(u,n,Re),Re.matrix.copy(ce),Re.needsUpdate=!0,e.boundsTree.shapecast({intersectsBounds:g=>Re.intersectsBox(g),intersectsTriangle:g=>{g.a.applyMatrix4(i),g.b.applyMatrix4(i),g.c.applyMatrix4(i),g.needsUpdate=!0;for(let w=y*3,M=(x+y)*3;w<M;w+=3)if(D($t,w,f,l),$t.needsUpdate=!0,g.intersectsTriangle($t))return!0;return!1}});for(let m=y*3,g=(x+y)*3;m<g;m+=3){D(Zt,m,f,l),Zt.a.applyMatrix4(ce),Zt.b.applyMatrix4(ce),Zt.c.applyMatrix4(ce),Zt.needsUpdate=!0;for(let w=0,M=d.count;w<M;w+=3)if(D($t,w,d,p),$t.needsUpdate=!0,Zt.intersectsTriangle($t))return!0}}else{const c=u+8,f=a[u+6];return L(c,n,Ue),!!(s.intersectsBox(Ue)&&xi(c,t,e,i,s)||(L(f,n,Ue),s.intersectsBox(Ue)&&xi(f,t,e,i,s)))}}const ke=new N,gi=new X,he=new X,fn=new A,dn=new A,pn=new A,mn=new A;function yn(u,t,e,i={},s={},n=0,r=1/0){t.boundingBox||t.computeBoundingBox(),gi.set(t.boundingBox.min,t.boundingBox.max,e),gi.needsUpdate=!0;const a=u.geometry,o=a.attributes.position,h=a.index,c=t.attributes.position,f=t.index,l=tt.getPrimitive(),d=tt.getPrimitive();let p=fn,y=dn,x=null,m=null;s&&(x=pn,m=mn);let g=1/0,w=null,M=null;return ke.copy(e).invert(),he.matrix.copy(ke),u.shapecast({boundsTraverseOrder:b=>gi.distanceToBox(b),intersectsBounds:(b,z,_)=>_<g&&_<r?(z&&(he.min.copy(b.min),he.max.copy(b.max),he.needsUpdate=!0),!0):!1,intersectsRange:(b,z)=>{if(t.boundsTree)return t.boundsTree.shapecast({boundsTraverseOrder:S=>he.distanceToBox(S),intersectsBounds:(S,B,T)=>T<g&&T<r,intersectsRange:(S,B)=>{for(let T=S,C=S+B;T<C;T++){D(d,3*T,f,c),d.a.applyMatrix4(e),d.b.applyMatrix4(e),d.c.applyMatrix4(e),d.needsUpdate=!0;for(let P=b,E=b+z;P<E;P++){D(l,3*P,h,o),l.needsUpdate=!0;const F=l.distanceToTriangle(d,p,x);if(F<g&&(y.copy(p),m&&m.copy(x),g=F,w=P,M=T),F<n)return!0}}}});{const _=qt(t);for(let S=0,B=_;S<B;S++){D(d,3*S,f,c),d.a.applyMatrix4(e),d.b.applyMatrix4(e),d.c.applyMatrix4(e),d.needsUpdate=!0;for(let T=b,C=b+z;T<C;T++){D(l,3*T,h,o),l.needsUpdate=!0;const P=l.distanceToTriangle(d,p,x);if(P<g&&(y.copy(p),m&&m.copy(x),g=P,w=T,M=S),P<n)return!0}}}}}),tt.releasePrimitive(l),tt.releasePrimitive(d),g===1/0?null:(i.point?i.point.copy(y):i.point=y.clone(),i.distance=g,i.faceIndex=w,s&&(s.point?s.point.copy(m):s.point=m.clone(),s.point.applyMatrix4(ke),y.applyMatrix4(ke),s.distance=y.sub(s.point).length(),s.faceIndex=M),i)}function xn(u,t=null){t&&Array.isArray(t)&&(t=new Set(t));const e=u.geometry,i=e.index?e.index.array:null,s=e.attributes.position;let n,r,a,o,h=0;const c=u._roots;for(let l=0,d=c.length;l<d;l++)n=c[l],r=new Uint32Array(n),a=new Uint16Array(n),o=new Float32Array(n),f(0,h),h+=n.byteLength;function f(l,d,p=!1){const y=l*2;if(a[y+15]===Me){const m=r[l+6],g=a[y+14];let w=1/0,M=1/0,b=1/0,z=-1/0,_=-1/0,S=-1/0;for(let B=m,T=m+g;B<T;B++){const C=3*u.resolveTriangleIndex(B);for(let P=0;P<3;P++){let E=C+P;E=i?i[E]:E;const F=s.getX(E),U=s.getY(E),R=s.getZ(E);F<w&&(w=F),F>z&&(z=F),U<M&&(M=U),U>_&&(_=U),R<b&&(b=R),R>S&&(S=R)}}return o[l+0]!==w||o[l+1]!==M||o[l+2]!==b||o[l+3]!==z||o[l+4]!==_||o[l+5]!==S?(o[l+0]=w,o[l+1]=M,o[l+2]=b,o[l+3]=z,o[l+4]=_,o[l+5]=S,!0):!1}else{const m=l+8,g=r[l+6],w=m+d,M=g+d;let b=p,z=!1,_=!1;t?b||(z=t.has(w),_=t.has(M),b=!z&&!_):(z=!0,_=!0);const S=b||z,B=b||_;let T=!1;S&&(T=f(m,d,b));let C=!1;B&&(C=f(g,d,b));const P=T||C;if(P)for(let E=0;E<3;E++){const F=m+E,U=g+E,R=o[F],j=o[F+3],ht=o[U],lt=o[U+3];o[l+E]=R<ht?R:ht,o[l+E+3]=j>lt?j:lt}return P}}}function gn(u,t,e,i,s,n,r){k.setBuffer(u._roots[t]),bi(0,u,e,i,s,n,r),k.clearBuffer()}function bi(u,t,e,i,s,n,r){const{float32Array:a,uint16Array:o,uint32Array:h}=k,c=u*2;if(Y(c,o)){const l=H(u,h),d=J(c,o);rn(t,e,i,l,d,s,n,r)}else{const l=Q(u);St(l,a,i,n,r)&&bi(l,t,e,i,s,n,r);const d=K(u,h);St(d,a,i,n,r)&&bi(d,t,e,i,s,n,r)}}const bn=["x","y","z"];function wn(u,t,e,i,s,n){k.setBuffer(u._roots[t]);const r=wi(0,u,e,i,s,n);return k.clearBuffer(),r}function wi(u,t,e,i,s,n){const{float32Array:r,uint16Array:a,uint32Array:o}=k;let h=u*2;if(Y(h,a)){const f=H(u,o),l=J(h,a);return on(t,e,i,f,l,s,n)}else{const f=ji(u,o),l=bn[f],p=i.direction[l]>=0;let y,x;p?(y=Q(u),x=K(u,o)):(y=K(u,o),x=Q(u));const g=St(y,r,i,s,n)?wi(y,t,e,i,s,n):null;if(g){const b=g.point[l];if(p?b<=r[x+f]:b>=r[x+f+3])return g}const M=St(x,r,i,s,n)?wi(x,t,e,i,s,n):null;return g&&M?g.distance<=M.distance?g:M:g||M||null}}const Le=new W,Ht=new rt,jt=new rt,le=new N,is=new X,Ie=new X;function _n(u,t,e,i){k.setBuffer(u._roots[t]);const s=_i(0,u,e,i);return k.clearBuffer(),s}function _i(u,t,e,i,s=null){const{float32Array:n,uint16Array:r,uint32Array:a}=k;let o=u*2;if(s===null&&(e.boundingBox||e.computeBoundingBox(),is.set(e.boundingBox.min,e.boundingBox.max,i),s=is),Y(o,r)){const c=t.geometry,f=c.index,l=c.attributes.position,d=e.index,p=e.attributes.position,y=H(u,a),x=J(o,r);if(le.copy(i).invert(),e.boundsTree)return L(u,n,Ie),Ie.matrix.copy(le),Ie.needsUpdate=!0,e.boundsTree.shapecast({intersectsBounds:g=>Ie.intersectsBox(g),intersectsTriangle:g=>{g.a.applyMatrix4(i),g.b.applyMatrix4(i),g.c.applyMatrix4(i),g.needsUpdate=!0;for(let w=y,M=x+y;w<M;w++)if(D(jt,3*t.resolveTriangleIndex(w),f,l),jt.needsUpdate=!0,g.intersectsTriangle(jt))return!0;return!1}});for(let m=y,g=x+y;m<g;m++){const w=t.resolveTriangleIndex(m);D(Ht,3*w,f,l),Ht.a.applyMatrix4(le),Ht.b.applyMatrix4(le),Ht.c.applyMatrix4(le),Ht.needsUpdate=!0;for(let M=0,b=d.count;M<b;M+=3)if(D(jt,M,d,p),jt.needsUpdate=!0,Ht.intersectsTriangle(jt))return!0}}else{const c=u+8,f=a[u+6];return L(c,n,Le),!!(s.intersectsBox(Le)&&_i(c,t,e,i,s)||(L(f,n,Le),s.intersectsBox(Le)&&_i(f,t,e,i,s)))}}const De=new N,Mi=new X,ue=new X,Mn=new A,An=new A,zn=new A,Sn=new A;function Bn(u,t,e,i={},s={},n=0,r=1/0){t.boundingBox||t.computeBoundingBox(),Mi.set(t.boundingBox.min,t.boundingBox.max,e),Mi.needsUpdate=!0;const a=u.geometry,o=a.attributes.position,h=a.index,c=t.attributes.position,f=t.index,l=tt.getPrimitive(),d=tt.getPrimitive();let p=Mn,y=An,x=null,m=null;s&&(x=zn,m=Sn);let g=1/0,w=null,M=null;return De.copy(e).invert(),ue.matrix.copy(De),u.shapecast({boundsTraverseOrder:b=>Mi.distanceToBox(b),intersectsBounds:(b,z,_)=>_<g&&_<r?(z&&(ue.min.copy(b.min),ue.max.copy(b.max),ue.needsUpdate=!0),!0):!1,intersectsRange:(b,z)=>{if(t.boundsTree){const _=t.boundsTree;return _.shapecast({boundsTraverseOrder:S=>ue.distanceToBox(S),intersectsBounds:(S,B,T)=>T<g&&T<r,intersectsRange:(S,B)=>{for(let T=S,C=S+B;T<C;T++){const P=_.resolveTriangleIndex(T);D(d,3*P,f,c),d.a.applyMatrix4(e),d.b.applyMatrix4(e),d.c.applyMatrix4(e),d.needsUpdate=!0;for(let E=b,F=b+z;E<F;E++){const U=u.resolveTriangleIndex(E);D(l,3*U,h,o),l.needsUpdate=!0;const R=l.distanceToTriangle(d,p,x);if(R<g&&(y.copy(p),m&&m.copy(x),g=R,w=E,M=T),R<n)return!0}}}})}else{const _=qt(t);for(let S=0,B=_;S<B;S++){D(d,3*S,f,c),d.a.applyMatrix4(e),d.b.applyMatrix4(e),d.c.applyMatrix4(e),d.needsUpdate=!0;for(let T=b,C=b+z;T<C;T++){const P=u.resolveTriangleIndex(T);D(l,3*P,h,o),l.needsUpdate=!0;const E=l.distanceToTriangle(d,p,x);if(E<g&&(y.copy(p),m&&m.copy(x),g=E,w=T,M=S),E<n)return!0}}}}}),tt.releasePrimitive(l),tt.releasePrimitive(d),g===1/0?null:(i.point?i.point.copy(y):i.point=y.clone(),i.distance=g,i.faceIndex=w,s&&(s.point?s.point.copy(m):s.point=m.clone(),s.point.applyMatrix4(De),y.applyMatrix4(De),s.distance=y.sub(s.point).length(),s.faceIndex=M),i)}function Tn(){return typeof SharedArrayBuffer<"u"}const fe=new k.constructor,Ve=new k.constructor,Bt=new fi(()=>new W),Gt=new W,Jt=new W,Ai=new W,zi=new W;let Si=!1;function Pn(u,t,e,i){if(Si)throw new Error("MeshBVH: Recursive calls to bvhcast not supported.");Si=!0;const s=u._roots,n=t._roots;let r,a=0,o=0;const h=new N().copy(e).invert();for(let c=0,f=s.length;c<f;c++){fe.setBuffer(s[c]),o=0;const l=Bt.getPrimitive();L(0,fe.float32Array,l),l.applyMatrix4(h);for(let d=0,p=n.length;d<p&&(Ve.setBuffer(n[c]),r=ot(0,0,e,h,i,a,o,0,0,l),Ve.clearBuffer(),o+=n[d].length,!r);d++);if(Bt.releasePrimitive(l),fe.clearBuffer(),a+=s[c].length,r)break}return Si=!1,r}function ot(u,t,e,i,s,n=0,r=0,a=0,o=0,h=null,c=!1){let f,l;c?(f=Ve,l=fe):(f=fe,l=Ve);const d=f.float32Array,p=f.uint32Array,y=f.uint16Array,x=l.float32Array,m=l.uint32Array,g=l.uint16Array,w=u*2,M=t*2,b=Y(w,y),z=Y(M,g);let _=!1;if(z&&b)c?_=s(H(t,m),J(t*2,g),H(u,p),J(u*2,y),o,r+t,a,n+u):_=s(H(u,p),J(u*2,y),H(t,m),J(t*2,g),a,n+u,o,r+t);else if(z){const S=Bt.getPrimitive();L(t,x,S),S.applyMatrix4(e);const B=Q(u),T=K(u,p);L(B,d,Gt),L(T,d,Jt);const C=S.intersectsBox(Gt),P=S.intersectsBox(Jt);_=C&&ot(t,B,i,e,s,r,n,o,a+1,S,!c)||P&&ot(t,T,i,e,s,r,n,o,a+1,S,!c),Bt.releasePrimitive(S)}else{const S=Q(t),B=K(t,m);L(S,x,Ai),L(B,x,zi);const T=h.intersectsBox(Ai),C=h.intersectsBox(zi);if(T&&C)_=ot(u,S,e,i,s,n,r,a,o+1,h,c)||ot(u,B,e,i,s,n,r,a,o+1,h,c);else if(T)if(b)_=ot(u,S,e,i,s,n,r,a,o+1,h,c);else{const P=Bt.getPrimitive();P.copy(Ai).applyMatrix4(e);const E=Q(u),F=K(u,p);L(E,d,Gt),L(F,d,Jt);const U=P.intersectsBox(Gt),R=P.intersectsBox(Jt);_=U&&ot(S,E,i,e,s,r,n,o,a+1,P,!c)||R&&ot(S,F,i,e,s,r,n,o,a+1,P,!c),Bt.releasePrimitive(P)}else if(C)if(b)_=ot(u,B,e,i,s,n,r,a,o+1,h,c);else{const P=Bt.getPrimitive();P.copy(zi).applyMatrix4(e);const E=Q(u),F=K(u,p);L(E,d,Gt),L(F,d,Jt);const U=P.intersectsBox(Gt),R=P.intersectsBox(Jt);_=U&&ot(B,E,i,e,s,r,n,o,a+1,P,!c)||R&&ot(B,F,i,e,s,r,n,o,a+1,P,!c),Bt.releasePrimitive(P)}}return _}const Ne=new X,ss=new W,Cn={strategy:vi,maxDepth:40,maxLeafTris:10,useSharedArrayBuffer:!1,setBoundingBox:!0,onProgress:null,indirect:!1,verbose:!0};class qe{static serialize(t,e={}){e={cloneBuffers:!0,...e};const i=t.geometry,s=t._roots,n=t._indirectBuffer,r=i.getIndex();let a;return e.cloneBuffers?a={roots:s.map(o=>o.slice()),index:r?r.array.slice():null,indirectBuffer:n?n.slice():null}:a={roots:s,index:r?r.array:null,indirectBuffer:n},a}static deserialize(t,e,i={}){i={setIndex:!0,indirect:!!t.indirectBuffer,...i};const{index:s,roots:n,indirectBuffer:r}=t,a=new qe(e,{...i,[ri]:!0});if(a._roots=n,a._indirectBuffer=r||null,i.setIndex){const o=e.getIndex();if(o===null){const h=new ct(t.index,1,!1);e.setIndex(h)}else o.array!==s&&(o.array.set(s),o.needsUpdate=!0)}return a}get indirect(){return!!this._indirectBuffer}constructor(t,e={}){if(t.isBufferGeometry){if(t.index&&t.index.isInterleavedBufferAttribute)throw new Error("MeshBVH: InterleavedBufferAttribute is not supported for the index attribute.")}else throw new Error("MeshBVH: Only BufferGeometries are supported.");if(e=Object.assign({...Cn,[ri]:!1},e),e.useSharedArrayBuffer&&!Tn())throw new Error("MeshBVH: SharedArrayBuffer is not available.");this.geometry=t,this._roots=null,this._indirectBuffer=null,e[ri]||(Os(this,e),!t.boundingBox&&e.setBoundingBox&&(t.boundingBox=this.getBoundingBox(new W))),this.resolveTriangleIndex=e.indirect?i=>this._indirectBuffer[i]:i=>i}refit(t=null){return(this.indirect?xn:nn)(this,t)}traverse(t,e=0){const i=this._roots[e],s=new Uint32Array(i),n=new Uint16Array(i);r(0);function r(a,o=0){const h=a*2,c=n[h+15]===Me;if(c){const f=s[a+6],l=n[h+14];t(o,c,new Float32Array(i,a*4,6),f,l)}else{const f=a+ne/4,l=s[a+6],d=s[a+7];t(o,c,new Float32Array(i,a*4,6),d)||(r(f,o+1),r(l,o+1))}}}raycast(t,e=0,i=0,s=1/0){const n=this._roots,r=this.geometry,a=[],o=e.isMaterial,h=Array.isArray(e),c=r.groups,f=o?e.side:e,l=this.indirect?gn:cn;for(let d=0,p=n.length;d<p;d++){const y=h?e[c[d].materialIndex].side:f,x=a.length;if(l(this,d,y,t,a,i,s),h){const m=c[d].materialIndex;for(let g=x,w=a.length;g<w;g++)a[g].face.materialIndex=m}}return a}raycastFirst(t,e=0,i=0,s=1/0){const n=this._roots,r=this.geometry,a=e.isMaterial,o=Array.isArray(e);let h=null;const c=r.groups,f=a?e.side:e,l=this.indirect?wn:ln;for(let d=0,p=n.length;d<p;d++){const y=o?e[c[d].materialIndex].side:f,x=l(this,d,y,t,i,s);x!=null&&(h==null||x.distance<h.distance)&&(h=x,o&&(x.face.materialIndex=c[d].materialIndex))}return h}intersectsGeometry(t,e){let i=!1;const s=this._roots,n=this.indirect?_n:un;for(let r=0,a=s.length;r<a&&(i=n(this,r,t,e),!i);r++);return i}shapecast(t){const e=tt.getPrimitive(),i=this.indirect?an:sn;let{boundsTraverseOrder:s,intersectsBounds:n,intersectsRange:r,intersectsTriangle:a}=t;if(r&&a){const f=r;r=(l,d,p,y,x)=>f(l,d,p,y,x)?!0:i(l,d,this,a,p,y,e)}else r||(a?r=(f,l,d,p)=>i(f,l,this,a,d,p,e):r=(f,l,d)=>d);let o=!1,h=0;const c=this._roots;for(let f=0,l=c.length;f<l;f++){const d=c[f];if(o=Gs(this,f,n,r,s,h),o)break;h+=d.byteLength}return tt.releasePrimitive(e),o}bvhcast(t,e,i){let{intersectsRanges:s,intersectsTriangles:n}=i;const r=tt.getPrimitive(),a=this.geometry.index,o=this.geometry.attributes.position,h=this.indirect?p=>{const y=this.resolveTriangleIndex(p);D(r,y*3,a,o)}:p=>{D(r,p*3,a,o)},c=tt.getPrimitive(),f=t.geometry.index,l=t.geometry.attributes.position,d=t.indirect?p=>{const y=t.resolveTriangleIndex(p);D(c,y*3,f,l)}:p=>{D(c,p*3,f,l)};if(n){const p=(y,x,m,g,w,M,b,z)=>{for(let _=m,S=m+g;_<S;_++){d(_),c.a.applyMatrix4(e),c.b.applyMatrix4(e),c.c.applyMatrix4(e),c.needsUpdate=!0;for(let B=y,T=y+x;B<T;B++)if(h(B),r.needsUpdate=!0,n(r,c,B,_,w,M,b,z))return!0}return!1};if(s){const y=s;s=function(x,m,g,w,M,b,z,_){return y(x,m,g,w,M,b,z,_)?!0:p(x,m,g,w,M,b,z,_)}}else s=p}return Pn(this,t,e,s)}intersectsBox(t,e){return Ne.set(t.min,t.max,e),Ne.needsUpdate=!0,this.shapecast({intersectsBounds:i=>Ne.intersectsBox(i),intersectsTriangle:i=>Ne.intersectsTriangle(i)})}intersectsSphere(t){return this.shapecast({intersectsBounds:e=>t.intersectsBox(e),intersectsTriangle:e=>e.intersectsSphere(t)})}closestPointToGeometry(t,e,i={},s={},n=0,r=1/0){return(this.indirect?Bn:yn)(this,t,e,i,s,n,r)}closestPointToPoint(t,e={},i=0,s=1/0){return Js(this,t,e,i,s)}getBoundingBox(t){return t.makeEmpty(),this._roots.forEach(i=>{L(0,new Float32Array(i),ss),t.union(ss)}),t}}onmessage=({data:u})=>{let t=performance.now();function e(r){r=Math.min(r,1);const a=performance.now();a-t>=10&&r!==1&&(postMessage({error:null,serialized:null,position:null,progress:r}),t=a)}const{index:i,position:s,options:n}=u;try{const r=new ii;if(r.setAttribute("position",new ct(s,3,!1)),i&&r.setIndex(new ct(i,1,!1)),n.includedProgressCallback&&(n.onProgress=e),n.groups){const c=n.groups;for(const f in c){const l=c[f];r.addGroup(l.start,l.count,l.materialIndex)}}const a=new qe(r,n),o=qe.serialize(a,{copyIndexBuffer:!1});let h=[s.buffer,...o.roots];o.index&&h.push(o.index.buffer),h=h.filter(c=>typeof SharedArrayBuffer>"u"||!(c instanceof SharedArrayBuffer)),a._indirectBuffer&&h.push(o.indirectBuffer.buffer),postMessage({error:null,serialized:o,position:s,progress:1},h)}catch(r){postMessage({error:r,serialized:null,position:null,progress:1})}}})();
