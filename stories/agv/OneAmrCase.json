// 配置完成的 AMR 对象的 JSON
{
    "AMR 品牌": "",
    "AMR 型号": "",
    "AMR 版本": "",
    "最后一次修改时间": "",
    "实例版本": "",
    "修改人":"",
    "认证": {
        "CE": true,
        "UL": false,
        "洁净度": "Opitional",
        "功能安全": "SGS-SIL2"
    },
    
    "设备清单" :[
      {
        "设备 ID": "Laser1",
        "设备类型": "2DLaser",
        "设备坐标": {"x":1,"y":2,"z":3,"yaw":0,"pitch":0,"roll":0},
        "是否启用": true, 
        "设备品牌": "Sick", 
        "设备型号": "LMS511", 
        "物料编码": "ABC1234567890",
        "设备参数": {/*根据设备品牌型号确定*/}  
      }
    ],

    "功能清单":
    [
        {
            "功能名称": "料架腿识别",
            "版本": "",
            "版本状态": "稳定",
            "Tips": "", 
            
            "功能参数列表": [
                // 参数对象
                {
                    "参数名称": "Distance",
                    "参数值": 1.0,
                    "子参数列表": []
                }
            ],
        
            "设备列表": [
                "Laser1"
            ]
        }
    ],
    
    
    "车体基本参数列表": [
        {
            "参数名称": "ABC",
            "参数值": "1234",
            "子参数列表": [
            ]
        }
    ],

    "其他定制化": {
      "LOGO": "",
      "外观": ""
    }
}



// 参数定义，对应模型文件
{
    "参数名称": "",
    "参数类型": "", // 数值，布尔量，字符串，选项#，IP 地址，列表
    "参数描述": "", // I18N ###
    "参数单位": "",
    "参数最大值": "",
    "参数最小值": "",
    "参数默认值": "",
    "参数选项": ["AAA","BBB"], // 如果父参数是个 选项，则可能存在多个选项。
    "子参数列表": { "AAA": [{},{}], "BBB":[{}]}, // 如果父参数是个 选项，则每个选项，可能存在一列子参数。
    "是否多个": false,
    "权限等级": 0, // 例如，0, 只有授权者和云系统可修改。1，Roboshop 普通登陆者可修改。
}



// 参数对象
{
  "参数名称": "",
  "参数值": "",
  "子参数列表": [
    "引用父对象"
  ],
  "权限等级": 0, // 例如，0, 只有授权者和云系统可修改。1，Roboshop 普通登陆者可修改。
}