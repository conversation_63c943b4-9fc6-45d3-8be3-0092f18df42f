// AMR 平台的 JSON
{
    "AMR 品牌": "",
    "AMR 型号": "",
    "AMR 版本": "",
    "认证": {
        "CE": true,
        "UL": false,
        "洁净度": "Opitional",
        "功能安全": "SGS-SIL2"
    },
    
    // 一辆平台车，能承接的设备类型是有限的。比如，只有一个主激光。
    "设备清单" :[
      {
        "设备 ID": "Laser1",
        "设备类型": "2DLaser",
        "设备坐标": {"x":1,"y":2,"z":3,"yaw":0,"pitch":0,"roll":0},
        // 车能带的设备范围清单，通过规则描述，不写在这里。
      }
    ],
    "功能清单":[
      // 功能依赖于设备。功能仓库中陈列了所有的功能清单。对单车而言，凡是有设备承接的功能，都可以引入该功能。
      "引用功能对象"
    ],
    
    
    "车体基本参数列表": [
      "引用参数对象"
    ],

    "其他定制化": {
      "LOGO": "",
      "外观": ""
    }
}


// 功能对象列表
[
  {
    "功能名称": "3D 相机栈板识别",
    "版本": "",
    "版本状态": "稳定",// 内测，公测，稳定等
    "Tips": "", // 发布预告、升级预告等。 
    
    "功能参数列表": [
      "引用参数对象"
    ],

    "设备列表": [
      {
        // "设备 ID" :"Laser", // 貌似不需要这一项
        "设备类型": "3D 相机",
        "设备需求": "Opitional" // 选项：可选（Opitional），单个（Single），至少一个（AtLeastOne）
      },
      {
        "设备类型": "3D 激光",
        "设备需求": "Single"
      }
    ],
  }
]


// 参数定义，对应模型文件
{
  "参数名称": "",
  "参数类型": "", // 数值，布尔量，字符串，选项#，IP 地址，列表
  "参数描述": "", // I18N ###
  "参数单位": "",
  "参数最大值": "",
  "参数最小值": "",
  "参数默认值": "",
  "参数选项": ["AAA","BBB"], // 如果父参数是个 选项，则可能存在多个选项。
  "子参数列表": { "AAA": [{},{}], "BBB":[{}]}, // 如果父参数是个 选项，则每个选项，可能存在一列子参数。
  "是否多个": false,
  "权限等级": 0, // 例如，0, 只有授权者和云系统可修改。1，Roboshop 普通登陆者可修改。
}

// 参数对象
{
  "参数名称": "",
  "参数值": "",
  "子参数列表": [
    "引用父对象"
  ],
}