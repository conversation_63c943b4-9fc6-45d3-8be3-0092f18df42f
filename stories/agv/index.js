const items = [
  "市场",
  "行业",
  "场景",
  "空间",
  "温度",
  "湿度",
  "地面材质",
  "特殊环境特征",
  "通讯方式",
  "洁净度",
  "认证",
  "CE认证",
  "人机协作",
  "安全防护",
  "车型",
  "载具类型",
  "托盘",
  "载重",
  "尺寸限制",
  "取放货高度",
  "叉齿提升高度",
  "顶升行程",
  "料架底部空间高度",
  "最小通道宽度",
  "货物/托盘/平台尺寸",
  "导航方式",
  "运动模型"
]

items.forEach(async item => {
  const response = await fetch('https://deviceapi-dev.seer-group.com/api/select-amr-rule-items', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      "data": {
        "name": item
      }
    })
  })
  if (response.ok) {
    const data = await response.json()
    console.log(`${item}: ok`)
  }
})


