const Data = require('./test.json')
const cbd15 = Data["CBD15-CE"]

const getRules = async () => {
  const response = await fetch('https://deviceapi-dev.seer-group.com/api/select-amr-rule-items?populate=*', {
    method: 'get',
    headers: {
      'Content-Type': 'application/json'
    }
  })
  if (response.ok) {
    const data = await response.json()
    data.data.forEach(d => {
      d.name = d.attributes.name
      delete d.attributes
    })
    return data.data
  }
}

const itemUsed = [
  "市场",
  "行业",
  "场景",
  "空间",
  "地面材质",
  "特殊环境特征",
  "通讯方式",
  "洁净度",
  "认证",
  "CE认证",
  "人机协作",
  "安全防护",
  "车型",
  "载具类型",
  "托盘",
  "导航方式",
  "运动模型"
]

const saveData = async (data) => {
  const response = await fetch('https://deviceapi-dev.seer-group.com/api/select-amr-rules', {
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      data: {
        rules_name: data.id,
        value: data.value
      }
    })
  })
  if (response.ok) {
    return data.data
  }
}
let count = 0

getRules().then(items => {
  const data = []
  itemUsed.forEach(key => {
    const values = new Set()
    cbd15.forEach(item => {
      values.add(item[key])
    })
    values.delete(0)
    const itemWidthId = items.find(r => r.name === key)
    data.push({
      ...itemWidthId,
      values
    })
    values.forEach(v => {
      if (count >= 0) {
        saveData({
          id: itemWidthId.id,
          value: v
        }).then(r=>{
          console.log(`${v}: done`)
        })
        count++
      }
    })
  })
})


