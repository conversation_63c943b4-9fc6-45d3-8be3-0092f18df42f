import { css, html, LitElement, PropertyValues, unsafeCSS } from 'lit'
import { customElement, query, state }                      from 'lit/decorators.js'
import { Application, Sprite, Texture }                     from 'pixi.js'
import { BufferGeometry, Texture as Texture3, Vector2 }     from 'three'
import { Viewport }                                         from 'pixi-viewport'
import bg                                                   from './asserts/bg-png.png?url'

/*
* TODO:
*  3、输出打印尺寸，打印素材
*  4、输入素材有效性校验
* */

export interface CropperConfigs {
  canvasWidth: number
  canvasHeight: number
  scaleX: number
  scaleY: number
  x: number
  y: number
  ox: number
  oy: number
  url: string
}

@customElement('image-cropper')
export class Cropper extends LitElement {
  static styles = css`
    :host {
      width: 100%;
      height: 100%;
    }
    .cropper-container {
      position: relative;
      width: 100%;
      height: 100%;

      canvas {
        width: 100%;
        height: 100%;
        background: url("${unsafeCSS(bg)}") center repeat;
        background-size: 25px;
      }

      device-viewer {
        position: absolute;
        left: 500px;
        top: 0px;
        height: 1000px;
      }
      
      .uv-mask {
        pointer-events: none;
        z-index: 20;
        position: absolute;
        left: 0;
        top: 0;
      }
      
      .cross {
        pointer-events: none;
        z-index: 10;
        position: absolute;
        left: 50%;
        top: 50%;
        &:before {
          content: '';
          position: absolute;
          left: -6px;
          top: 0;
          margin-top: -0.5px;
          width: 12px;
          box-sizing: border-box;
          border: 0.5px solid green;
        }
        &:after {
          content: '';
          position: absolute;
          left: 0;
          top: -6px;
          margin-left: -0.5px;
          height: 12px;
          box-sizing: border-box;
          border: 0.5px solid red;
        }
      }
      
      .rect {
        pointer-events: none;
        position: absolute;
        border: 1px dashed red;
        box-sizing: border-box;
      }
      .axios-x {
        display:none;
        pointer-events: none;
        position: absolute;
        width: 100%;
        height: 0;
        top: 50%;
        margin-top:-0.5px;
        border: 0.5px solid green;
      }
      .axios-y {
        display:none;
        pointer-events: none;
        position: absolute;
        width: 0;
        height: 100%;
        left:50%;
        margin-left:-0.5px;
        border: 0.5px solid red;
      }
    }
  `

  private _domBoundingRect = {
    x: 0,
    y: 0,
    width: 0,
    height: 0
  }

  app = new Application({
    backgroundAlpha: 0,
    autoDensity: true,
    resolution: 2
  })

  viewport = new Viewport({
    events: this.app.renderer.events,
    worldWidth: 250,
    worldHeight: 250
  })

  imageSize = {
    width: 0,
    height: 0
  }

  // 3D 物体中 logo 占位的真实尺寸
  logoSize = {
    width: 0,
    height: 0
  }

  get supports() {
    return [
      'png',
      'jpg',
      'jpeg',
      'svg',
      'webp'
    ]
  }

  private _imageUrl = ''
  private _canvasScale = 1

  texture = new Texture3(this.app.view as HTMLCanvasElement)

  private _timer = 0

  private _sprite: Sprite | undefined

  private _loading = false

  get loading() {
    return this._loading
  }

  get canvas() {
    return this.app.view
  }

  @query('.cropper-container')
  container: HTMLDivElement | undefined
  @query('.rect')
  boundRect: HTMLDivElement | undefined
  @query('.axios-x')
  axiosX: HTMLDivElement | undefined
  @query('.axios-y')
  axiosY: HTMLDivElement | undefined

  @state()
  pathD = ''

  get imageScale() {
    if (!this._sprite) {
      return { x: 0, y: 0 }
    }
    const { screenWidth, screenHeight } = this.viewport
    const scale = this.viewport.scale.x
    const spriteScreenWidth = this._sprite.width * scale / 2
    const spriteScreenHeight = this._sprite.height * scale / 2

    const x = (this._domBoundingRect.width / spriteScreenWidth) * (spriteScreenWidth / screenWidth)
    const y = (this._domBoundingRect.height / spriteScreenHeight) * (spriteScreenHeight / screenHeight)
    return { x, y }
  }

  get imageRealSize() {
    // 获取图片物理尺寸
    const { x: sx, y: sy } = this.imageScale
    let { width, height } = this.logoSize
    const uvSize = Math.max(width, height)
    width = uvSize * sx
    height = uvSize * sy
    return { width, height }
  }

  getConfigs = (): CropperConfigs => {
    const { screenWidth, screenHeight } = this.viewport

    if (!this._sprite) {
      return {
        canvasWidth: screenWidth,
        canvasHeight: screenHeight,
        scaleX: 1,
        scaleY: 1,
        ox: 0,
        oy: 0,
        x: 0,
        y: 0,
        url: this._imageUrl
      }
    }

    const sx = screenWidth / this._sprite.width
    const sy = screenHeight / this._sprite.height
    // 画布 scale
    const scaleX = this.viewport.scale.x / sx
    const scaleY = this.viewport.scale.x / sy

    const gp = this._sprite.toGlobal(new Vector2(-this._sprite.width / 2, -this._sprite.height / 2))
    const ox = -gp.x / (this._sprite.width * this.viewport.scale.x)
    const oy = -gp.y / (this._sprite.height * this.viewport.scale.y)

    return {
      canvasWidth: screenWidth,
      canvasHeight: screenHeight,
      scaleX,
      scaleY,
      ox: ox,
      oy: oy,
      x: this.viewport.position.x,
      y: this.viewport.position.y,
      url: this._imageUrl
    }
  }

  constructor() {
    super()

    this.app.stage.addChild(this.viewport)
    this.viewport
      .drag({})
      .pinch()
      .wheel()
      .decelerate({
        friction: 0,
        bounce: 1
      })
      .on('zoomed-end', this._onChanged)
      .on('moved-end', this._onChanged);
    (this.app.view as HTMLCanvasElement).addEventListener('wheel', this._wheelEvent, { passive: false })
  }

  private _onChanged = async () => {
    if (!this.imageSize.width || !this.imageSize.height) return
    if (!this.app.view.toDataURL) return
    const image = this.app.view.toDataURL('image/png', 0.75)

    // TODO: canvas 兼容性处理
    //@ts-ignore
    const gl = this.app.renderer.gl

    const { screenWidth, screenHeight } = this.viewport

    if (this._timer) {
      clearTimeout(this._timer)
    }

    if (gl) {
      const width = this.app.renderer.width
      const height = this.app.renderer.height
      const pixels = new Uint8Array(width * height * 4)
      gl.readPixels(0, 0, width, height, gl.RGBA, gl.UNSIGNED_BYTE, pixels)
      let minX = width // 初始假设为最大可能值
      let maxX = 0     // 初始假设为最小可能值
      let minY = height // 同理
      let maxY = 0     // 同理
      for (let y = 0; y < height; y++) {
        for (let x = 0; x < width; x++) {
          const index = (y * width + x) * 4
          const alpha = pixels[index + 3]  // Alpha 通道
          if (alpha > 0) {  // 如果当前像素不透明
            minX = Math.min(minX, x)
            maxX = Math.max(maxX, x)
            minY = Math.min(minY, y)
            maxY = Math.max(maxY, y)
          }
        }
      }
      // @ts-ignore
      const canvasRect = this.app.renderer.view.getBoundingClientRect()

      const boundingRect = {
        x: minX,
        y: minY,
        width: maxX - minX + 1,
        height: maxY - minY + 1
      }

      if (minX > maxX || minY > maxY) {
        console.log('没有非透明区域')
      }
      this.axiosX && (this.axiosX.style.display = 'none')
      this.axiosY && (this.axiosY.style.display = 'none')
      const moveLimit = 2
      const halfWidth = screenWidth / 2
      const halfHeight = screenHeight / 2

      if (canvasRect) {
        const domBoundingRect = {
          x: boundingRect.x * (canvasRect.width / this.app.renderer.width),
          y: (this.app.renderer.height - boundingRect.y - boundingRect.height) * (canvasRect.height / this.app.renderer.height),
          width: boundingRect.width * (canvasRect.width / this.app.renderer.width),
          height: boundingRect.height * (canvasRect.height / this.app.renderer.height)
        }
        this._domBoundingRect = domBoundingRect
        if (this.boundRect) {
          this.boundRect.style.left = `${domBoundingRect.x}px`
          this.boundRect.style.top = `${domBoundingRect.y}px`
          this.boundRect.style.width = `${domBoundingRect.width}px`
          this.boundRect.style.height = `${domBoundingRect.height}px`
        }
        const bx = domBoundingRect.x + domBoundingRect.width / 2
        const by = domBoundingRect.y + domBoundingRect.height / 2

        const offsetX = halfWidth - bx
        const offsetY = halfHeight - by
        if (Math.abs(offsetX) < moveLimit) {
          this.viewport.position.x += offsetX
          this.axiosY && (this.axiosY.style.display = 'block')
        }
        if (Math.abs(offsetY) < moveLimit) {
          this.viewport.position.y += offsetY
          this.axiosX && (this.axiosX.style.display = 'block')
        }
      } else {
        if (Math.abs(this.viewport.position.x - halfWidth) < moveLimit) {
          this.viewport.position.x = halfWidth
          this.axiosY && (this.axiosY.style.display = 'block')
        }
        if (Math.abs(this.viewport.position.y - halfHeight) < moveLimit) {
          this.viewport.position.y = halfHeight
          this.axiosX && (this.axiosX.style.display = 'block')
        }
      }

      this._timer = window.setTimeout(() => {
        this.axiosX && (this.axiosX.style.display = 'none')
        this.axiosY && (this.axiosY.style.display = 'none')
      }, 600)
    }

    this.dispatchEvent(new CustomEvent('image-change', {
      detail: { image },
      bubbles: true, // 让事件冒泡
      composed: true // 允许事件穿越 Shadow DOM 边界
    }))
  }

  private _wheelEvent = (event: Event) => {
    event.preventDefault()
  }

  resize(width: number, height: number) {
    this.app.renderer.resize(width, height)
    this.viewport.resize(width, height)
  }

  protected firstUpdated(_changedProperties: PropertyValues) {
    super.firstUpdated(_changedProperties)

    if (!this.container) return
    this.container?.append(this.app.view as HTMLCanvasElement)
    this.app.resizeTo = this.container
  }

  setUV(geometry: BufferGeometry) {
    const { screenWidth, screenHeight } = this.viewport
    // 获取 UV 属性和顶点索引
    const uvAttribute = geometry.attributes.uv
    const indexAttribute = geometry.index!
    if (!indexAttribute) {
      this.pathD = ''
      return
    }

    // 创建一个数组来存储每个面的 UV 坐标
    let faces: number[][] = []

    // 按顺序提取每个面的 UV 坐标
    for (let i = 0; i < indexAttribute.count; i += 3) {
      const index1 = indexAttribute.getX(i)
      const index2 = indexAttribute.getX(i + 1)
      const index3 = indexAttribute.getX(i + 2)

      const uv1 = [uvAttribute.getX(index1) * screenWidth, (uvAttribute.getY(index1)) * screenHeight]
      const uv2 = [uvAttribute.getX(index2) * screenWidth, (uvAttribute.getY(index2)) * screenHeight]
      const uv3 = [uvAttribute.getX(index3) * screenWidth, (uvAttribute.getY(index3)) * screenHeight]

      faces.push(uv1, uv2, uv3)
    }

    // 生成 SVG 路径数据
    let pathD = ''
    faces.forEach((uv, i) => {
      if (i % 3 === 0) {
        pathD += `M${uv[0]},${uv[1]} `
      } else {
        pathD += `L${uv[0]},${uv[1]} `
      }
      if (i % 3 === 2) {
        pathD += 'Z '
      }
    })
    faces = []
    this.pathD = pathD
  }

  async add(url: string, logoSize: { width: number, height: number }) {
    this.clear()

    this._imageUrl = url
    this.logoSize = logoSize
    this._loading = true
    const texture = await Texture.fromURL(url)
    this._sprite = new Sprite(texture)
    this.viewport.addChild(this._sprite)

    this.imageSize.width = this._sprite.texture.width
    this.imageSize.height = this._sprite.texture.height
    this.fit()
    this._loading = false
  }

  clear() {
    this._sprite?.removeAllListeners()
    this._sprite?.removeFromParent()
    this._sprite?.texture.destroy(true)
    this._sprite?.destroy(true)
    this._sprite = undefined
    if (this.boundRect) {
      this.boundRect.style.left = `0px`
      this.boundRect.style.top = `0px`
      this.boundRect.style.width = `0px`
      this.boundRect.style.height = `0px`
    }
  }

  updateConfigs = async ({
                           canvasWidth,
                           x,
                           y,
                           scaleX,
                           url
                         }: CropperConfigs) => {
    const { screenWidth } = this.viewport
    this._canvasScale = screenWidth / canvasWidth

    await this.add(url, this.logoSize)
    const scale = scaleX * screenWidth / (this._sprite?.width || 1)

    this.viewport.scale.set(scale * this._canvasScale)
    this.viewport.position.set(x * this._canvasScale, y * this._canvasScale)
  }

  fit() {
    if (!this._sprite) return
    this.viewport.scale.set(1, 1)
    this._sprite.anchor.set(0.5, 0.5)
    this.viewport.moveCenter(this._sprite.position.x, this._sprite.position.y)
    const viewportWidth = this.viewport.screenWidth
    const viewportHeight = this.viewport.screenHeight
    const scaleX = this._sprite.width / viewportWidth
    const scaleY = this._sprite.height / viewportHeight

    const scale = Math.max(scaleX, scaleY)
    this.viewport.scale.set(1 / scale, 1 / scale)
  }

  disconnectedCallback() {
    super.disconnectedCallback()
    this.clear()
    this.viewport.destroy({
      children: true,
      texture: true,
      baseTexture: true
    })
    this.viewport.off('zoomed')
    this.viewport.off('moved')
    this.viewport.off('moved-end');
    (this.app.view as HTMLCanvasElement).removeEventListener('wheel', this._wheelEvent)
    this.app.destroy(true)
  }

  protected render(): unknown {
    const { screenWidth, screenHeight } = this.viewport
    return html`
      <div class="cropper-container">
        <div class="rect"></div>
        <div class="axios-x"></div>
        <div class="axios-y"></div>
        <div class="cross"></div>
        <svg class="uv-mask" viewBox="0 0 ${screenWidth} ${screenHeight}">
          <path d=${this.pathD} stroke-width="1" stroke="#898989" stroke-opacity="1" fill="none"></path>
        </svg>
      </div>
    `
  }
}
