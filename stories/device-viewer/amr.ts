/*
* AMR 类别支持
* 1、叉车类 Done
* 2、顶升类 Done
* 3、料箱类 Done
* 4、滚筒类
* 5、非标上装类
* 6、复合类
* 7、清洁机器人 Done
* 8、服务机器人
* */


import { Color, EdgesGeometry, Group, LineBasicMaterial, Mesh, MeshBasicMaterial, MeshStandardMaterial, Object3D, TextureLoader, Vector2, Vector3 } from 'three'
import { LineSegments2 } from 'three/examples/jsm/lines/LineSegments2.js'
import { LineSegmentsGeometry } from 'three/examples/jsm/lines/LineSegmentsGeometry.js'
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial.js'
import TWEEN, { Tween }                                                                        from '@tweenjs/tween.js'
import { Scene3D, Skin, Slot, SLOT_EVENTS }                                                    from './scene'
import { gltfLoader, SGLBLoader }                                                              from '../sglb'
import { Outerline }                                                                           from './outerline'
import {
  Mechanism
}                                                                                              from './mechanism/mechanism'
import { clearThreeObjectMesh, generateLogoSize, getVector3FromArray, ObjectSize }             from './utils'
import { Dimension }                                                                           from './dimension'
import {
  SizeBox
}                                                                                              from './dimension/size-box'
import { GroupRoller }                                                                         from './group-roller'
import {
  AMRAssembly
}                                                                                              from '@/device-viewer/assembly'
import EventEmitter                                                                            from 'eventemitter3'
import { imageBitmapToUrl }                                                                    from '../utils'
import { CropperConfigs }                                                                      from '@/cropper'
import { GoodsBox }                                                                            from './goods-box'
import {
  Logo
}                                                                                              from '@/device-viewer/logo'
import {
  Points,
  PointsMaterial,
  BufferGeometry,
  Float32BufferAttribute,
  AdditiveBlending
} from 'three'
import {
  mergeObject
}                                                                                              from '@/device-viewer/utils/geometory'

export const textureLoader = new TextureLoader()
textureLoader.crossOrigin = 'anonymous'

export interface AmrProps {
  url?: string,
  showSizeBox: boolean
  showSlots?: boolean
  showController?: boolean
}

export class Amr extends Object3D {

  private _emitter = new EventEmitter()
  addEventListener = this._emitter.addListener.bind(this._emitter)

  private _container = new Group()
  lines = new Group()
  private _app: Scene3D | undefined

  sizeBox: SizeBox = new SizeBox()

  _goodsSize = new ObjectSize(0, 0, 0)

  _slots: Slot[] = []
  _logos: Logo[] = []
  _skins: Skin[] = []

  _levels = new Map<number, AMRAssembly>()

  private _lift: Object3D | undefined
  private _fork: Object3D | undefined
  private _jack: Object3D | undefined
  private _bodyOpacity = 1

  private _masts = new Map<number, Object3D>()

  private _liftMaxHeight = 0
  private _jackMaxHeight = 0
  private _forkMaxHeight = 0

  controllers: Mechanism[] = []
  goods: GoodsBox[] = []

  private _breathLights: any[] = []
  private _outline: Outerline | undefined

  private _shelf: Object3D | undefined
  private _shelves = new Map<number, Object3D>()
  private _shelfMaxLevel = 1

  private _shelveAnimateTimes: number[] = []

  private _url = ''

  private _showController = true
  private _showSlots = true

  private _shapeKeys = new Map<string, number>()

  private _shapeKeyTimer = 0
  private _shapeKeyCache = ''

  private _materialsCache: Map<string, {
    opacity: number
    transparent: boolean,
    metalness: number,
    roughness: number,
    color: any
    mat: MeshStandardMaterial
  }> = new Map()
  opacityCache: undefined | number = undefined

  private _pointCloudMode = false
  private _pointClouds: Points[] = []
  private _originalMeshes: Map<string, { mesh: Mesh, visible: boolean }> = new Map()
  private _pointCloudAnimations: Tween<any>[] = []

  actions: string[] = []

  private _mergedGeometry: BufferGeometry | undefined

  get shapeKeys() {
    return this._shapeKeys
  }

  get size() {
    const size = this.sizeBox.size.getSize(new Vector3())
    return {
      width: size.z,
      length: size.x,
      height: size.y
    }
  }

  get goodsSize() {
    return this._goodsSize
  }

  get showController() {
    return this._showController
  }

  set showController(s: boolean) {
    this._showController = s
    this.controllers.forEach(c => s ? c.show() : c.hide())
  }

  get slots() {
    return this._slots
  }

  get logos() {
    return this._logos
  }

  get skins() {
    return this._skins
  }

  get bodyOpacity() {
    return this._bodyOpacity
  }

  get fork() {
    return this._fork
  }

  get forkHeight() {
    return this._fork?.position.y || 0
  }

  get liftHeight() {
    return this._lift?.position.y || 0
  }

  get jack() {
    return this._jack
  }

  get jackHeight() {
    return this._jack?.position.y || 0
  }

  get lift() {
    return this._lift
  }

  set bodyOpacity(o) {
    this._bodyOpacity = o
    if (o < 1) {
      this._materialsCache.forEach(m => {
        m.mat.transparent = true
        m.mat.opacity = o
        m.mat.metalness = 0
        m.mat.roughness = 1
        m.mat.color = new Color('#3859be')
        m.mat.needsUpdate = true
      })
    } else {
      this._materialsCache.forEach(m => {
        m.mat.transparent = m.transparent
        m.mat.opacity = m.opacity
        m.mat.metalness = m.metalness
        m.mat.roughness = m.roughness
        m.mat.color = m.color
        m.mat.needsUpdate = true
      })
    }
  }

  constructor(private _props: AmrProps) {
    super()

    this._url = _props.url || ''
    if (this._url) {
      this.load(this._url).then()
    }
    this.add(this._container, this.lines)
    if (this._props.showSizeBox) {
      this.add(this.sizeBox)
    }

    this.showController = typeof _props.showController === 'boolean' ? _props.showController : true
    this._showSlots = typeof _props.showSlots === 'boolean' ? _props.showSlots : true
  }

  init(app: Scene3D) {
    this._app = app
    this.sizeBox.init(app)

    this.addEventListener('shape-key-changed', (key, value) => {
      this._slots.forEach(s => s.shapeKeyChanged(key, value))
      if (this.sizeBox.follow === key) {
        this.sizeBox.generate(this._container, value)
        if (this.sizeBox.originOffset) {
          this._container.traverse(m => {
            if (m instanceof Mesh && !m.userData.isDevice && !m.userData.isBracket) {
              m.geometry.translate(this.sizeBox.originOffset - this.sizeBox.translateCache, 0, 0)
            }
            if (m instanceof Slot && !m.props.slot.userData.ignoreOriginChanged) {
              if (m.parent) {
                m.parent.position.add(new Vector3(this.sizeBox.originOffset - this.sizeBox.translateCache, 0, 0))
              }
            }
          })
          this.sizeBox.translateCache = this.sizeBox.originOffset
          this.sizeBox.generate(this._container)
        }
      } else {
        this.sizeBox.generate(this._container)
      }

      if (['width', 'length', 'height'].includes(key)) {
        this._emitter.emit('size-changed', key, value)
      }
    })
  }

  async load(url: string, type = '') {
    this._url = url

    if (!this.name) {
      this.name = url.substring(url.lastIndexOf('/') + 1, url.lastIndexOf('.'))
    }

    if (!url.startsWith('blob') && !url.endsWith('.sglb')) {
      // return console.error('Not valid amr file url.')
    }
    let glb
    if (type === 'glb' || url.endsWith('.glb')) {
      let r = await gltfLoader.loadAsync(url)
      glb = r.scene
    } else {
      const sglbLoader = new SGLBLoader({ useCache: true })
      const r = await sglbLoader.loadAsync(url)
      if (!r.glb) return
      glb = r.glb
    }
    this._generateAmr(glb.clone(true))

    glb = null

    this._emitter.emit('amr-loaded')
  }

  changeSkin(skin: Skin, value: string) {
    if (!skin) return
    skin.change(value)
    if (this._app && this._app.usePathTracing) {
      this._app.sampleCount = 0
      if (this._app.usePathTracing) {
        this._app.pathTracer.pausePathTracing = false
        this._app.pathTracer.updateMaterials()
      }
    }
  }

  changeLogo(logo: Logo, configs: CropperConfigs) {
    logo.change(configs.url, new Vector2(configs.scaleX, configs.scaleY), new Vector2(
      configs.ox,
      configs.oy
    ))
  }

  setShapeKey(key: string, value: number) {
    if (this._shapeKeyTimer && key === this._shapeKeyCache) {
      clearTimeout(this._shapeKeyTimer)
    }
    this._shapeKeyCache = key
    this._shapeKeyTimer = window.setTimeout(() => {
      this._container.traverse(node => {
        // @ts-ignore
        if (!Object.keys(node.morphTargetDictionary || {}).includes(key)) return

        let val = value
        if (node.userData[`origin_${key}`]) {
          val -= node.userData[`origin_${key}`]
        }
        // @ts-ignore
        const index = node.morphTargetDictionary[key]
        // @ts-ignore
        node.morphTargetInfluences && (node.morphTargetInfluences[index] = val)

        if (node instanceof Mesh) {
          node.geometry.translate(0, 0, 0.00001)
        }
      })
      this._emitter.emit('shape-key-changed', key, value)
    }, 100)
  }

  private _generateAmr(scene: Object3D) {
    this._mergedGeometry = mergeObject(scene)

    scene.traverse(m => {
      if (m.userData.isShelf) {
        this._shelf = m
      }
      if (m.userData.isFork) {
        this._fork = m
      }
      if (m.userData.isMast && m.userData.mastLevel) {
        this._masts.set(m.userData.mastLevel, m)
      }
      if (m.userData.isJack) {
        this._jack = m
      }
      if (m.userData.isLift) {
        this._lift = m
      }

      // @ts-ignore
      Object.keys(m.morphTargetDictionary || {}).forEach(key => {
        // @ts-ignore
        let val = m.morphTargetInfluences[m.morphTargetDictionary[key]]
        if (m.userData[`origin_${key}`]) {
          val += m.userData[`origin_${key}`]
        }
        if (!this._shapeKeys.has(key)) {
          this._shapeKeys.set(key, val)
        }
      })
    })
    scene.traverse(m => {
      if (m instanceof Mesh) {
        m.castShadow = true
        m.receiveShadow = true
        if (m.material) {
          if (Array.isArray(m.material)) {
            m.material.forEach(m => {
              if (m.userData.changeColor) {
                if (!this.skins.find(r => r.name === m.userData.name)) {
                  this.skins.push(new Skin({
                    name: m.userData.name,
                    target: m
                  }))
                }
              }
            })
          } else {
            if (m.material.userData.isBreathLight && m.material.emissiveIntensity) {
              // 设置发光颜色（必须设置，否则bloom不会生效）
              if (!m.material.emissive) {
                m.material.emissive = new Color(0xffffff)  // 白色发光
              }
              m.material.emissiveIntensity = 1
              m.material.toneMapped = false
              const t = new TWEEN.Tween(m.material)
                .to({
                  emissiveIntensity: 0.2  // 最低也保持在0.8，确保有一定的发光效果
                })
                .easing(TWEEN.Easing.Quintic.InOut)
                .duration(1000)
                .start()
                .yoyo(true)
                .repeat(Infinity)

              this._breathLights.push(t)
            }
            if (m.material.userData.changeColor) {
              if (!this.skins.find(r => r.name === m.material.userData.name)) {
                this.skins.push(new Skin({
                  name: m.material.userData.name,
                  target: m.material
                }))
              }
            }
          }
        }

        if (m.material) {
          if (Array.isArray(m.material)) {
            const mats = m.material as MeshStandardMaterial[]
            mats.forEach(mat => {
              if (mat.userData.isLOGO) {
                m.material.transparent = true
                let imageUrl
                const imageSize = {
                  width: 0,
                  height: 0
                }
                if (m.material.map && m.material.map.image) {
                  imageSize.width = m.material.map.image.width
                  imageSize.height = m.material.map.image.height
                  imageUrl = imageBitmapToUrl(m.material.map.image)
                }
                const logo = new Logo({
                  name: mat.userData.name,
                  target: m,
                  size: generateLogoSize(m),
                  imageSize,
                  imageUrl
                })
                this._logos.push(logo)
              }
              if (mat.userData.isTransparent) {
                mat.transparent = true
              }
              this._materialsCache.set(mat.uuid, {
                opacity: mat.opacity,
                transparent: mat.transparent,
                roughness: mat.roughness,
                metalness: mat.metalness,
                color: mat.color,
                mat
              })
            })
          } else {
            const mat = m.material as MeshStandardMaterial
            if (mat.userData.isLOGO) {
              m.material.transparent = true
              let imageUrl
              const imageSize = {
                width: 0,
                height: 0
              }
              if (m.material.map && m.material.map.image) {
                imageSize.width = m.material.map.image.width
                imageSize.height = m.material.map.image.height
                imageUrl = imageBitmapToUrl(m.material.map.image)
              }
              const logo = new Logo({
                name: mat.userData.name,
                target: m,
                size: generateLogoSize(m),
                imageSize,
                imageUrl
              })
              this._logos.push(logo)
            }
            if (mat.userData.isTransparent) {
              mat.transparent = true
            }
            this._materialsCache.set(mat.uuid, {
              opacity: mat.opacity,
              transparent: mat.transparent,
              roughness: mat.roughness,
              metalness: mat.metalness,
              color: mat.color,
              mat
            })
          }
        }
      }

      this.generateController(m, scene)
      this.generateDimension(m, scene)

      const slot = this.generateSlot(m, scene)
      if (slot) {
        this._slots.push(slot)
        // m.add(slot)
      }
      if (m.userData.isOrigin) {
        const keys = Object.keys(m.userData)
        keys.forEach(item => {
          if (item.startsWith('follow_')) {
            const key = item.replace('follow_', '')
            if (this.shapeKeys.has(key)) {
              this.sizeBox.follow = key
              this.sizeBox.followOrigin = this._shapeKeys.get(key) || 0
              this.sizeBox.followScale = m.userData[item]
            }
          }
        })
      }
    })

    this.slots.forEach(s => {
      if (s.props.mirrorTarget) {
        s.mirrorTarget = this.slots.find(t => t.props.slot.name === s.props.mirrorTarget)
      }
    })

    scene.updateMatrixWorld(true)
    this._container.add(scene)
    this.sizeBox.generate(scene)
    this.sizeBox.show()

    // this.setShapeKey('hand', 1)
    // this.setShelfLevel()

  }

  // 设置长宽高，针对可配置的车体生效
  setHeight(height: number) {
    this.setShapeKey('height', height)
  }

  setWeight(width: number) {
    this.setShapeKey('width', width)
  }

  setLength(length: number) {
    this.setShapeKey('length', length)
  }

  limitValue(v: number) {
    if (this.name === 'SFL-CDD14') {
      if (v > 1.6) {
        this.setShapeKey('mast', 1)
      } else {
        this.setShapeKey('mast', 0)
      }
    }
    if (['SFL-CDD20', 'SFL-CDD20-Y'].includes(this.name)) {
      if (v > 1.6) {
        this.setShapeKey('mast', 1)
      } else {
        this.setShapeKey('mast', 0)
      }
    }
    if (this.name === 'SFL-CDD15') {
      if (v > 2.43) {
        this.setShapeKey('mast', 1)
      } else {
        this.setShapeKey('mast', 0)
      }
    }
    if (['SFL-CPD20-Y', 'SFL-CPD30-Y'].includes(this.name)) {
      if (v > 3.5) {
        this.setShapeKey('mast', 2)
      } else if (v > 3) {
        this.setShapeKey('mast', 1)
      } else {
        this.setShapeKey('mast', 0)
      }
    }
  }

  setForkHeight(height: number) {
    if (!this._fork) return
    if (height > this._forkMaxHeight) {
      height = this._forkMaxHeight
    }

    this._fork.position.y = height
    if (this._masts.size === 1) {
      const mast = this._masts.get(2)
      const startHeight = mast?.userData.startHeight || 0.1
      mast && (mast.position.y = (height - startHeight) * 0.5)
    }
  }

  setForkMaxHeight(height: number) {
    if (!this._fork) return
    this._forkMaxHeight = height
    this.limitValue(height)
    this.controllers.forEach(m => {
      if (m.name.toLowerCase().includes('fork')) {
        m.props.max = height
      }
    })
    if (this._fork && this.forkHeight > this._forkMaxHeight) {
      this.setForkHeight(height)
    }
  }

  setLiftHeight(height: number) {
    if (!this._lift) return
    if (height > this._liftMaxHeight) {
      height = this._liftMaxHeight
    }

    this._lift.position.y = height
  }

  setLiftMaxHeight(height: number) {
    if (!this._lift) return
    this._liftMaxHeight = height
    this.limitValue(height)
    this.controllers.forEach(m => {
      if (m.name.toLowerCase().includes('lift')) {
        m.props.max = height
      }
    })
    if (this.liftHeight > this._liftMaxHeight) {
      this.setLiftHeight(height)
    }

    // 固定高度差
    this.setHeight(height + 0.46)
    // this.setHeight(height + 1)
  }

  setJackMaxHeight(height: number) {
    if (!this._jack) return
    this._jackMaxHeight = height
    this.limitValue(height)
    this.controllers.forEach(m => {
      if (m.name.toLowerCase().includes('jack')) {
        m.props.max = height
      }
    })
    if (this.jackHeight > this._jackMaxHeight) {
      this.setJackHeight(height)
    }
  }

  setJackHeight(height: number) {
    if (!this._jack) return
    if (height > this._jackMaxHeight) {
      height = this._jackMaxHeight
    }
    this._jack.position.y = height
  }

  setLevel(level: number) {
    this._levels.forEach(item => {
      item.destroy()
    })
    this._levels.clear()
    for (let i = 0; i < level; i++) {
      const t = new GroupRoller()
      t.position.y = 0.112 + i * 0.6
      t.addEventListener('size-change', (size) => {
        setTimeout(() => {
          this.setShapeKey('length', size.length)
          this.setShapeKey('width', size.width)
          // this.setShapeKey('height', size.height + 0.135)
        }, 20)
      })
      if (this.lift) {
        this.lift.add(t)
      } else {
        this.add(t)
      }
      this._levels.set(i, t)
    }

    setTimeout(() => {
      this.setGoodsSize(this._goodsSize)
    }, 100)
  }

  setGoodsSize(size: {
    length: number,
    width: number,
    height: number
  }) {
    this._goodsSize.length = size.length
    this._goodsSize.width = size.width
    this._goodsSize.height = size.height
    this._levels.forEach(item => {
      (item as GroupRoller).setGoodsSize(size)
    })

    this.setShelfLevel(this._shelfMaxLevel)
  }

  // 设置料箱料斗层数
  setShelfLevel(levelMax = 3) {
    if (!this._shelf) return
    this._shelfMaxLevel = levelMax

    const levelOffset = this._goodsSize.height + 0.12

    //Clear all
    this._shelves.delete(0)
    this._shelves.forEach(node => {
      clearThreeObjectMesh(node)
      if (node.parent) {
        node.removeFromParent()
      }
    })
    this.goods = []
    this._shelves.clear()
    this._shelves.set(0, this._shelf)

    if (this._shelveAnimateTimes.length) {
      this._shelveAnimateTimes.forEach(t => window.clearTimeout(t))
    }
    this._shelveAnimateTimes = []

    for (let i = 1; i < levelMax; i++) {
      const t = window.setTimeout(() => {
        if (!this._shelf) return
        const shelf = this._shelf.clone(true)
        shelf.position.y += levelOffset * i
        this._shelves.set(i, shelf)
        this._shelf.parent?.add(shelf)

        let container: Object3D | undefined
        shelf.traverse(m => {
          if (m.userData.isShelfContainer) {
            container = m
          }
        })
        if (container) {
          const goodsBox = new GoodsBox(this._goodsSize.length, this._goodsSize.width, this._goodsSize.height)
          container.add(goodsBox)
          this.goods.push(goodsBox)
          if (i === 1) {
            const goodsBox = new GoodsBox(this._goodsSize.length, this._goodsSize.width, this._goodsSize.height)
            goodsBox.position.y -= levelOffset
            container.add(goodsBox)
            this.goods.push(goodsBox)
          }
        }

        shelf.scale.set(0.5, 1, 0)
        new Tween(shelf.scale)
          .to({ x: 1, y: 1, z: 1 })
          .duration(100)
          .start()
      }, 100 + i * 90)
      this._shelveAnimateTimes.push(t)
    }
  }

  generateOuterLine() {
    this.traverse(m => {
      if (m.userData.isOuterObject) {
        const radius = this.sizeBox.radius
        if (this._outline) {
          this._outline.update(radius)
        } else {
          this._outline = new Outerline(radius, 0)
          this.add(this._outline)
        }
      }
    })
  }

  generateController(m: Object3D, amr: Object3D) {
    if (!this.showController) return
    if (m.userData.isForkController && m.userData.target) {
      const targetName = m.userData.target.name
      const { min, max } = m.userData
      let target: Object3D | undefined
      amr.traverse(o => {
        if (o.name === targetName) {
          target = o
        }
      })
      if (!target) return
      const mechanism = new Mechanism({
        name: '',
        target: targetName,
        default: 0,
        min,
        max
      }, target)
      m.add(mechanism)
    } else if (m.userData.isController && m.userData.target) {
      const targetName = m.userData.target.name
      const { min, max } = m.userData
      let target: Object3D | undefined

      amr.traverse(o => {
        if (o.name === targetName) {
          target = o
        }
      })
      if (!target) return

      const mechanism = new Mechanism({
        name: '',
        userData: m.userData,
        target: m.userData.key,
        default: 0,
        min: min || 0,
        max: max || 1
      }, target)
      m.add(mechanism)
      this.controllers.push(mechanism)
      if (target.userData.isFork || target.userData.isLift || target.userData.isJack) {
        this._forkMaxHeight = max || 1
        this._liftMaxHeight = max || 1
        this._jackMaxHeight = max || 1
        mechanism.addListener('change', (height: number) => {
          this.setForkHeight(height)
          this.setLiftHeight(height)
          this.setJackHeight(height)
        })
      }
    }
  }

  generateDimension(m: Object3D, amr: Object3D) {
    // const l3 = new Line3(new Vector3(), new Vector3(0.949, 0, 0.497753))
    // this.scene.add(l3)
    if (m.userData.isDimension) {
      const divider = m.userData.divider
      const startOrigin = getVector3FromArray(m.userData.startOrigin)
      const axios = m.userData.axios
      let start, end
      if (axios === 'x') {
        start = new Vector3(m.userData.start - m.position.x, 0, 0)
        end = start.clone().setX(m.userData.end - m.position.x)
      } else if (axios === 'z') {
        start = new Vector3(0, m.userData.start - m.position.y, 0)
        end = start.clone().setY(m.userData.end - m.position.y)
      } else {
        start = new Vector3(0, 0, m.userData.start - m.position.z)
        end = start.clone().setZ(m.userData.end - m.position.z)
      }

      const keys = Object.keys(m.userData)
      let targetNameList = keys.flatMap(r => r.startsWith('target') ? m.userData[r].name : [])
      if (!targetNameList.length) return
      const d = new Dimension({
        start, end,
        startOrigin,
        axios,
        divider
      })
      m.add(d)
    }
  }

  generateSlot(m: Object3D, scene: Object3D) {
    // 整理插槽数据
    if (m.userData.isSlot) {
      const name = m.userData.name
      const isMirror = m.userData.isMirror
      const mirrorTarget = m.userData.mirrorSlot?.name

      if (name) {
        const holeTargetName = m.userData.holeTarget && m.userData.holeTarget.name
        let holeTarget
        if (holeTargetName) {
          scene.traverse(node => {
            if (node.name === holeTargetName) {
              holeTarget = node
            }
          })
        }

        const follows = new Map<string, {
          scale: number,
          origin: number
        }>()
        Object.keys(m.userData).forEach(item => {
          if (item.startsWith('follow_')) {
            const key = item.split('_')[1]
            if (key) {
              let val = m.userData[item]
              follows.set(key, {
                scale: val,
                origin: this.shapeKeys.get(key) || 0
              })
            }
          }
        })

        const slot = new Slot({
          name,
          follows,
          slot: m,
          show: this._showSlots,
          isMirror,
          mirrorTarget
        })

        slot.holeTarget = holeTarget

        slot.addEventListener(SLOT_EVENTS.ON_DROP, r => {
          if (this._app && this._app.props.useSound) {
            this._app.sound.play()
          }
        })

        slot.addEventListener(SLOT_EVENTS.ON_BEFORE_SELECTED, r => {
          this._slots.forEach(s => {
            if (s.selected) {
              s.selected = false
            }
          })
        })
        slot.addEventListener(SLOT_EVENTS.ON_ERROR_STATE_CHANGE, hasError => {
          if (!this._app) return
          this._app._outlinePassError.selectedObjects = this._app._outlinePassError.selectedObjects.filter(r => !!r.parent)
          if (hasError) {
            if (slot.device) {
              const exist = this._app._outlinePassError.selectedObjects.find(r => r.parent == slot.device?.parent)
              if (exist) return
              this._app._outlinePassError.selectedObjects = [
                slot.device,
                ...this._app._outlinePassError.selectedObjects
              ]
            }
          } else {
            if (slot.device) {
              this._app._outlinePassError.selectedObjects =
                this._app._outlinePassError.selectedObjects.filter(r => r.parent != slot.device?.parent)
            }
          }
        })

        return slot
      }
    }
  }

  clean() {
    this._emitter.removeAllListeners()
    if (this._shelveAnimateTimes.length) {
      this._shelveAnimateTimes.forEach(t => window.clearTimeout(t))
    }
    this._shelveAnimateTimes = []

    this.controllers.forEach(d => d.dispose())
    this.controllers = []

    clearThreeObjectMesh(this._container)
    this._levels.forEach(l => l.destroy())
    this._levels.clear()
    this._slots = []
    this._logos = []
    this._skins = []
    this._materialsCache.clear()
    this.opacityCache = undefined
    this._breathLights.forEach(b => b.stop())
    this._breathLights = []
    this.sizeBox.reset()
  }

  destroy() {
    // 清理点云资源
    if (this._pointCloudMode) {
      this._clearPointClouds()
    }

    this.clean()
    this.sizeBox.destroy()
  }

  setPointCloudMode(enabled: boolean) {
    this._pointCloudMode = enabled

    if (enabled) {
      this.sizeBox.hide()
      this.controllers.forEach(c => c.hide())
      this.slots.forEach(s => s.hide())
      // 隐藏原始网格并创建点云
      this._createPointClouds()
    } else {
      this.sizeBox.show()
      this.slots.forEach(s => s.hide())
      this.controllers.forEach(c => c.show())
      // 恢复原始网格并清除点云
      this._clearPointClouds()
    }
  }

  private _createPointClouds() {
    // 清除已有的点云
    this._clearPointClouds()

    // 用于存储所有点的位置和颜色
    const allPoints: number[] = []
    const allColors: number[] = []

    // 点的间距阈值 - 减小一半以增大密度
    const pointSpacing = 0.02 // 原来是0.02，现在密度翻倍
    const maxEdgeLength = 0.05 // 相应减小最大边长阈值

    // 预估点数上限，防止内存溢出
    const MAX_RAW_POINTS = 10000000 // 原始点数上限

    // 计算模型的边界框，用于生成随机初始位置
    const boundingBox = new Vector3()
    const minPoint = new Vector3(Infinity, Infinity, Infinity)
    const maxPoint = new Vector3(-Infinity, -Infinity, -Infinity)

    this._container.traverse((child) => {
      if (child instanceof Mesh && child.geometry && child.visible) {
        // 只处理可见的mesh
        // 保存原始网格状态
        this._originalMeshes.set(child.uuid, {
          mesh: child,
          visible: child.visible
        })

        // 隐藏原始网格
        child.visible = false

        // 更新边界框
        child.geometry.computeBoundingBox()
        const box = child.geometry.boundingBox!.clone()
        box.applyMatrix4(child.matrixWorld)
        minPoint.min(box.min)
        maxPoint.max(box.max)

        // 获取几何体数据
        const geometry = child.geometry
        const positions = geometry.attributes.position
        const indices = geometry.index

        if (positions) {
          // 获取世界变换矩阵
          child.updateMatrixWorld()
          const matrix = child.matrixWorld

          // 如果有索引，按面片处理
          if (indices) {
            const indexArray = indices.array

            // 遍历每个三角形
            for (let i = 0; i < indexArray.length; i += 3) {
              const a = indexArray[i]
              const b = indexArray[i + 1]
              const c = indexArray[i + 2]

              // 获取三角形的三个顶点
              const v1 = new Vector3(
                positions.getX(a),
                positions.getY(a),
                positions.getZ(a)
              ).applyMatrix4(matrix)

              const v2 = new Vector3(
                positions.getX(b),
                positions.getY(b),
                positions.getZ(b)
              ).applyMatrix4(matrix)

              const v3 = new Vector3(
                positions.getX(c),
                positions.getY(c),
                positions.getZ(c)
              ).applyMatrix4(matrix)

              // 在三角形表面生成点
              if (allPoints.length < MAX_RAW_POINTS * 3) {
                this._generatePointsOnTriangle(v1, v2, v3, pointSpacing, maxEdgeLength, allPoints, allColors)
              }
            }
          } else {
            // 如果没有索引，直接处理顶点
            for (let i = 0; i < positions.count; i++) {
              const vertex = new Vector3(
                positions.getX(i),
                positions.getY(i),
                positions.getZ(i)
              ).applyMatrix4(matrix)

              allPoints.push(vertex.x, vertex.y, vertex.z)

              // 生成颜色 - 基于高度的蓝色渐变
              const normalizedY = (vertex.y - minPoint.y) / (maxPoint.y - minPoint.y)
              allColors.push(0, normalizedY * 0.5, 0.5 + normalizedY * 0.5)
            }
          }
        }
      }
    })

    // 去除重复点
    const uniquePoints = this._removeDuplicatePoints(allPoints, allColors, pointSpacing * 0.5)

    // 性能优化：限制最大点数
    const MAX_POINTS = 100000 // 最大10万个点
    if (uniquePoints.positions.length > MAX_POINTS * 3) {
      // 随机采样以减少点数
      const sampleRate = (MAX_POINTS * 3) / uniquePoints.positions.length
      const sampledPositions: number[] = []
      const sampledColors: number[] = []

      for (let i = 0; i < uniquePoints.positions.length; i += 3) {
        if (Math.random() < sampleRate) {
          sampledPositions.push(uniquePoints.positions[i], uniquePoints.positions[i + 1], uniquePoints.positions[i + 2])
          sampledColors.push(uniquePoints.colors[i], uniquePoints.colors[i + 1], uniquePoints.colors[i + 2])
        }
      }

      uniquePoints.positions = sampledPositions
      uniquePoints.colors = sampledColors
    }

    if (uniquePoints.positions.length > 0) {
      // 计算边界范围
      const center = minPoint.clone().add(maxPoint).multiplyScalar(0.5)
      const size = maxPoint.clone().sub(minPoint)
      const maxSize = Math.max(size.x, size.y, size.z)

      // 边界检查：防止除零错误
      if (maxSize === 0 || !isFinite(maxSize)) {
        console.warn('Point cloud generation failed: invalid model bounds')
        return
      }

      // 创建两个位置数组：初始位置（随机）和目标位置
      const initialPositions = new Float32Array(uniquePoints.positions.length)
      const targetPositions = new Float32Array(uniquePoints.positions)

      // 创建初始颜色和目标颜色数组
      const initialColors = new Float32Array(uniquePoints.colors.length)
      const targetColors = new Float32Array(uniquePoints.colors)

      // 生成星云状的初始位置和颜色
      for (let i = 0; i < uniquePoints.positions.length; i += 3) {
        const pointIndex = i / 3

        // 目标位置
        const targetX = targetPositions[i]
        const targetY = targetPositions[i + 1]
        const targetZ = targetPositions[i + 2]

        // 创建星云状分布
        // 使用多个参数创建复杂的分布模式
        const t = pointIndex / (uniquePoints.positions.length / 3)
        const spiralAngle = t * Math.PI * 4 // 螺旋角度

        // 使用概率分布让外围更密集，中心更稀疏
        // 使用平方根函数实现平滑的由外向内的密度递减
        const densityRandom = Math.random()
        const radiusFactor = Math.sqrt(densityRandom) // 使用平方根，分布更平滑

        // 半径使用连续函数，避免空白区域
        // 加入小的偏移量避免中心过于密集
        const minRadius = 0.05 + Math.random() * 0.1 // 最小半径有随机性
        const radius = maxSize * 2.2 * (minRadius + radiusFactor * (1 - minRadius))

        // 螺旋臂
        const armCount = 3
        const armAngle = (pointIndex % armCount) * (Math.PI * 2 / armCount) + spiralAngle

        // 添加噪声使分布更自然
        const noise = Math.random() * 0.4
        const spiralRadius = radius * (1 + noise)

        // 垂直方向的变化 - 根据半径调整高度，外围更分散
        const heightFactor = radiusFactor // 高度随半径变化
        // 边缘点的Y轴分散程度更大
        const edgeSpreadFactor = 0.25 + radiusFactor * 0.2 // 边缘点增加额外的分散
        // 使用随机方向让Y轴上下都有分布
        const verticalDirection = Math.random() < 0.5 ? -1 : 1
        const verticalSpread = verticalDirection * Math.random() * maxSize * edgeSpreadFactor * heightFactor
        const heightNoise = (Math.random() - 0.5) * maxSize * 0.25 * heightFactor

        // 计算初始位置 - 星云状分布
        // 外围粒子有更多的随机偏移，让边缘更蓬松
        const randomOffset = maxSize * 0.3 * (1 + radiusFactor * 0.5)

        // 椭圆形状 - x轴稍微拉伸
        const ellipseX = 1.2
        const ellipseZ = 0.85

        initialPositions[i] = center.x + Math.cos(armAngle) * spiralRadius * ellipseX + (Math.random() - 0.5) * randomOffset
        // Y轴不设置基础偏移，让粒子在正负方向均匀分布
        initialPositions[i + 1] = center.y + verticalSpread + heightNoise
        initialPositions[i + 2] = center.z + Math.sin(armAngle) * spiralRadius * ellipseZ + (Math.random() - 0.5) * randomOffset

        // 生成星云般的初始颜色 - 使用更丰富的色彩
        const colorRandom = Math.random()
        const brightness = Math.random() < 0.1 ? 1.5 : 1.0 // 10%的点更亮，模拟亮星

        // 外围点有更高概率是蓝色系
        const blueBoost = radiusFactor > 0.75 ? 0.7 : (radiusFactor > 0.5 ? 0.4 : 0) // 外围点大幅增加蓝色概率
        const adjustedColorRandom = colorRandom * (1 - blueBoost) // 调整随机值

        // 调整概率，让蓝色系更多，外围更蓝
        if (adjustedColorRandom < 0.7) { // 70%+ 青色系（外围可达95%）
          initialColors[i] = (0.1 + Math.random() * 0.2) * brightness     // R
          initialColors[i + 1] = (0.6 + Math.random() * 0.4) * brightness // G
          initialColors[i + 2] = (0.8 + Math.random() * 0.2) * brightness // B
        } else if (adjustedColorRandom < 0.8) { // 10% 紫色系
          initialColors[i] = (0.6 + Math.random() * 0.4) * brightness     // R
          initialColors[i + 1] = (0.1 + Math.random() * 0.3) * brightness // G
          initialColors[i + 2] = (0.8 + Math.random() * 0.2) * brightness // B
        } else if (adjustedColorRandom < 0.92) { // 12% 粉色系
          initialColors[i] = (0.8 + Math.random() * 0.2) * brightness     // R
          initialColors[i + 1] = (0.2 + Math.random() * 0.3) * brightness // G
          initialColors[i + 2] = (0.6 + Math.random() * 0.3) * brightness // B
        } else { // 8% 橙色系
          initialColors[i] = (0.9 + Math.random() * 0.1) * brightness     // R
          initialColors[i + 1] = (0.4 + Math.random() * 0.3) * brightness // G
          initialColors[i + 2] = (0.1 + Math.random() * 0.2) * brightness // B
        }
      }

      // 创建点云几何体
      const pointGeometry = new BufferGeometry()
      pointGeometry.setAttribute('position', new Float32BufferAttribute(initialPositions, 3))
      pointGeometry.setAttribute('color', new Float32BufferAttribute(initialColors, 3))

      // 创建点云材质 - 星云效果需要更大的初始点
      const pointMaterial = new PointsMaterial({
        size: 0.002, // 与动画初始值保持一致
        color: '#367BF5',
        vertexColors: false,
        opacity: 0.1,
        transparent: false,
        // blending: AdditiveBlending,
        depthWrite: false,
        sizeAttenuation: true
      })

      // 创建点云对象
      const points = new Points(pointGeometry, pointMaterial)
      this.add(points)
      this._pointClouds.push(points)

      // 创建汇聚动画
      const positionAttribute = pointGeometry.attributes.position as Float32BufferAttribute
      const colorAttribute = pointGeometry.attributes.color as Float32BufferAttribute
      const animationData = { progress: 0 }

      // 初始透明度设置为0.3，让星云效果更明显
      pointMaterial.opacity = 0.3

      const tween = new TWEEN.Tween(animationData)
        // .to({ progress: 1 }, 2600) // 2.6秒动画
        .to({ progress: 1 }, 600) // 2.6秒动画
        // .delay(100) // 延迟0.1秒开始，让初始状态稳定
        .easing(TWEEN.Easing.Linear.None) // 线性进度，通过自定义函数控制加速和减速
        .onUpdate(() => {
          const progress = animationData.progress
          const positions = positionAttribute.array as Float32Array
          const colors = colorAttribute.array as Float32Array

          // 动画分为两个阶段：汇聚阶段和旋转阶段
          const convergencePhase = 0.6 // 前60%用于汇聚
          const isConverging = progress < convergencePhase

          // 性能优化：减少不必要的计算
          const needsColorUpdate = progress < 0.9 // 90%后颜色基本稳定

          // 汇聚阶段：加速运动，带螺旋效果
          const convergenceProgress = isConverging ? progress / convergencePhase : 1
          const acceleratedProgress = convergenceProgress * convergenceProgress // 二次加速
          const spiralRotation = acceleratedProgress * Math.PI * 1.25 // 汇聚时旋转450度

          // 旋转阶段：减速运动，纯旋转
          const rotationProgress = isConverging ? 0 : (progress - convergencePhase) / (1 - convergencePhase)
          const deceleratedRotation = 1 - Math.pow(1 - rotationProgress, 3) // 三次减速，更平滑
          const additionalRotation = deceleratedRotation * Math.PI * 0.75 // 额外旋转135度

          // 总旋转量 = 450 + 135 = 585度
          const finalRotation = isConverging ? spiralRotation : spiralRotation + additionalRotation

          // 更新每个点的位置和颜色
          for (let i = 0; i < positions.length; i += 3) {
            // 获取初始位置和目标位置
            const initX = initialPositions[i]
            const initY = initialPositions[i + 1]
            const initZ = initialPositions[i + 2]
            const targetX = targetPositions[i]
            const targetY = targetPositions[i + 1]
            const targetZ = targetPositions[i + 2]

            // 插值计算当前应该在的位置（汇聚阶段使用加速进度）
            const positionProgress = isConverging ? acceleratedProgress : 1
            const currentX = initX + (targetX - initX) * positionProgress
            const currentY = initY + (targetY - initY) * positionProgress
            const currentZ = initZ + (targetZ - initZ) * positionProgress

            // 计算相对于中心的坐标
            const dx = currentX - center.x
            const dz = currentZ - center.z

            // 在汇聚阶段，不应用椭圆变换（保持目标点的原始形状）
            // 只在初始分布时使用椭圆
            if (isConverging) {
              // 汇聚阶段：直接使用实际距离和角度
              const distance = Math.sqrt(dx * dx + dz * dz)
              const angle = Math.atan2(dz, dx)
              const newAngle = angle + finalRotation

              positions[i] = center.x + Math.cos(newAngle) * distance
              positions[i + 1] = currentY
              positions[i + 2] = center.z + Math.sin(newAngle) * distance
            } else {
              // 旋转阶段：目标点已经到位，只需要旋转
              const distance = Math.sqrt(dx * dx + dz * dz)
              const angle = Math.atan2(dz, dx)
              const newAngle = angle + finalRotation

              positions[i] = center.x + Math.cos(newAngle) * distance
              positions[i + 1] = currentY
              positions[i + 2] = center.z + Math.sin(newAngle) * distance
            }

            // 更新颜色
            if (needsColorUpdate) {
              colors[i] = initialColors[i] + (targetColors[i] - initialColors[i]) * progress
              colors[i + 1] = initialColors[i + 1] + (targetColors[i + 1] - initialColors[i + 1]) * progress
              colors[i + 2] = initialColors[i + 2] + (targetColors[i + 2] - initialColors[i + 2]) * progress
            }
          }

          // 更新透明度：到达目标时最亮
          let opacity = 0.3
          if (isConverging) {
            opacity = 0.3 + 0.4 * acceleratedProgress // 汇聚时变亮
          } else {
            opacity = 0.7 - 0.1 * rotationProgress // 旋转时稍微变暗
          }
          pointMaterial.opacity = opacity

          // 更新点的大小：速度最快时稍大
          let size = 0.0015
          if (isConverging) {
            size = 0.0015 - 0.0005 * acceleratedProgress // 汇聚时缩小
          } else {
            size = 0.001 - 0.0002 * rotationProgress // 旋转时继续缩小
          }
          // pointMaterial.size = size

          positionAttribute.needsUpdate = true
          if (needsColorUpdate) {
            colorAttribute.needsUpdate = true
          }
        })
        .onComplete(() => {
          // 动画完成后从动画数组中移除
          const index = this._pointCloudAnimations.indexOf(tween)
          if (index > -1) {
            this._pointCloudAnimations.splice(index, 1)
          }
          const lineMaterial = new LineMaterial({
            color: '#222',
            linewidth: 0.001,
            opacity: 0.1,
            transparent: true,
            depthTest: true,
            depthWrite: false,
          })

          // 创建不可见的深度物体，用于遮挡背面线条
          const depthMaterial = new MeshBasicMaterial({
            colorWrite: true,  // 不写入颜色，只写入深度
            color: '#0000ff',
            depthWrite: false,
            depthTest: true,
            transparent: true,
            opacity: 0.12,  // 添加透明度
            // polygonOffset: true,
            // polygonOffsetFactor: 1,
            // polygonOffsetUnits: 1
          })

          // this._originalMeshes.forEach(t => {
          //   if (t.mesh.geometry) {
          //     const worldGeometry = t.mesh.geometry.clone()
          //     worldGeometry.applyMatrix4(t.mesh.matrixWorld)
          //
          //     const depthMesh = new Mesh(worldGeometry.clone(), depthMaterial)
          //     // depthMesh.scale.setScalar(0.98)  // 内缩2%
          //     depthMesh.renderOrder = 0  // 先渲染深度物体
          //     this.add(depthMesh)
          //
          //     // 生成边缘线条
          //     const edge = new EdgesGeometry(worldGeometry, 12)
          //     const lineGeometry = new LineSegmentsGeometry().fromEdgesGeometry(edge)
          //
          //     const line = new LineSegments2(lineGeometry, lineMaterial)
          //
          //     // 线条在深度物体之后渲染
          //     line.renderOrder = 1
          //
          //     this.add(line)
          //     this.slots.forEach(s=>s.show())
          //     points.visible = false
          //   }

          const depthMesh = new Mesh(this._mergedGeometry, depthMaterial)
          depthMesh.renderOrder = 0  // 先渲染深度物体
          this.lines.add(depthMesh)

          // 生成边缘线条
          const edge = new EdgesGeometry(this._mergedGeometry, 15)
          const lineGeometry = new LineSegmentsGeometry().fromEdgesGeometry(edge)

          const line = new LineSegments2(lineGeometry, lineMaterial)

          // 线条在深度物体之后渲染
          line.renderOrder = 1

          // this.lines.add(line)
          this.slots.forEach(s=>s.show())
          points.visible = false

        })
        .start()

      // 将动画添加到数组中以便管理
      this._pointCloudAnimations.push(tween)
    }
  }

  private _generatePointsOnTriangle(
    v1: Vector3,
    v2: Vector3,
    v3: Vector3,
    spacing: number,
    maxEdgeLength: number,
    points: number[],
    colors: number[]
  ) {
    // 计算三条边的长度
    const edge1Length = v1.distanceTo(v2)
    const edge2Length = v2.distanceTo(v3)
    const edge3Length = v3.distanceTo(v1)

    // 计算需要的细分程度
    const maxLength = Math.max(edge1Length, edge2Length, edge3Length)
    const subdivisions = Math.max(2, Math.ceil(maxLength / spacing))
    // const subdivisions = 0

    // 如果三角形太大，进行细分
    if (maxLength > maxEdgeLength) {
      // 使用重心坐标在三角形表面均匀分布点
      for (let i = 0; i <= subdivisions; i++) {
        for (let j = 0; j <= subdivisions - i; j++) {
          const u = i / subdivisions
          const v = j / subdivisions
          const w = 1 - u - v

          if (w >= 0) {
            // 计算点的位置
            const point = new Vector3()
              .addScaledVector(v1, u)
              .addScaledVector(v2, v)
              .addScaledVector(v3, w)

            points.push(point.x, point.y, point.z)

            // 生成渐变颜色
            const t = (u + v * 0.5) // 用重心坐标生成渐变
            const r = 0.1 + t * 0.4
            const g = 0.6 - t * 0.2
            const b = 0.9 - t * 0.1

            colors.push(0, point.y/5, 1-point.y/1.4)

          }
        }
      }
    } else {
      // 小三角形只添加顶点
      points.push(v1.x, v1.y, v1.z)
      points.push(v2.x, v2.y, v2.z)
      points.push(v3.x, v3.y, v3.z)

              // 添加颜色
      for (let i = 0; i < 3; i++) {
        const t = Math.random()
        const r = 0.2 + t * 0.3
        const g = 0.8 - t * 0.3
        const b = 0.9 + t * 0.1
        // colors.push(r, g, b)
        // colors.push(0.6, 0.8, 1.0)  // 蓝色泛白的光效果
        colors.push(0, 0, 0)  // 蓝色泛白的光效果
      }
    }
  }

  private _removeDuplicatePoints(positions: number[], colors: number[], threshold: number) {
    const uniquePositions: number[] = []
    const uniqueColors: number[] = []
    const processedPoints = new Map<string, boolean>()

    for (let i = 0; i < positions.length; i += 3) {
      const x = positions[i]
      const y = positions[i + 1]
      const z = positions[i + 2]

      // 创建一个基于网格的键来快速查找附近的点
      const gridKey = `${Math.round(x / threshold)}_${Math.round(y / threshold)}_${Math.round(z / threshold)}`

      if (!processedPoints.has(gridKey)) {
        processedPoints.set(gridKey, true)
        uniquePositions.push(x, y, z)
        uniqueColors.push(colors[i], colors[i + 1], colors[i + 2])
      }
    }

    return {
      positions: uniquePositions,
      colors: uniqueColors
    }
  }

  private _clearPointClouds() {
    // 停止所有正在进行的动画
    this._pointCloudAnimations.forEach(tween => {
      tween.stop()
    })
    this._pointCloudAnimations = []

    // 移除所有点云
    this._pointClouds.forEach(points => {
      points.geometry.dispose()
      if (points.material instanceof PointsMaterial) {
        points.material.dispose()
      }
      points.removeFromParent()
    })
    this._pointClouds = []

    // 恢复原始网格的可见性
    this._originalMeshes.forEach(({ mesh, visible }) => {
      mesh.visible = visible
    })
    this._originalMeshes.clear()
  }

  playAction(name: string, reset = true) {
    // This method is not provided in the edit specification,
    // so it will be left as is.
  }

  pauseAction(name: string) {
    // Pause action implementation
    console.log(`Pausing action: ${name}`)
  }
}
