import { Object3D }                                          from 'three'
import { gltfLoader, SGLBLoader }                            from '../sglb'
import { calculateTotalDimensionsUsingVertices, ObjectSize } from './utils'


export class AMRAssembly extends Object3D {

  protected _size = new ObjectSize(0, 0, 0)

  constructor(private _url?: string) {
    super()

    this._load(_url).then()
  }

  loaded(g: any) {

  }

  get size() {
    return this._size
  }

  private async _load(url: string | undefined) {
    this._url = url

    if (url) {
      let glb
      if (url.endsWith('.glb')) {
        let r = await gltfLoader.loadAsync(url)
        glb = r.scene
        if (glb) {
          this.loaded(glb)
        }
      } else {
        const sglbLoader = new SGLBLoader({ useCache: true })
        const r = await sglbLoader.loadAsync(url)
        if (!r.glb) return
        glb = r.glb
        if (glb) {
          this.loaded(glb)
        }
        return glb
      }
    } else {
      return
    }

    const d = calculateTotalDimensionsUsingVertices(this)
    // d.box.getSize(this._size)
  }

  destroy() {

  }
}
