import {
  BoxGeometry,
  EdgesGeometry,
  Group,
  LineSegments,
  Mesh,
  MeshBasicMaterial,
  MeshStandardMaterial,
  Object3D,
  Vector3
}                                           from 'three'
import { AMRAssembly }                      from './assembly'
import Model                                from './asserts/models/belt.glb?url'
import EventEmitter                         from 'eventemitter3'
import { clearThreeObjectMesh, ObjectSize } from './utils'

export interface RollerProps {
  goodsSize: Vector3
}

export class Belt extends AMRAssembly {
  private _goodsWidthGap = 0.01
  private _goodsLengthGap = 0.01

  private _emitter = new EventEmitter()
  addEventListener = this._emitter.addListener.bind(this._emitter)

  private _length = 0

  private _container = new Group()

  private _goodsContainer: Object3D | undefined

  private _goodsSize = new ObjectSize(0, 0, 0)

  private _goods: undefined | Mesh

  constructor(private _props: RollerProps) {
    super(Model)

    this.add(this._container)
  }

  get length() {
    return this._length
  }

  loaded(glb: Object3D) {
    this._container.add(glb.clone(true))
    this._container.traverse(m => {
      if (m.userData.isContainer) {
        this._goodsContainer = m
      }
      const key = 'length'
      // @ts-ignore
      if (Object.keys(m.morphTargetDictionary || {}).includes(key)) {
        // @ts-ignore
        const index = m.morphTargetDictionary[key]
        // @ts-ignore
        this._length = m.morphTargetInfluences[index]
      }
    })
    this.setGoodsSize({
      length: this.size.length,
      width: this.size.width,
      height: this.size.height
    })
  }

  setGoodsSize({
                 length, width, height
               }: {
    length: number
    width: number
    height: number
  }) {
    this._goodsSize.length = length
    this._goodsSize.width = width
    this._goodsSize.height = height

    if (this._goods) {
      clearThreeObjectMesh(this._goods)
      this._goods.removeFromParent()
    }

    this.size.length = length + this._goodsLengthGap * 2 + 0.04
    this.size.height = height + 0.115
    this.size.width = width + this._goodsWidthGap * 2 + 0.07

    this.setShapeKey('goodsWidth', width + this._goodsWidthGap * 2)
    this.setShapeKey('goodsLength', length + this._goodsLengthGap * 2)
    this._emitter.emit('size-change', this.size)

    const box = new BoxGeometry(this._goodsSize.width, height, this._goodsSize.length)
    box.translate(0, height / 2, 0)
    const edges = new EdgesGeometry(box)

    this._goods = new Mesh(box, new MeshStandardMaterial({
      color: '#22f',
      opacity: 0.2,
      transparent: true
    }))
    const lines = new LineSegments(edges, new MeshBasicMaterial({
      color: '#22f'
    }))

    this._goods.add(lines)
    this._goodsContainer?.add(this._goods)
  }

  setShapeKey(key: string, value: number) {
    if (key === 'goodsLength') {
      this._length = value
    }
    this._container.traverse(node => {
      let val = value
      if (node.userData[`origin_${key}`]) {
        val -= node.userData[`origin_${key}`]
      }
      // @ts-ignore
      if (!Object.keys(node.morphTargetDictionary || {}).includes(key)) return

      // @ts-ignore
      const index = node.morphTargetDictionary[key]
      // @ts-ignore
      node.morphTargetInfluences && (node.morphTargetInfluences[index] = val)
    })
  }

  destroy() {
    this._emitter.removeAllListeners()
    // @ts-ignore
    this._emitter = null
    clearThreeObjectMesh(this)
  }
}
