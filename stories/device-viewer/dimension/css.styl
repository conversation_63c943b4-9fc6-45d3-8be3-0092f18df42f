* {
  box-sizing border-box
}

.pointer-container {
  width 20px
  height @width
  pointer-events auto
  cursor pointer
  display: flex;
  align-items: center;
  justify-content: center;

  &.active {
    .point {
      transform scale(3)
      background rgb(0, 102, 255)
    }

    .value {
      //opacity 1
      //transform translate3d(-50%, 0, 0)
    }
  }

  &:hover {
    .point {
      transform scale(4)
      background #00FF95
    }

    .value {
      opacity 1
      transform translate3d(-50%, 0, 0)
    }
  }

  .point {
    width 3px
    height @width
    border-radius 50%
    background #898989
    transition all .2s
  }

  .value {
    white-space nowrap
    pointer-events none
    font-size 12px
    opacity 0
    position absolute
    bottom 80%
    left 50%
    color #898989
    transform translate3d(-50%, -14px, 0)
    transition all .2s
  }
}

