import { CSS3DObject }                                  from 'three/examples/jsm/renderers/CSS3DRenderer'
import { A<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Object3D, Vector2, Vector3 } from 'three'
import EventEmitter                                     from 'eventemitter3'
import { Line3 }                                        from './line3'
import { getLocationPosition }                          from '../utils'

export interface DimensionProps {
  start: Vector3
  end: Vector3,
  startOrigin?: Vector3
  axios: 'x' | 'y' | 'z'
  divider: number,
}

export class Dimension extends CSS3DObject {
  private _scale = 0.01
  private _emitter = new EventEmitter()

  addEventListener = this._emitter.on.bind(this._emitter)
  removeEventListener = this._emitter.on.bind(this._emitter)

  private endNode: HTMLDivElement
  private _textNode: HTMLDivElement
  private _mouseStart = new Vector2()
  private _targetSize = new Vector2()

  private _caches: Map<number, number>[] = []

  private _sizeFlag = 'width'

  private _direction = 1

  private _lines: Line3[] = []

  constructor(public props: DimensionProps) {
    const box = document.createElement('div')
    super(box)
    const { start, end } = props
    const length = end.distanceTo(start)
    if (this.props.axios === 'z') {
      this.props.axios = 'y'
    } else if (this.props.axios === 'y') {
      this.props.axios = 'z'
    }

    if (this.props.axios === 'y') {
      this._sizeFlag = 'height'
    }

    const wrapper = document.createElement('div')
    wrapper.style.position = 'absolute'
    wrapper.style.left = '0'
    wrapper.style.bottom = '0'
    box.append(wrapper)

    this._textNode = document.createElement('div')
    this._textNode.innerText = length.toFixed(2) + ' m'
    wrapper.append(this._textNode)

    const endNode = document.createElement('div')
    this.endNode = endNode
    endNode.style.position = 'absolute'
    endNode.style.left = 'auto'
    endNode.style.right = '0'
    endNode.style.width = '4px'
    endNode.style.height = '4px'
    endNode.style.background = '#0066FF'
    endNode.style.borderRadius = '50%'
    endNode.style.margin = '-2px -2px 0 0'
    endNode.style.cursor = 'pointer'
    wrapper.append(endNode)

    this.scale.set(this._scale, this._scale, this._scale)
    box.style.fontSize = '6px'
    box.style.color = '#fff'

    //@ts-ignore
    wrapper.style[this._sizeFlag] = length * 100 + 'px'
    wrapper.style.textAlign = 'center'
    if (this._sizeFlag === 'height') {
      wrapper.style.borderLeft = '0.1px dashed #fff'
      wrapper.style.display = 'flex'
      wrapper.style.alignItems = 'center'
      wrapper.style.justifyContent = 'center'
      wrapper.style.whiteSpace = 'nowrap'
      endNode.style.top = '0'
      endNode.style.left = '0'
      endNode.style.right = 'auto'
      endNode.style.margin = '-2px 0 0 -2px'
    } else {
      wrapper.style.borderBottom = '0.1px dashed #fff'
    }

    endNode.addEventListener('pointerdown', this._pointerdown)
    endNode.addEventListener('pointermove', this._pointermove)
    endNode.addEventListener('pointerup', this._pointercancel)

    if (end[this.props.axios] < start[this.props.axios]) {
      this.rotateY(Math.PI)
    } else {
      this._direction = -1
    }
  }

  private _getIndexByAxios() {
    if (this.props.axios === 'y') {
      return 1
    } else if (this.props.axios === 'z') {
      return 2
    } else {
      return 0
    }
  }

  private _pointerdown = (e: PointerEvent) => {
    const { pageX, pageY } = e
    this.endNode.setPointerCapture(e.pointerId)
    this._mouseStart.set(pageX, pageY)
    this._targetSize.setX(parseFloat(this.endNode.parentElement!.style.width))
    this._targetSize.setY(parseFloat(this.endNode.parentElement!.style.height))
  }

  private _pointermove = (e: PointerEvent) => {
    if (!this._mouseStart.x) return
    const { pageX, pageY } = e
    let offset = pageX - this._mouseStart.x
    let width = offset + this._targetSize.x
    if (this._sizeFlag === 'height') {
      offset = pageY - this._mouseStart.y
      width = offset * this._direction + this._targetSize.y
    }
    const more = width % 10
    width = width - more
    // if (width < 110) {
    //   width = 110
    // }
    // if (width > 180) {
    //   width = 180
    // }
    offset = (width - this._targetSize.x) * this._direction
    if (this._sizeFlag === 'height') {
      offset = (width - this._targetSize.y) * this._direction
    }
    this._textNode.innerText = (width / 100).toFixed(2) + ' m'
    //@ts-ignore
    this.endNode.parentElement!.style[this._sizeFlag] = `${width}px`
  }

  private _pointercancel = (e: PointerEvent) => {
    this.endNode.releasePointerCapture(e.pointerId)
    this._mouseStart.set(0, 0)
  }

  dispose() {
    this._lines.forEach(l => l.dispose())

    this._emitter.removeAllListeners()
    this.endNode.removeEventListener('pointerdown', this._pointerdown)
    this.endNode.removeEventListener('pointermove', this._pointermove)
    this.endNode.removeEventListener('pointerup', this._pointercancel)
    this.element.remove()

    this._caches.forEach(m => m.clear())
    this._caches = []
  }
}
