import { CSS3DObject } from 'three/examples/jsm/renderers/CSS3DRenderer'
import { Vector3 }     from 'three'

const mainColor = '#898989'

export class Line3 extends CSS3DObject {
  private _text = new Text3('')

  constructor(start: Vector3, end: Vector3, private _startWith?: string) {
    const box = document.createElement('div')
    super(box)

    const text = document.createElement('div')
    box.append(text)

    const midPoint = new Vector3().addVectors(start, end).multiplyScalar(0.5)
    this.position.copy(midPoint.multiplyScalar(1))
    const distance = start.distanceTo(end)
    let angle = Math.atan2(start.z - end.z, start.x - end.x)
    this.rotation.y = -angle
    if (start.y - end.y) {
      angle = Math.atan2(start.y - end.y, start.x - end.x)
      this.rotation.z = angle
    }

    box.style.zIndex = (Math.random() * 10000).toFixed(0)
    box.style.pointerEvents = 'none'
    box.style.width = `${this.getV(distance)}px`
    // box.style.height = `${0.1}px`
    // box.style.background = '#fff'
    box.style.borderBottom = `0.1px solid #cccccd`
    box.style.textAlign = 'center'
    box.style.transition = 'all .3s'
    // box.style.borderBottom = '0.1px solid #fff'  // 线的厚度
    box.style.color = mainColor  // 线的厚度
    // box.style.borderLeft = '10px solid #fff'  // 线的厚度

    // text.innerText = `${(distance * 100).toFixed(0)} cm`
    // text.style.fontSize = '3px'

    // const showText = `${(this.getSV(distance)).toFixed(0)} mm`
    // this._text.update(this._startWith ? `${this._startWith}${showText}` : showText)
    this.add(this._text)

    this.update(start, end)

    this.scale.set(0.01, 0.01, 0.01)
    this.rotateX(-Math.PI / 2)
  }

  getV(v: number) {
    return v * 100
  }

  getSV(v: number) {
    return v * 1000
  }

  dispose() {
    this.element.remove()
    this.clear()
    this.removeFromParent()
  }

  hide() {
    this.element.style.opacity = '0'
    this._text.hide()
  }

  show() {
    this.element.style.opacity = '1'
    this._text.show()
  }

  update(start: Vector3, end: Vector3) {
    const distance = start.distanceTo(end)
    this.element.style.width = `${this.getV(distance)}px`
    this.element.style.width = `${this.getV(distance)}px`
    const showText = `${(this.getSV(distance)).toFixed(0)} mm`
    this._text.update(this._startWith ? `${this._startWith}${showText}` : showText)

    const midPoint = new Vector3().addVectors(start, end).multiplyScalar(0.5)
    this.position.copy(midPoint.multiplyScalar(1))
  }

  updateSize(pos: Vector3) {
    this._text.updateSize(pos)
  }
}

export class Text3 extends CSS3DObject {
  constructor(text: string, color = mainColor) {
    const box = document.createElement('div')
    super(box)
    box.style.pointerEvents = 'none'
    box.innerText = text
    box.style.fontSize = '5px'
    box.style.color = color
    box.style.paddingBottom = '5px'
    box.style.transition = 'all .3s'
    // this.rotateX(-Math.PI / 2)
    this.position.setY(1)
  }

  hide() {
    this.element.style.opacity = '0'
  }

  show() {
    this.element.style.opacity = '1'
  }

  update(text: string) {
    this.element.innerText = text
  }

  updateSize(pos: Vector3) {
    const current = this.parent?.position.clone()
    current?.setY(this.position.y)
    let distance = current?.distanceTo(pos) || 1
    distance *= 1.2
    if (distance < 5) {
      distance = 5
    }
    this.element.style.fontSize = `${distance}px`
    this.element.style.paddingBottom = `${distance}px`
  }
}
