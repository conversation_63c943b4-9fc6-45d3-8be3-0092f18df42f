import { Box3, Object3D, Vector3 }               from 'three'
import { calculateTotalDimensionsUsingVertices } from '../utils'
import { Tween }                                 from '@tweenjs/tween.js'
import { Outerline }                             from '../outerline'
import { Scene3D }                               from '../scene'
import { SizeLine }                              from './size-line'
import { CSS2DObject }                           from 'three/examples/jsm/renderers/CSS2DRenderer'

/*
* 长宽高的标尺
*
* x 为长
* y 为宽
* z 为高
*
* */

export class SizeBox extends Object3D {
  private _box3 = new Box3()

  private _line11 = new SizeLine('长：')
  private _line12 = new SizeLine('宽：')
  private _line13 = new SizeLine('高：')

  private _outline = new Outerline(0, 0)

  private _object: Object3D | undefined

  _fitAnimation: Tween<any> | undefined

  private _radius = 0

  private _app: Scene3D | undefined

  private _hasWidthConfig = false
  private _hasLengthConfig = false
  private _hasHeightConfig = false

  follow: string = ''
  followOrigin = 0
  followScale = 1

  originOffset = 0
  translateCache = 0

  head = 0
  tail = 0

  constructor() {
    super()
    this.add(this._outline, this._line11, this._line12, this._line13)
  }

  get hasSizeConfig() {
    // 模型是否携带配置尺寸的 shape key
    return {
      length: this._hasLengthConfig,
      width: this._hasWidthConfig,
      height: this._hasHeightConfig
    }
  }

  get size() {
    return this._box3.clone()
  }

  get radius() {
    return this._radius
  }

  init(app: Scene3D) {
    this._app = app
    this._app.controls.addEventListener('change', this._change)
  }

  reset() {
    this.hide()
  }

  private _change = () => {
    if (!this._app) return
    const camera = this._app.camera
    if (!camera) return
    this._outline.change(camera.position)
  }

  generate(object?: Object3D, offset = 0) {
    if (object) {
      this._object = object
    }
    this.update(offset)
  }

  show(all=true) {
    const nodes:Object3D[] = [this._line11, this._line12, this._line13]
    if (all) {
      this.visible = true
      this._outline.visible = true
      this._outline.show()
      nodes.push(this._outline)
    }
    nodes.forEach(node => {
      node.traverse(m => {
        m.visible = true
        if (m instanceof CSS2DObject) {
          m.element.style.visibility = 'visible'
        }
      })
    })
  }

  hide(all=true) {
    const nodes:Object3D[] = [this._line11, this._line12, this._line13]
    if (all) {
      this.visible = false
      this._outline.visible = false
      this._outline.hide()
      nodes.push(this._outline)
    }

    nodes.forEach(node => {
      node.traverse(m => {
        m.visible = false
        if (m instanceof CSS2DObject) {
          m.element.style.visibility = 'hidden'
        }
      })
    })
  }

  destroy() {
    this._app?.controls.removeEventListener('change', this._change)
    this._line11.destroy()
    this._line12.destroy()
    this._line13.destroy()
  }

  update(originOffset = 0) {
    if (!this._object) return
    const object = this._object

    const { box, maxRadius, hasLength, hasHeight, hasWidth } = calculateTotalDimensionsUsingVertices(object)

    this.tail = Math.abs(box.min.x)
    this.head = Math.abs(box.max.x)

    this._hasWidthConfig = hasWidth
    this._hasLengthConfig = hasLength
    this._hasHeightConfig = hasHeight

    this._box3.copy(box)
    this._radius = maxRadius
    if (this.follow && originOffset) {
      this.originOffset = (this.followOrigin - originOffset) * this.followScale
    }
    this._outline.update(this._radius)

    this._change()

    const minX = this._box3.min.x
    const maxX = this._box3.max.x
    const minZ = this._box3.min.z
    const maxZ = this._box3.max.z

    const minY = this._box3.min.y
    const maxY = this._box3.max.y

    let offset = 0.2

    // 4个起点
    const s1 = new Vector3(minX, 0, minZ)
    const s2 = new Vector3(maxX, 0, minZ)

    this._line11.update(
      s1.clone().sub(new Vector3(0, 0, offset / 1.8)),
      s2.clone().sub(new Vector3(0, 0, offset / 1.8)),
      this._radius
    )

    const s3 = new Vector3(maxX, 0, maxZ)
    this._line12.update(
      s2.clone().add(new Vector3(offset, 0, 0)),
      s3.clone().add(new Vector3(offset, 0, 0)),
      this._radius
    )

    const z1 = new Vector3(minX, minY, minZ)
    const z2 = new Vector3(minX, maxY, minZ)

    const zo = z1.clone()
    zo.z -= offset / 6
    const z2o = z2.clone()
    z2o.z -= offset / 6
    this._line13.update(zo, z2o, this._radius)
  }
}
