import { BufferGeometry, Group, LineBasicMaterial, LineSegments, Vector3 } from 'three'
import { clearThreeObjectMesh }                                            from '../utils'
import {
  CSS2DObject
}                                                                          from 'three/examples/jsm/renderers/CSS2DRenderer'

export class SizeText extends CSS2DObject {
  private _text = document.createElement('div')

  onPointMove: (() => void) | undefined
  onPointLeave: (() => void) | undefined

  constructor(text: string) {
    const container = document.createElement('div')
    super(container)

    this.element.style.pointerEvents = 'auto'
    this.element.style.padding = '4px 14px'
    this.element.style.border = '1px solid #eee'

    this._text.style.fontSize = '14px'
    this._text.style.font = 'SF Pro SC,SF Pro Text,SF Pro Icons,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif'
    this._text.style.color = '#fff'
    this._text.style.fontWeight = '500'
    this._text.style.transition = 'all .16s'
    this._text.innerText = text
    this.element.append(this._text)

    this._text.addEventListener('pointermove', () => {
      this.element.style.border = '1px solid #666'
      this._text.style.color = '#000'
      this.onPointMove?.()
    })
    this._text.addEventListener('pointerleave', () => {
      this.element.style.border = '1px solid #fff'
      this._text.style.transform = 'scale(1)'
      this._text.style.color = '#fff'
      this.onPointLeave?.()
    })
  }

  set text(text: string) {
    this._text.innerText = text
  }

  dispose() {
    this._text.remove()
    this.element.remove()
  }
}

export class SizeLine extends Group {
  private readonly _line2Length = 0.01
  private _line = new LineSegments(
    new BufferGeometry(),
    new LineBasicMaterial({
      color: '#666',
      linewidth: 2
    }))
  private _line2 = new LineSegments(
    new BufferGeometry(),
    new LineBasicMaterial({
      color: '#666',
      linewidth: 2
    }))
  private _text: SizeText = new SizeText('')

  constructor(private _startWith?: string, private _showText = true) {
    super()

    this._line.frustumCulled = false
    this.add(this._line, this._line2)

    if (this._showText) {
      this.add(this._text)

      this._text.onPointMove = () => {
        this._line.material.color.set('#fff')
        this._line2.material.color.set('#fff')
      }
      this._text.onPointLeave = () => {
        this._line.material.color.set('#666')
        this._line2.material.color.set('#666')
      }
    }
  }

  updateMaterial(mat: any) {
    this._line.material = mat
  }

  reset() {
    this._line.geometry?.dispose()
    this._line.computeLineDistances()
  }

  update(start: Vector3, end: Vector3, radius = 0) {
    this._line.geometry?.dispose()
    this._line.geometry.setFromPoints([start, end])
    this._line.computeLineDistances()

    const len = this._line2Length + radius * 0.02

    // Calculate direction vector and perpendicular vector for tangent lines
    const direction = new Vector3().subVectors(end, start).normalize()

    // Choose perpendicular vector based on the primary axis of change
    let perpendicular: Vector3

    if (Math.abs(direction.z) > Math.abs(direction.x) && Math.abs(direction.z) > Math.abs(direction.y)) {
      // Z-axis is the primary direction, use x and y for perpendicular
      perpendicular = new Vector3(-direction.y, direction.x, 0)
    } else if (Math.abs(direction.y) > Math.abs(direction.x)) {
      // Y-axis is the primary direction, keep perpendicular in z direction
      perpendicular = new Vector3(0, 0, 1)
    } else {
      // X-axis is the primary direction, keep perpendicular in z direction
      perpendicular = new Vector3(0, 0, 1)
    }

    // Ensure we have a valid perpendicular vector
    if (perpendicular.length() < 0.001) {
      // Fallback to a default perpendicular vector
      if (Math.abs(direction.x) < 0.9) {
        perpendicular = new Vector3(1, 0, 0)
      } else {
        perpendicular = new Vector3(0, 1, 0)
      }
    }

    perpendicular.normalize()

    // Create tangent lines at start and end points
    const startTangent1 = new Vector3().addVectors(start, perpendicular.clone().multiplyScalar(len))
    const startTangent2 = new Vector3().addVectors(start, perpendicular.clone().multiplyScalar(-len))
    const endTangent1 = new Vector3().addVectors(end, perpendicular.clone().multiplyScalar(len))
    const endTangent2 = new Vector3().addVectors(end, perpendicular.clone().multiplyScalar(-len))

    // Set up the second line geometry for tangent lines
    this._line2.geometry?.dispose()
    this._line2.geometry = new BufferGeometry()
    // Create two separate line segments: one at start, one at end
    this._line2.geometry.setFromPoints([
      startTangent1, startTangent2,  // First line segment at start
      endTangent1, endTangent2       // Second line segment at end
    ])
    this._line2.computeLineDistances()

    const distance = start.distanceTo(end)
    if (this._startWith) {
      this._text.text = `${this._startWith}${(distance * 1000).toFixed(0)} mm`
    } else {
      this._text.text = `${(distance * 1000).toFixed(0)} mm`
    }
    const center = new Vector3()
    center.addVectors(start, end).multiplyScalar(0.5)
    this._text.position.copy(center)

  }

  destroy() {
    this._text.dispose()
    this.children.forEach(c => clearThreeObjectMesh(c))
    this.removeFromParent()
  }
}
