import {
  BoxGeometry,
  Color,
  EdgesGeometry,
  LineDashedMaterial,
  LineSegments,
  Mesh,
  MeshBasicMaterial,
  Object3D
}                               from 'three'
import { clearThreeObjectMesh } from './utils'

export class GoodsBox extends Object3D {
  constructor(length = 0.6, width = 0.4, height = 0.2) {
    super()
    this._generate(length, width, height)
  }

  updateSize(length = 0.1, width = 0.1, height = 0.1) {
    this._generate(length, width, height)
  }

  clearAll() {
    clearThreeObjectMesh(this)
    // this.children.forEach(node => clearThreeObjectMesh(node))
  }

  hide() {
    this.traverse(node => node.visible = false)
  }

  show() {
    this.traverse(node => node.visible = true)
  }

  private _generate(length: number, width: number, height: number) {
    this.clearAll()
    const box = new Mesh(new BoxGeometry(length, height, width),
      new MeshBasicMaterial({
        color: new Color('#0f5fd5'),
        transparent: true,
        opacity: 0.4
      }))
    box.geometry.translate(0, height / 2, 0)

    const edges = new EdgesGeometry(box.geometry)
    const lineMaterial = new LineDashedMaterial({
      color: new Color('#000'),
      dashSize: 0.015,
      gapSize: 0.015
    })

    const lineSegments = new LineSegments(edges, lineMaterial)
    lineSegments.computeLineDistances()
    this.add(box, lineSegments)
  }
}
