import { AMRAssembly }              from './assembly'
import Model                        from './asserts/models/roller-hopper.glb?url'
import { Group, Object3D, Vector3 } from 'three'
import { Roller }                   from './roller'
import { Row }                      from './row'
import EventEmitter                 from 'eventemitter3'
import { clearThreeObjectMesh }     from './utils'
import { Belt }                     from './belt'

export class GroupRoller extends AMRAssembly {
  private _container = new Group()

  private _emitter = new EventEmitter()
  addEventListener = this._emitter.addListener.bind(this._emitter)

  private _widthGap = 0.01
  private _lengthGap = 0.01

  private _row = new Row(this._widthGap)

  constructor() {
    super(Model)

    this.add(this._container)
  }

  loaded(glb: Object3D) {
    this._container.add(glb.clone(true))
    const row = this._row

    row.addEventListener('size-change', ({ width, length, height }) => {
      this.setShapeKey('goodsWidth', width)
      this.setShapeKey('goodsLength', length)

      this.size.width = length + this._lengthGap
      this.size.length = width + this._widthGap
      this.size.height = height
      this._emitter.emit('size-change', this.size)
    })

    const t = new Belt({
      goodsSize: new Vector3(1, 1, 1)
    })
    const t2 = new Belt({
      goodsSize: new Vector3(1, 1, 1)
    })

    row.addItem(t)
    row.addItem(t2)

    this.add(row)
  }

  setShapeKey(key: string, value: number) {
    const nodes: Object3D[] = []
    if (['length', 'width'].includes(key)) {
      value -= 0.2
    }

    this._container.traverse(node => {
      // @ts-ignore
      if (!Object.keys(node.morphTargetDictionary || {}).includes(key)) return

      let val = value
      if (node.userData[`origin_${key}`]) {
        val -= node.userData[`origin_${key}`]
      }
      // @ts-ignore
      const index = node.morphTargetDictionary[key]
      // @ts-ignore
      node.morphTargetInfluences && (node.morphTargetInfluences[index] = val)
    })
  }

  setGoodsSize(size: {
    length: number
    width: number
    height: number
  }) {
    this._row.items.forEach((item: Roller) => item.setGoodsSize(size))
  }

  destroy() {
    this._emitter.removeAllListeners()
    this._row.destroy()
    clearThreeObjectMesh(this)
  }
}
