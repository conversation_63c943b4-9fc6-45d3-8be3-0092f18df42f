import { DeviceViewer, Props } from './index'

export default {
  title: 'Shop/DeviceViewer',
  render: (args: Props) => {
    const deviceViewer = new DeviceViewer()
    deviceViewer.test = args.test
    deviceViewer.count = args.count
    deviceViewer.onChange = args.onChange
    deviceViewer.addEventListener('ticker', () => {
      console.log('tttt')
    })
    return deviceViewer
  },
  argTypes: {
    test: {
      control: 'text',
      description: 'test'
    },
    count: {
      control: 'number'
    },
    onChange: {
      control: 'function',
      description: '选择回调',
      value: () => {

      }
    }
  }
}

export const Primary = {
  args: {
    test: 'test',
    count: 4
  }
}
