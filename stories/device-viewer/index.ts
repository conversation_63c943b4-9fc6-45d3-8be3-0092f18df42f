import { css, html, LitElement, PropertyValues, unsafeCSS } from 'lit'
import { customElement, property, query, queryAll, state }  from 'lit/decorators.js'
import { Vector2 }                                          from 'three'
import { Scene3D, Skin }                                    from './scene'
import bg                                                   from './asserts/bg-png.png'
import bgCircle                                             from './asserts/bg-circle.png'
import Radar                                                from './asserts/img-radar.png'
import Radar2                                               from './asserts/img-radar2.png'
// import { DeviceInfo }                                       from './slot'
import { downloadBlob }                                     from '../utils'
import { Cropper }                                          from '../cropper'
import { DeviceInfo }                                       from '@/device-viewer/slot'
import { Logo }                                             from '@/device-viewer/logo'

export interface Props {
  test: string
  count: number
  onChange: () => {}
}

@customElement('device-viewer')
export class Device<PERSON>iewer extends LitElement {

  scene = new Scene3D({
    useSound: true,
    fov: 39.6,
    useAxisBall: true,
    axisBallOffset: new Vector2(-100, 10)
  })

  cropper = new Cropper()

  @query('.container')
  container: HTMLDivElement | undefined

  @query('.canvas')
  canvas: HTMLDivElement | undefined

  @query('#drag')
  drag: HTMLButtonElement | undefined

  // createRenderRoot() {
  //   return this
  // }

  constructor() {
    super()
    const style = document.createElement('style')
    document.head.append(style)

    this.scene.renderer.domElement.addEventListener('ticker', () => {
      const percent = this.scene.sampleCount / this.scene.maxSample
      if (this.progressBar) {
        this.progressBar.style.width = percent * 100 + '%'
      }
    })
  }

  protected firstUpdated(_changedProperties: PropertyValues) {
    super.firstUpdated(_changedProperties)
    if (!this.canvas) return
    this.scene.appendTo(this.canvas)

    function preventDefaults(e: DragEvent) {
      e.preventDefault()
      e.stopPropagation()
    };

    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
      // @ts-ignore
      this.container!.addEventListener(eventName, preventDefaults, false)
    })

    const handleDrop = (e: DragEvent) => {
      let dt = e.dataTransfer
      // @ts-ignore
      let files = dt.files
      if (!files.length) return
      const file = files[0]
      this.scene.clearAMR()
      const url = URL.createObjectURL(file)
      this.scene.addAMR(url, file.name.substring(file.name.lastIndexOf('.') + 1))
        // this.scene.addAMR('http://localhost:5173/amrs/SJV-CSL06-YF.glb')
        // this.scene.addAMR(Model)
        // this.scene.addAMR(Model)
        .then(this.scene.fit.bind(this.scene))
        .then(() => {
          this.shapeKeys = this.scene.amr.shapeKeys

          this.colors = this.scene.amr.skins

          this.logos = this.scene.amr.logos
          if (this.logos.length) {
            this.cropper.style.display = 'block'
          } else {
            this.cropper.style.display = 'none'
          }

          this.scene.amr.slots.forEach(s => {
            // s.addEventListener(SLOT_EVENTS.ON_FUNCTION_REMOVED, f => {
            //   console.log('ON_FUNCTION_REMOVED', f)
            // })
            // s.addEventListener(SLOT_EVENTS.ON_FUNCTION_ADDED, f => {
            //   console.log('ON_FUNCTION_ADDED', f)
            // })

            const fs = [
              '单激光定位导航',
              '激光反光板导航',
              '普通栈板识别',
              '2D 障碍物停障',
              '2D 障碍物绕障1',
              '2D 障碍物绕障2',
              '2D 障碍物绕障3',
              '2D 障碍物绕障4',
              '2D 障碍物绕障5'
            ]
            s.allFunctions = [...fs]
            fs.pop()
            fs.pop()
            fs.pop()
            fs.pop()
            fs.pop()
            s.addFunctions(fs)
            // s.addEventListener(SLOT_EVENTS.ON_SELECTED, e => {
            //   console.log('Slot drop error:', e)
            //   s.changeName(`我是新的插槽${Math.random().toFixed(2)}`)
            // })
          })
        })
    }

    this.container!.addEventListener('drop', handleDrop, false)


    if (this.cropperContainer) {
      this.cropperContainer.append(this.cropper)
      this.cropper.resize(100, 100)

      this.cropper.addEventListener('image-change', (e: any) => {
        if (e.detail && e.detail.image) {
          if (!this.selectedLogo) return
          const configs = this.cropper.getConfigs()
          this.scene.amr.changeLogo(this.selectedLogo, configs)
          this.logoSize = this.cropper.imageRealSize
        }
      })
    }

    this.scene.amr.addEventListener('size-changed', () => {
    })
    this.scene.amr.addEventListener('shape-key-changed', (key, value) => {
      console.log(key, value)
      if (['height'].includes(key)) {
        this.scene.fit()
      }
    })

    this.scene.addAMR('https://cdn2-1304552240.cos.ap-shanghai.myqcloud.com/rp/amrs/SFL-CDD15.sglb')
    // this.scene.addAMR('https://seer-group-dev25.aedev.feishuapp.cn/ae/api/v1/assets/attachment/download?token=BIZ_8c8672f9a9314def80f2528ef8b6055d&namespace=nebular__c', 'glb')
    // this.scene.addAMR('/test.glb')
      // this.scene.addAMR(Model)
      // this.scene.addAMR(Model)
      .then(this.scene.fit.bind(this.scene))
      .then(() => {
        this.shapeKeys = this.scene.amr.shapeKeys

        this.actions = this.scene.amr.actions || []

        this.colors = this.scene.amr.skins

        console.log(this.scene.amr.slots)

        this.logos = this.scene.amr.logos
        console.log(this.logos)
        if (this.logos.length) {
          this.cropper.style.display = 'block'
        } else {
          this.cropper.style.display = 'none'
        }

        // this.setShapeKey('height', 0.4)
        // this.setShapeKey('width', 0.6)
        // this.setShapeKey('length', 1)

        this.scene.amr.slots.forEach(s => {
          // s.addEventListener(SLOT_EVENTS.ON_FUNCTION_REMOVED, f => {
          //   console.log('ON_FUNCTION_REMOVED', f)
          // })
          // s.addEventListener(SLOT_EVENTS.ON_FUNCTION_ADDED, f => {
          //   console.log('ON_FUNCTION_ADDED', f)
          // })

          const fs = [
            '单激光定位导航',
            '激光反光板导航',
            '普通栈板识别',
            '2D 障碍物停障',
            '2D 障碍物绕障1',
            '2D 障碍物绕障2',
            '2D 障碍物绕障3',
            '2D 障碍物绕障4',
            '2D 障碍物绕障5'
          ]
          s.allFunctions = [...fs]
          fs.pop()
          fs.pop()
          fs.pop()
          fs.pop()
          fs.pop()
          s.addFunctions(fs)
          // s.addEventListener(SLOT_EVENTS.ON_SELECTED, e => {
          //   console.log('Slot drop error:', e)
          //   s.changeName(`我是新的插槽${Math.random().toFixed(2)}`)
          // })
        })
      })

    setTimeout(() => {
      // this.scene.amr.setLevel(1)
      // this.changeGoodsSize(undefined)
    }, 300)
  }

  protected updated(_changedProperties: PropertyValues) {
    super.updated(_changedProperties)
  }

  disconnectedCallback() {
    super.disconnectedCallback()
    this.scene.destroy()
  }

  @property()
  test = ''

  @property()
  count = 4

  @property()
  theme: undefined | string

  @property()
  onChange: undefined | Function

  @state()
  usePathTracing = false

  @state()
  pointCloudMode = false

  @state()
  actions: string[] = []

  @state()
  shelfLevel = 5

  @state()
  shapeKeys = new Map()

  @state()
  colors: Skin[] = []

  @state()
  selectedLogo: Logo | undefined
  @state()
  logos: Logo[] = []

  @state()
  logoSize = {
    width: 0,
    height: 0
  }

  @query('.progress-bar')
  progressBar: HTMLDivElement | undefined

  @query('#cropper')
  cropperContainer: HTMLDivElement | undefined

  @queryAll('li .line-start')
  lineCircles: HTMLDivElement[] | undefined

  @query('.test')
  lineWrapper: HTMLDivElement | undefined

  @query('#sizeLength')
  sizeLength: HTMLDivElement | undefined

  @query('#sizeWidth')
  sizeWidth: HTMLDivElement | undefined

  @query('#sizeHeight')
  sizeHeight: HTMLDivElement | undefined

  fit = this.scene.fit.bind(this.scene)
  generateBVH = () => {
    this.usePathTracing = true
    this.scene.generateBVH().then()
  }
  download = async () => {
    const url = await this.scene.download()
    const downloadLink = document.createElement('a')
    downloadLink.href = url
    downloadLink.download = 'transparent-background-object.png'
    document.body.appendChild(downloadLink)
    downloadLink.click()
    document.body.removeChild(downloadLink)
  }
  clearAMR = this.scene.clearAMR.bind(this.scene)
  exportAMR = async () => {
    const glb = await this.scene.exportAMR()

    if (glb) {
      downloadBlob(new Blob([glb as BlobPart]), 'ttt.glb')
    }
  }
  addAMR = () => {
    this.scene.addAMR('https://cdn2-1304552240.cos.ap-shanghai.myqcloud.com/rp/amrs/SFL-CDD14.sglb')
      .then(this.scene.fit.bind(this.scene))
      .then(() => {
        this.shapeKeys = this.scene.amr.shapeKeys
      })
  }
  // changeLogo = this.scene.changeLogo.bind(this.scene)

  playAction = (name: string) => {
    this.scene.amr.playAction(name)
  }

  pauseAction = (name: string) => {
    this.scene.amr.pauseAction(name)
  }

  clean = () => {
    this.scene.removeFromParent()
    this.scene.clean()
    // this.scene.appendTo(this.container!)

    // this.scene.addAMR('https://cdn2-1304552240.cos.ap-shanghai.myqcloud.com/rp/amrs/SFL-CDD15.sglb')
    //   .then(() => {
    //     this.scene.fit()
    //   })
  }

  recreate = () => {
    this.scene.destroy()

    this.scene = new Scene3D({
      useSound: false,
      useAxisBall: true
    })
    this.scene.appendTo(this.canvas!)
    setTimeout(() => {
      this.scene.addAMR('https://cdn2-1304552240.cos.ap-shanghai.myqcloud.com/rp/amrs/SFL-CPD20-Y.sglb')
        .then(() => {
          this.scene.fit()
        })
    }, 100)
  }

  setShapeKey = (key: string, val: any) => {
    this.scene.amr.setShapeKey(key, val)
  }

  cancel = () => {
    this.usePathTracing = false
    this.scene.usePathTracing = false
  }

  changeShelfLevel = (e: any) => {
    // console.log(e.target.value)
    const v = parseInt(e.target.value)
    if (!v) return
    this.shelfLevel = v
    this.scene.amr.setShelfLevel(this.shelfLevel)
  }

  changeAmrHeight = (e: any) => {
    const v = parseFloat(e.target.value)
    if (!v) return
    this.scene.amr.setForkHeight(v)
    this.scene.amr.setLiftHeight(v)
    this.scene.fit()
    // this.scene.amr.setForkMaxHeight(v)
  }

  togglePointCloudMode = () => {
    this.pointCloudMode = !this.pointCloudMode
    this.scene.pointCloudMode = this.pointCloudMode

    // 更新容器背景色
    // if (this.container) {
    //   const canvas = this.container.querySelector('canvas')
    //   if (canvas) {
    //     // canvas.style.background = this.pointCloudMode ? '#000000' : '#eaf7fd'
    //   }
    // }
  }

  changeAmrLevel = (e: any) => {
    const v = parseFloat(e.target.value)
    if (!v) return
    this.scene.amr.setLevel(v)
    this.changeGoodsSize(undefined)
    // this.scene.fit()
  }

  changeAmrShelvesLevel = (e: any) => {
    const v = parseFloat(e.target.value)
    if (!v) return
    this.shelfLevel = v
    this.scene.amr.setShelfLevel(v)
  }

  setMaxHeight = (e: any) => {
    const v = parseFloat(e.target.value)
    if (!v) return
    this.scene.amr.setForkMaxHeight(v)
    this.scene.amr.setJackMaxHeight(v)
    this.scene.amr.setLiftMaxHeight(v)
    this.fit()
  }

  changeGoodsSize = (e: any) => {
    // @ts-ignore
    const length = parseFloat(this.sizeLength!.value || '0')
    // @ts-ignore
    const width = parseFloat(this.sizeWidth!.value || '0')
    // @ts-ignore
    const height = parseFloat(this.sizeHeight!.value || '0')

    // this.scene.fit()
    // this.scene.amr.setLevel(1)
    this.scene.amr.setGoodsSize({
      length: length,
      width: width,
      height: height
    })
  }

  changeColor = (s: Skin, value: string) => {
    this.scene.amr.changeSkin(s, value)
  }

  selectLogo = (l: Logo) => {
    this.selectedLogo = l
    if (l.imageUrl) {
      this.cropper.setUV(l.target.geometry)
      this.cropper.add(l.imageUrl, l.size).then()
    }
  }

  selectImage = (e: Event) => {
    const input = e.target as HTMLInputElement
    if (input.files && input.files.length > 0) {
      const file = input.files[0]
      const url = URL.createObjectURL(file)
      console.log('Selected file - ', file.name, this.selectedLogo)
      const l = this.selectedLogo
      if (l) {
        this.cropper.add(url, l.size)
      }
    }
    // @ts-ignore
    input.value = null
  }

  resetLogo = (l: Logo) => {
    l.reset()
    if (l.imageUrl) {
      this.cropper.add(l.imageUrl, l.size).then()
    }
  }

  clearLogo = (l: Logo) => {
    l.clear()
    this.cropper.clear()
  }

  loadConfigs = () => {
    const configs = JSON.parse(localStorage.getItem('cropper')!)
    if (configs) {
      this.cropper.updateConfigs(configs).then()
    }
  }

  saveConfigs = (l: Logo) => {
    const configs = this.cropper.getConfigs()
    localStorage.setItem('cropper', JSON.stringify({
      ...configs,
      url: 'https://cn.seer-group.com/cdn1/static/common/logo.png/webp90'
    }))
  }

  dragstart = (e: DragEvent) => {
    const deviceId = (e.target as HTMLElement)?.getAttribute('data-id')
    let url = 'https://cdn2-1304552240.cos.ap-shanghai.myqcloud.com/rp/devices/M30D Mini.sglb'
    // let bracketUrl = 'https://cdn2-1304552240.cos.ap-shanghai.myqcloud.com/rp/brackets/Mid-70_Bracket.sglb'
    let bracketUrl = ''
    if (deviceId === '2') {
      url = 'https://cdn2-1304552240.cos.ap-shanghai.myqcloud.com/rp/devices/ROH- A001L.sglb'
      // bracketUrl = 'https://cdn2-1304552240.cos.ap-shanghai.myqcloud.com/rp/brackets/Mid-360_Bracket.sglb'
    }
    this.drag?.classList.add('dragging')
    const id = (e.target as HTMLImageElement).getAttribute('data-id')
    const jsonDataString = JSON.stringify({
      // id, name: '', url: 'test.ss'
      id, name: 'H1E0-02C',
      url,
      bracketUrl
    } as DeviceInfo) // 转换为字符串
    e.dataTransfer?.setData('application/json', jsonDataString)

    this.scene.amr.slots.forEach((s, i) => s.canInstall(Math.random() > 0))
    this.scene.amr.bodyOpacity = 0.2
  }

  dragend = (e: DragEvent) => {
    this.drag?.classList.remove('dragging')
    this.scene.amr.slots.forEach(s => s.canInstall(false))
    // this.scene.slots.forEach(s => s.error(Math.random() > 0.5))
  }

  getShapeKeyValue = (key: string) => {
    const v = this.shapeKeys.get(key).toFixed(3)
    if (v.endsWith('.000')) return v.replace('.000', '')
    return v
  }

  protected render(): unknown {
    return html`
      <div class="container">
        <div class="canvas"></div>
<!--        <div class="bg"></div>-->
        <div class="progress-bar"></div>
        <div class="actions">
          <button @click=${this.fit}>聚焦</button>
          ${
            this.usePathTracing
              ? html`
                <button @click=${this.cancel}>取消渲染</button>`
              : html`
                <button @click=${this.generateBVH}>渲染</button>`
          }
          <button @click=${() => {
            this.scene.amr.bodyOpacity = this.scene.amr.bodyOpacity > 0.5 ? 0.3 : 1
          }}>模式切换
          </button>
          <button @click=${this.togglePointCloudMode} 
            style=${this.pointCloudMode ? 'background: #0066ff; color: white;' : ''}>
            ${this.pointCloudMode ? '退出点云模式' : '点云模式'}
          </button>
          <button @click=${this.download}>Download</button>
            <!--          <button @click=${this.clearAMR}>CLEAR</button>-->
            <!--          <button @click=${this.addAMR}>ADD</button>-->
            <!--          <button @click=${this.exportAMR}>EXPORT</button>-->
          <button @click=${this.recreate}>Recreate</button>
          <button @click=${this.clean}>Clean</button>
          <div class="animations">
            <label>动画:</label>
            ${
              this.actions.map(a => {
                return html`
                  <div class="animation-item">
                    <div>${a}</div>
                    <button @click=${() => this.playAction(a)}>播放</button>
                    <button @click=${() => this.pauseAction(a)}>暂停</button>
                  </div>
                `
              })
            }
          </div>
          <div class="amr-actions">
            <label>滚筒层数：</label>
            <input value="1" type="number" @change=${this.changeAmrLevel}>
            <div>
              <label>货物尺寸：</label>
              <input value="0.6" id="sizeLength" @change=${this.changeGoodsSize}>
              <input value="0.9" id="sizeWidth" @change=${this.changeGoodsSize}>
              <input value="0.35" type="number" step="0.01" id="sizeHeight" @change=${this.changeGoodsSize}>
            </div>
            <div>
              <label>料斗层数：</label>
              <input value="3" type="number" @change=${this.changeAmrShelvesLevel}>
            </div>
            <div>
              <label>最大高度：</label>
              <input value="0" @change=${this.setMaxHeight}>
            </div>
            <div>
              <label>起升高度：</label>
              <input value="0" @change=${this.changeAmrHeight}>
            </div>

            <div>
              ${
                this.colors.map(c => {
                  return html`
                    <div>
                      <label>${c.name}：</label>
                      <input value=${c.color} type="color"
                             @change=${(e: any) => this.changeColor(c, e.target.value)}>
                      <button @click=${() => c.reset()}>Reset</button>
                    </div>
                  `
                })
              }
            </div>

            <div>
              ${
                this.logos.length ?
                  html`<label>修改 Logo：</label>`
                  : ``
              }
              ${
                this.logos.map(l => {
                  return html`
                    <div>
                      <button @click=${() => this.selectLogo(l)}>${l.name}</button>
                      <button @click=${() => this.resetLogo(l)}>Reset</button>
                      <button @click=${() => this.loadConfigs()}>Load Configs</button>
                      <button @click=${() => this.saveConfigs(l)}>Save</button>
                      <button @click=${() => this.clearLogo(l)}>Clear</button>
                      <div>宽：${(this.logoSize.width * 1000).toFixed(0)} mm | 高：${(this.logoSize.height * 1000).toFixed(0)} mm</div>
                      <input type="file" @change=${this.selectImage}>
                    </div>
                  `
                })
              }
              ${
                this.logos.length
                  ? html`
                    <button @click=${() => this.cropper.clear()}>Clear</button>`
                  : ''
              }

              <div id="cropper"></div>
            </div>

          </div>
          ${
            [...this.shapeKeys.keys()].map(key => {
              return html`
                <div class="shape-key">
                  <label>${key}</label>
                  <input
                    value=${this.getShapeKeyValue(key)}
                    type="range"
                    step="0.1"
                    min="0.5"
                    max="2"
                    @input=${(e: any) => this.setShapeKey(key, e.target.value)}>
                </div>`
            })
          }
          <div
            class="drag"
            @dragstart=${this.dragstart}
            @dragend=${this.dragend}
            draggable="true">
            <img alt=""
                 data-id="1"
                 src=${Radar}>
          </div>
          <div
            class="drag"
            @dragstart=${this.dragstart}
            @dragend=${this.dragend}
            draggable="true">
            <img alt=""
                 data-id="2"
                 src=${Radar2}>
          </div>
        </div>

        <!--        <div class="test">-->
        <!--          <ul>-->
        <!--            <li>功能1</li>-->
        <!--            <li>功能2</li>-->
        <!--            <li>功能3<i class="line-start"></i></li>-->
        <!--            <li>功能4<i class="line-start"></i></li>-->
        <!--            <li>功能5<i class="line-start"></i></li>-->
        <!--            <li>功能6</li>-->
        <!--            <li>功能7<i class="line-start"></i></li>-->
        <!--            <li>功能8</li>-->
        <!--            <li>功能9</li>-->
        <!--            <li>功能10<i class="line-start"></i></li>-->
        <!--            <li>功能11</li>-->
        <!--            <li>功能12<i class="line-start"></i></li>-->
        <!--            <li>功能13<i class="line-start"></i></li>-->
        <!--            <li>功能14<i class="line-start"></i></li>-->
        <!--            <li>功能15</li>-->
        <!--            <li>功能16</li>-->
        <!--          </ul>-->
        <!--        </div>-->
      </div>`
  }

  static styles = css`
    :root {
        display:block;
        width:100%;
        height:100%;
      }
      
      .bg {
      pointer-events:none;
      z-index:100;
      position:absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
        background: url('/bg.png') center no-repeat;
        background-size: 100% auto;
      }
      .test {
        position: absolute;
        z-index: 1000;
        left: 0;
        top: 240px;
        overflow-y: auto;
        overflow-x: auto;
        width: auto;
        height: 300px;
      }
      li {
        margin: 10px 0;
        margin-right: 20px;
        width: 100px;
        line-height: 30px;
        background: #ddd;
      }
      .pane-container {
        position: absolute;
        left: auto;
        right: 0;
        top: 0;
      }
      
      .shape-key {
        label {
          font-size: 12px;
        }
        input {
          width: 70px;
        }
      }
      
      .amr-actions {
        margin-top: 10px;
        margin-bottom: 10px;
        font-size: 12px;
        input {
          width: 60px;
        }
      }
        
      .animations {
        label {
          font-size:14px;
        }
        div {
          font-size: 12px;
        }
        
        .animation-item {
          display: flex;
          align-items: center;
          button {
            margin-left: 10px;
          }
        }
      }
    
      #cropper {
        width: 100px;
        height: 100px;
      }
    
        .container {
          overflow: hidden;
          border: 0;
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          transform: scale3d(1, 1, 1);
        }
        
        .drag{
          width: 60px;
          height: 60px;
        }
        .drag.dragging{
          opacity: 0.4;
        }
        
        .drag img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
        
        .canvas {
          position: absolute;
          left:240px;
          left:0;
          top: 0;
          width: 100%;
          height: 100%;
        }
    
        canvas {
          position: absolute;
          left: 0;
          width: 100%;
          height: 100%;
          // background: url(${unsafeCSS(bg)}) center repeat;
          // background: linear-gradient(to bottom, #898996 0%, #454555 60%, #111125 100%);
          // background: #F5F7FC url(${unsafeCSS(bgCircle)}) 0% 50% no-repeat;
          background: #CBCBCB;
          background-size: auto 100%;
        }
    
        .actions {
          position: absolute;
          left: 0;
          top: 0;
          padding: 10px;
          height: 100%;
          overflow: auto;
        }
    
          button {
            font-size: 12px;
          }
          
        .progress-bar {
          position: absolute;
          left: 0;
          height: 2px;
          background: #f00;
          transition: width 0.3s;
        }
        
        li {
          position: relative;
        }
        .line-start {
          z-index: 100;
          position: absolute;
          left: 100%;
          top: 50%;
          width: 6px;
          height: 6px;
          margin-top: -3px;
          margin-left: 4px;
          border-radius: 50%;
          background: red;
        }
        
        .slot-item {
          pointer-events: auto;
          cursor: pointer;
          font-size:12px;
          color: #fff;
          width:12px;
          height:12px;
          background: rgba(255,255,255,0);
          border: 1px solid #fff;
          border-radius: 50%;
          transition: background .2s, width 0.14s, height 0.14s, border 0.14s;
          
          &.disable {
            cursor: not-allowed;
          }
          
          &.hidden {
            display: none;
          }
          
          &.drag-over {
            border-color: red;
          }
          
          &.can-install {
            position:relative;
            width:34px;
            height:34px;
            background:rgba(0,102,255,0.1);
            border-color: #0066FF;
            // &:before {
            //   content: "";
            //   position: absolute;
            //   left: 50%;
            //   top: 50%;
            //   width:3px;
            //   height:3px;
            //   transform: translate3d(-50%,-50%,0);
            //   border-radius: 50%;
            //   background: #00FF95;
            // }
          }
          &.filled {
            border-color: #00FF95;
          }

          &.selected {
            border-width: 4px;
          } 
          
          &.has-error {
            border-color: #ff0000;
          }
          
          &:hover {
            background: rgba(255,255,255,0.5);
          }
        
          &:hover:after {
            pointer-events: auto;
            transition: all .2s ease-in-out;
            opacity: 1;
            transform: translate3d(-50%,-4px,0);
          }
          
          &:after {
              pointer-events: none;
              content: attr(data-name);
              transition: all .2s 0.3s;
              opacity: 0;
              position:absolute;
              left: 50%;
              bottom: 100%;
              transform: translate3d(-50%,5px,0);
              white-space: nowrap;
              font-size: 12px;
              color: #000;
              padding: 2px 8px;
              border-radius: 2px;
              background: #fff;
            }
        }
  `
}
