import { Camera, Matrix4, Vector2, Vector3 } from 'three'
import { OrbitControls }                     from 'three/examples/jsm/controls/OrbitControls'
import EventEmitter                          from 'eventemitter3'

interface Axis {
  axis: string;
  direction: Vector3;
  size: number;
  color: string[];
  line?: number;
  label?: string;
  position: Vector3;
}

interface OrbitControlsGizmoProps {
  size?: number;
  offset: Vector2,
  padding?: number;
  bubbleSizePrimary?: number;
  bubbleSizeSecondary?: number;
  lineWidth?: number;
  fontSize?: number;
  fontFamily?: string;
  fontWeight?: string;
  fontColor?: string;
  className?: string;
  colors?: {
    x: string[];
    y: string[];
    z: string[];
  };
}

interface OrbitControlsGizmoOptions extends OrbitControlsGizmoProps {
  bubbleSizePrimary: number;
  bubbleSizeSecondary: number;
  size: number
  fontSize: number;
  colors: {
    x: string[];
    y: string[];
    z: string[];
  };
}

export class OrbitControlsGizmo {
  domElement: HTMLCanvasElement
  private lock: boolean = false
  private lockX: boolean = false
  private lockY: boolean = false
  private orbit: OrbitControls
  private camera: Camera
  private invRotMat: Matrix4 = new Matrix4()
  private mouse: Vector3 = new Vector3()
  private rotateStart: Vector2 = new Vector2()
  private rotateEnd: Vector2 = new Vector2()
  private rotateDelta: Vector2 = new Vector2()
  private center: Vector3
  private axes: Axis[]
  private selectedAxis: Axis | null = null
  private isDragging: boolean = false
  private context: CanvasRenderingContext2D | null = null
  private rect: DOMRect | undefined
  private orbitState: boolean = true

  private _animator = 0

  options: OrbitControlsGizmoOptions

  _emitter = new EventEmitter()

  addListener = this._emitter.addListener.bind(this._emitter)
  removeAllListeners = this._emitter.removeAllListeners.bind(this._emitter)

  private _text = document.createElement('div')

  private _textTimer = 0

  constructor(orbitControls: OrbitControls, options: OrbitControlsGizmoProps) {
    const defaults: OrbitControlsGizmoOptions = {
      offset: new Vector2(),
      size: 90,
      padding: 8,
      bubbleSizePrimary: 8,
      bubbleSizeSecondary: 6,
      lineWidth: 2,
      fontSize: 11,
      fontFamily: 'arial',
      fontWeight: 'normal',
      fontColor: '#fff',
      className: 'orbit-controls-gizmo',
      colors: {
        x: ['#f35f5f', '#902525'],
        y: ['#78da2f', '#71ae45'],
        z: ['#1d92fa', '#11518b']
      }
    }

    this.options = {
      ...defaults,
      ...options
    }
    this.orbit = orbitControls
    this.camera = orbitControls.object
    this.center = new Vector3(this.options.size! / 2, this.options.size! / 2, 0)
    this.axes = this.createAxes()

    this.domElement = this.createCanvas(this.options)
    this.orbit.addEventListener('change', this._update)

    this._text.style.cssText = `
      transition: all 0.8s ease-in-out;
      color: rgb(68, 78, 105);
      position: absolute;
      right: ${this.options.offset.x + this.options.size / 4}px;
      top: ${this.options.offset.y + this.options.size / 2 + 2}px;
      width: ${this.options.size / 2}px;
      margin-right: ${-this.options.size / 4}px;
      font-size: 12px;
      text-align:center;
    `
    // this.orbit.addEventListener('start', () => this.domElement.classList.add('inactive'))
    // this.orbit.addEventListener('end', () => this.domElement.classList.remove('inactive'))
  }

  appendTo(parent: HTMLElement) {
    parent.append(this.domElement)
    parent.append(this._text)
  }

  private _update = (): void => {
    if (this.lock) return

    if (this._textTimer) {
      window.clearTimeout(this._textTimer)
      this._textTimer = 0
    }

    this._textTimer = window.setTimeout(() => {
      this._text.style.opacity = '0'
    }, 800)

    this.camera.updateMatrix()
    this.invRotMat.extractRotation(this.camera.matrix).invert()

    this.axes.forEach(axis => {
      this.setAxisPosition(axis)
    })

    this.axes.sort((a, b) => (a.position.z > b.position.z ? 1 : -1))

    this.drawLayers(true)
    this._text.style.opacity = '1'
    this._text.innerText = this.camera.position.distanceTo(this.orbit.target).toFixed(2) + ' m'
  }

  private createAxes(): Axis[] {
    const {
      colors, lineWidth, bubbleSizePrimary, bubbleSizeSecondary
    } = this.options
    return [
      {
        axis: 'x',
        direction: new Vector3(1, 0, 0),
        size: bubbleSizePrimary,
        color: colors.x,
        line: lineWidth,
        label: '前',
        position: new Vector3()
      },
      {
        axis: 'y',
        direction: new Vector3(0, 1, 0),
        size: bubbleSizePrimary,
        color: colors.y,
        line: lineWidth,
        label: '上',
        position: new Vector3()
      },
      {
        axis: 'z',
        direction: new Vector3(0, 0, 1),
        size: bubbleSizePrimary,
        color: colors.z,
        line: lineWidth,
        label: '右',
        position: new Vector3()
      },
      {
        axis: '-x',
        direction: new Vector3(-1, 0, 0),
        size: bubbleSizeSecondary,
        color: colors.x,
        label: '后',
        position: new Vector3()
      },
      {
        axis: '-y',
        direction: new Vector3(0, -1, 0),
        size: bubbleSizeSecondary,
        color: colors.y,
        position: new Vector3()
      },
      {
        axis: '-z',
        direction: new Vector3(0, 0, -1),
        size: bubbleSizePrimary,
        color: colors.z,
        label: '左',
        position: new Vector3()
      }
    ]
  }

  private createCanvas(options: OrbitControlsGizmoOptions): HTMLCanvasElement {
    const canvas = document.createElement('canvas')
    canvas.width = options.size!
    canvas.height = options.size!
    canvas.classList.add(options.className!)
    canvas.style.cssText = `
      position: absolute;
      top: ${this.options.offset.y}px;
      right: ${this.options.offset.x}px;
      width: ${options.size! / 2}px;
      height: ${options.size! / 2}px;
      z-index: 1000;
      background-color: transparent;
      border-radius: 100%;
      transition: background-color .15s linear;
      cursor: pointer;
    `

    canvas.addEventListener('pointerdown', this.onPointerDown, false)
    canvas.addEventListener('pointerenter', this.onPointerEnter, false)
    canvas.addEventListener('pointermove', this.onPointerMove, false)
    canvas.addEventListener('click', this.onMouseClick, false)

    this.context = canvas.getContext('2d')

    return canvas
  }

  private onPointerDown = (e: PointerEvent): void => {
    this.rotateStart.set(e.clientX, e.clientY)
    this.orbitState = this.orbit.enabled
    this.orbit.enabled = false
    window.addEventListener('pointermove', this.onDrag, false)
    window.addEventListener('pointerup', this.onPointerUp, false)
  }

  private onPointerUp = (): void => {
    this.domElement.style.backgroundColor = '#FFF0'

    setTimeout(() => (this.isDragging = false), 0)
    this.domElement.classList.remove('dragging')
    this.orbit.enabled = this.orbitState
    window.removeEventListener('pointermove', this.onDrag, false)
    window.removeEventListener('pointerup', this.onPointerUp, false)
  }

  private onPointerEnter = (): void => {
    this.domElement.style.backgroundColor = '#FFF3'
    this.rect = this.domElement.getBoundingClientRect()
  }

  private onPointerMove = (e: PointerEvent | undefined): void => {
    if (this.isDragging || this.lock) return

    const currentAxis = this.selectedAxis

    this.selectedAxis = null
    if (e) {
      let {
        left, top
      } = this.rect!
      this.mouse.set((e.clientX - left) * 2, (e.clientY - top) * 2, 0)
    }

    this.axes.forEach(axis => {
      const distance = this.mouse.distanceTo(axis.position)
      if (distance < axis.size)
        this.selectedAxis = axis
    })

    if (currentAxis !== this.selectedAxis)
      this.drawLayers()
  }

  private onDrag = (e: PointerEvent): void => {
    if (this.lock) return

    if (!this.isDragging)
      this.domElement.classList.add('dragging')

    this.isDragging = true

    this.selectedAxis = null

    this.rotateEnd.set(e.clientX, e.clientY)
    this.rotateDelta.subVectors(this.rotateEnd, this.rotateStart).multiplyScalar(0.5)

    // if (!this.lockX)
    // this.orbit.rotateLeft(2 * Math.PI * this.rotateDelta.x / this.domElement.clientHeight)

    // if (!this.lockY)
    // this.orbit.rotateUp(2 * Math.PI * this.rotateDelta.y / this.domElement.clientHeight)

    this.rotateStart.copy(this.rotateEnd)

    this.orbit.update()
  }

  private onMouseClick = (): void => {
    if (this.isDragging || !this.selectedAxis) return
    if (this._animator) {
      cancelAnimationFrame(this._animator)
    }

    const vec = this.selectedAxis.direction.clone()
    const distance = this.camera.position.distanceTo(this.orbit.target)

    vec.multiplyScalar(distance)
    if (this.selectedAxis.axis === 'y') {
      vec.setY(this.orbit.target.y + distance)
    } else if (this.selectedAxis.axis === '-y') {
      vec.setY(this.orbit.target.y - distance)
    } else {
      vec.setY(this.orbit.target.y)
    }

    const duration = 500
    const start = performance.now()
    const maxAlpha = 1

    const axis = this.selectedAxis.axis

    const loop = () => {
      const now = performance.now()
      const delta = now - start
      const alpha = Math.min(delta / duration, maxAlpha)
      this.camera.position.lerp(vec, alpha)
      this.orbit.update()

      if (alpha !== maxAlpha) {
        this._animator = window.requestAnimationFrame(loop)
        return
      } else {
        this._animator = 0
        this.onPointerMove(undefined)
        this._emitter.emit('axis-select-end', axis)
      }

      this.onPointerMove(undefined)
    }
    this._emitter.emit('axis-select-start', axis)

    loop()
  }

  private drawCircle = (p: Vector3, radius = 10, color = '#FF0000'): void => {
    if (!this.context) return
    this.context.beginPath()
    this.context.arc(p.x, p.y, radius, 0, 2 * Math.PI, false)
    this.context.fillStyle = color
    this.context.fill()
    this.context.closePath()
  }

  private drawLine = (p1: Vector3, p2: Vector3, width = 1, color = '#FF0000'): void => {
    if (!this.context) return
    this.context.beginPath()
    this.context.moveTo(p1.x, p1.y)
    this.context.lineTo(p2.x, p2.y)
    this.context.lineWidth = width
    this.context.strokeStyle = color
    this.context.stroke()
    this.context.closePath()
  }

  private drawLayers = (clear = false): void => {
    if (!this.context) return
    if (clear) this.context.clearRect(0, 0, this.domElement.width, this.domElement.height)

    this.axes.forEach(axis => {
      const highlight = this.selectedAxis === axis
      const color = (axis.position.z >= -0.01) ? axis.color[0] : axis.color[1]

      if (axis.line) this.drawLine(this.center, axis.position, axis.line, color)
      this.drawCircle(axis.position, axis.size, highlight ? '#0066ff' : color)

      if (axis.label) {
        const fontSize = axis.axis === '-x' ? (this.options.fontSize - 3) : this.options.fontSize
        this.context!.font = [this.options.fontWeight, fontSize + 'px', this.options.fontFamily].join(' ')
        this.context!.fillStyle = this.options.fontColor!
        this.context!.textBaseline = 'middle'
        this.context!.textAlign = 'center'
        this.context!.fillText(axis.label, axis.position.x, axis.position.y)
      }
    })
  }

  private setAxisPosition = (axis: Axis): void => {
    const position = axis.direction.clone().applyMatrix4(this.invRotMat)
    const size = axis.size
    axis.position.set(
      (position.x * (this.center.x - (size / 2) - this.options.padding!)) + this.center.x,
      this.center.y - (position.y * (this.center.y - (size / 2) - this.options.padding!)),
      position.z
    )
  }

  destroy() {
    cancelAnimationFrame(this._animator)
    this.orbit.removeEventListener('change', this._update)
    this.orbit.enabled = this.orbitState
    window.removeEventListener('pointermove', this.onDrag, false)
    window.removeEventListener('pointerup', this.onPointerUp, false)
    this._emitter.removeAllListeners()

    this.domElement.addEventListener('pointerdown', this.onPointerDown, false)
    this.domElement.addEventListener('pointerenter', this.onPointerEnter, false)
    this.domElement.addEventListener('pointermove', this.onPointerMove, false)
    this.domElement.addEventListener('click', this.onMouseClick, false)
    this.domElement.remove()
    window.clearTimeout(this._textTimer)
    this._text.remove()
  }
}
