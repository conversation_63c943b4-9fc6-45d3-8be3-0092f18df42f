import {
  Camera,
  Color,
  DirectionalLight,
  DirectionalLightHelper,
  DoubleSide,
  FloatType,
  Group,
  HalfFloatType,
  Light,
  Material,
  MathUtils,
  Mesh,
  MeshBasicMaterial,
  MeshLambertMaterial,
  NoColorSpace,
  Object3D,
  Plane,
  PlaneGeometry,
  PlaneHelper,
  Scene,
  ShaderMaterial,
  UniformsUtils,
  Vector3,
  WebGLRenderer,
  WebGLRenderTarget
} from 'three'

interface ProgressiveShadowsParams {
  resolution?: number
  shadowMapRes?: number
  shadowBias?: number
  lightCount?: number
  size?: number
  frames?: number
  lightRadius?: number
  ambientWeight?: number
  alphaTest?: number
}

interface InternalParams {
  enabled: boolean
  frames: number
  lightRadius: number
  ambientWeight: number
  alphaTest: number
  debugHelpers: boolean
  size: number
}

interface LightInfo {
  object: Light
  intensity: number
}

interface MeshInfo {
  object: Object3D
  material: Material | Material[]
}

interface ObjectInfo {
  object: Object3D
  visible: boolean
}

class ProgressiveShadows {
  public params: InternalParams
  public scene: Scene
  public renderer: WebGLRenderer
  public buffer1Active: boolean
  public dirLights: DirectionalLight[]
  public dirLightsHelpers: DirectionalLightHelper[]
  public clearColor: Color
  public clearAlpha: number
  public progress: number
  public discardMaterial: any
  public lights: LightInfo[]
  public meshes: MeshInfo[]
  public objectsToHide: ObjectInfo[]
  public framesDone: number
  public fixedCamera: Camera | null
  public isRendering: boolean
  public progShadowGrp: Group
  public lightOrigin: Group
  public lightGroup: Group
  public progressiveLightMap1: WebGLRenderTarget
  public progressiveLightMap2: WebGLRenderTarget
  public shadowCatcherMaterial: any
  public shadowCatcherMesh: Mesh
  public debugHelpersGroup: Group
  public targetMat: MeshLambertMaterial
  public previousShadowMap: { value: any }
  public averagingWindow: { value: number }

  set visible(v: boolean) {
    this.shadowCatcherMesh.visible = v
    this.progShadowGrp.visible = v
  }

  constructor(
    renderer: WebGLRenderer,
    scene: Scene,
    {
      resolution = 1024,
      shadowMapRes = 512,
      shadowBias = 0.001,
      lightCount = 8,
      size = 4,
      frames = 40,
      lightRadius = 2,
      ambientWeight = 0.5,
      alphaTest = 0.98,
    }: ProgressiveShadowsParams = {}
  ) {
    this.params = {
      enabled: true,
      frames,
      lightRadius,
      ambientWeight,
      alphaTest,
      debugHelpers: false,
      size,
    }
    this.scene = scene
    this.renderer = renderer
    this.buffer1Active = false
    this.dirLights = []
    this.dirLightsHelpers = []
    this.clearColor = new Color()
    this.clearAlpha = 0
    this.progress = 0
    this.discardMaterial = new DiscardMaterial()
    this.lights = []
    this.meshes = []
    this.objectsToHide = []
    this.framesDone = 0

    // 添加相机缓存
    this.fixedCamera = null
    this.isRendering = false

    // All objects3d made by this class is added here , which is added to the scene
    this.progShadowGrp = new Group()
    this.progShadowGrp.name = "progressive_shadow_assets"
    this.scene.add(this.progShadowGrp)

    //light position control (only the position of this object used , lights are added to light group)
    this.lightOrigin = new Group()
    this.lightOrigin.name = "light_origin"
    this.lightOrigin.position.set(size, size, size)
    this.progShadowGrp.add(this.lightOrigin)

    // All lights are added to this group
    this.lightGroup = new Group()
    this.lightGroup.name = "all_dir_lights"
    this.progShadowGrp.add(this.lightGroup)

    // create 8 directional lights to speed up the convergence
    // Note: In r155+, light intensity is no longer scaled by PI internally
    // So we need to adjust the intensity accordingly
    const intensityMultiplier = Math.PI
    for (let l = 0; l < lightCount; l++) {
      const dirLight = new DirectionalLight(0xffffff, intensityMultiplier / lightCount)
      dirLight.name = "dir_light_" + l
      dirLight.castShadow = true
      dirLight.shadow.bias = shadowBias
      dirLight.shadow.camera.near = 0.1
      dirLight.shadow.camera.far = 50
      dirLight.shadow.camera.right = size / 2
      dirLight.shadow.camera.left = -size / 2
      dirLight.shadow.camera.top = size / 2
      dirLight.shadow.camera.bottom = -size / 2
      dirLight.shadow.mapSize.width = shadowMapRes
      dirLight.shadow.mapSize.height = shadowMapRes
      this.dirLights.push(dirLight)
      this.lightGroup.add(dirLight)
    }

    // Create the Progressive LightMap Texture
    const format = /(Android|iPad|iPhone|iPod)/g.test(navigator.userAgent) ? HalfFloatType : FloatType
    this.progressiveLightMap1 = new WebGLRenderTarget(resolution, resolution, { type: format })
    this.progressiveLightMap2 = new WebGLRenderTarget(resolution, resolution, { type: format })
    // Set color space for render targets
    this.progressiveLightMap1.texture.colorSpace = NoColorSpace
    this.progressiveLightMap2.texture.colorSpace = NoColorSpace

    // Material applied to shadow catching plane
    this.shadowCatcherMaterial = new SoftShadowMaterial({
      map: this.progressiveLightMap2.texture,
      transparent: true,
      opacity: 1.0,
    })

    // Create plane to catch shadows
    this.shadowCatcherMesh = new Mesh(new PlaneGeometry(size, size).rotateX(-Math.PI / 2), this.shadowCatcherMaterial)
    this.shadowCatcherMesh.position.y = 0.001 // avoid z-flicker
    this.shadowCatcherMesh.name = "shadowCatcherMesh"
    this.shadowCatcherMesh.receiveShadow = true
    this.progShadowGrp.add(this.shadowCatcherMesh)

    // Create group to add assets to debug shadow catcher
    this.debugHelpersGroup = new Group()

    //Inject some spicy new logic into a standard Lambert material
    this.targetMat = new MeshLambertMaterial({ fog: false })
    this.previousShadowMap = { value: this.progressiveLightMap1.texture }
    this.averagingWindow = { value: frames }
    this.targetMat.onBeforeCompile = (shader) => {
      // Vertex Shader: Set Vertex Positions to the Unwrapped UV Positions
      shader.vertexShader =
        "varying vec2 vUv;\n" + shader.vertexShader.slice(0, -1) + "vUv = uv; gl_Position = vec4((uv - 0.5) * 2.0, 1.0, 1.0); }"

      // Fragment Shader: Set Pixels to average in the Previous frame's Shadows
      const bodyStart = shader.fragmentShader.indexOf("void main() {")
      shader.fragmentShader =
        "varying vec2 vUv;\n" +
        shader.fragmentShader.slice(0, bodyStart) +
        "uniform sampler2D previousShadowMap;\n	uniform float averagingWindow;\n" +
        shader.fragmentShader.slice(bodyStart - 1, -1) +
        `\nvec3 texelOld = texture2D(previousShadowMap, vUv).rgb;
        gl_FragColor.rgb = mix(texelOld, gl_FragColor.rgb, 1.0/ averagingWindow);
      }`

      // Set the Previous Frame's Texture Buffer and Averaging Window
      shader.uniforms.previousShadowMap = this.previousShadowMap
      shader.uniforms.averagingWindow = this.averagingWindow
    }
  }

  /**
   * This function renders each mesh one at a time into their respective surface maps
   * @param {Camera} camera Standard Rendering Camera
   */
  renderOnRenderTargets(camera: Camera): void {
    this.prepare()
    // this.targetMat.uniforms.averagingWindow = { value: this.params.blendWindow }
    this.averagingWindow.value = this.params.frames

    // Ping-pong two surface buffers for reading/writing
    const activeMap = this.buffer1Active ? this.progressiveLightMap1 : this.progressiveLightMap2
    const inactiveMap = this.buffer1Active ? this.progressiveLightMap2 : this.progressiveLightMap1

    // Render the object's surface maps
    this.renderer.setRenderTarget(activeMap)
    // this.targetMat.uniforms.previousShadowMap = { value: inactiveMap.texture }
    this.previousShadowMap.value = inactiveMap.texture

    this.buffer1Active = !this.buffer1Active
    this.renderer.render(this.scene, camera)

    this.finish()

    // Restore the original Render Target
    this.renderer.setRenderTarget(null)
  }

  /**
   * Make Debug assets visible
   * @param {boolean} visible Whether the debug plane should be visible
   */
  showDebugHelpers(visible: boolean): void {
    if (!this.debugHelpersGroup.children.length) {
      const renderTargetDisplayHelper = new Mesh(
        new PlaneGeometry(2, 2),
        new MeshBasicMaterial({
          map: this.progressiveLightMap1.texture,
          side: DoubleSide,
        })
      )
      renderTargetDisplayHelper.position.y = this.params.size / 2

      for (const dirLight of this.dirLights) {
        const helpers = new DirectionalLightHelper(dirLight)
        this.dirLightsHelpers.push(helpers)
      }

      const shadowAreaHelper = new PlaneHelper(new Plane(new Vector3(0, 1, 0), 0.001), this.params.size, 0xffff00)
      this.debugHelpersGroup.add(renderTargetDisplayHelper, shadowAreaHelper, ...this.dirLightsHelpers)
    }

    if (visible) {
      this.progShadowGrp.add(this.debugHelpersGroup)
      this.dirLightsHelpers.forEach((h) => {
        h.update()
      })
    } else {
      this.progShadowGrp.remove(this.debugHelpersGroup)
    }
  }

  /**
   * Randomise direction lights
   */
  randomiseLights(): void {
    const length = this.lightOrigin.position.length()
    // Manually Update the Directional Lights
    for (let l = 0; l < this.dirLights.length; l++) {
      // Sometimes they will be sampled from the target direction
      // Sometimes they will be uniformly sampled from the upper hemisphere
      if (Math.random() > this.params.ambientWeight) {
        this.dirLights[l].position.set(
          this.lightOrigin.position.x + MathUtils.randFloatSpread(this.params.lightRadius),
          this.lightOrigin.position.y + MathUtils.randFloatSpread(this.params.lightRadius),
          this.lightOrigin.position.z + MathUtils.randFloatSpread(this.params.lightRadius)
        )
      } else {
        // Uniform Hemispherical Surface Distribution for Ambient Occlusion
        const lambda = Math.acos(2 * Math.random() - 1) - 3.14159 / 2.0
        const phi = 2 * 3.14159 * Math.random()
        this.dirLights[l].position.set(
          Math.cos(lambda) * Math.cos(phi) * length,
          Math.abs(Math.cos(lambda) * Math.sin(phi) * length),
          Math.sin(lambda) * length
        )
      }

      if (this.params.debugHelpers) this.dirLightsHelpers[l].update()
    }
  }

  /**
   * Trigger an update
   */
  async recalculate(): Promise<void> {
    if (!this.params.enabled) return
    this.clear()
    this.framesDone = 0
    // 重置相机缓存
    this.fixedCamera = null
    this.isRendering = false
  }

  /**
   * Prepare all meshes/lights
   */
  prepare(): void {
    this.lights.forEach((l) => (l.object.intensity = 0))
    this.meshes.forEach((m) => ((m.object as any).material = this.discardMaterial))
    this.objectsToHide.forEach((m) => (m.object.visible = false))

    this.lightGroup.visible = true
    this.shadowCatcherMesh.material = this.targetMat
    if (this.params.debugHelpers) this.showDebugHelpers(false)
  }

  /**
   * Restore all meshes/lights
   */
  finish(): void {
    this.lights.forEach((l) => (l.object.intensity = l.intensity))
    this.meshes.forEach((m) => ((m.object as any).material = m.material))
    this.objectsToHide.forEach((m) => (m.object.visible = m.visible))
    this.lightGroup.visible = false
    this.shadowCatcherMesh.material = this.shadowCatcherMaterial
    if (this.params.debugHelpers) this.showDebugHelpers(true)
  }

  /**
   * Clear the shadow Target & update mesh list
   * Call this once after all the models are loaded
   */
  clear(): void {
    this.renderer.getClearColor(this.clearColor)
    this.clearAlpha = this.renderer.getClearAlpha()
    this.renderer.setClearColor("black", 1) // setting to any other color/alpha will decrease shadow's impact when accumulating
    this.renderer.setRenderTarget(this.progressiveLightMap1)
    this.renderer.clear()
    this.renderer.setRenderTarget(this.progressiveLightMap2)
    this.renderer.clear()
    this.renderer.setRenderTarget(null)
    this.renderer.setClearColor(this.clearColor, this.clearAlpha);

    (this.shadowCatcherMesh.material as any).alphaTest = 0.0

    this.updateShadowObjectsList()
  }

  /**
   * Update list of meshes which need to be hidden
   */
  updateShadowObjectsList(): void {
    this.lights.length = 0
    this.meshes.length = 0
    this.objectsToHide.length = 0
    this.scene.traverse((object: Object3D) => {
      if ((object as any).geometry && object !== this.shadowCatcherMesh) {
        if (object.castShadow) {
          this.meshes.push({ object, material: (object as any).material })
        } else {
          this.objectsToHide.push({ object, visible: object.visible })
        }
      } else if ((object as any).isTransformControls) {
        this.objectsToHide.push({ object, visible: object.visible })
      } else if ((object as any).isLight && object.parent !== this.lightGroup) {
        this.lights.push({ object: object as Light, intensity: (object as any).intensity })
      }
    })

    // console.log({ meshes: this.meshes, lights: this.lights, objectsToHide: this.objectsToHide })
  }

  /**
   * Add this function to animate loop
   * @param {Camera} camera
   */
  update(camera: Camera): void {
    if (!this.params.enabled || this.framesDone >= this.params.frames) return

    // 在第一帧时缓存相机
    if (this.framesDone === 0) {
      this.fixedCamera = camera.clone()
      this.isRendering = true
    }

    (this.shadowCatcherMesh.material as any).alphaTest = MathUtils.clamp(
      MathUtils.mapLinear(this.framesDone, 2, this.params.frames - 1, 0, this.params.alphaTest),
      0,
      1
    )

    // 使用固定的相机进行渲染
    this.renderOnRenderTargets(this.fixedCamera || camera)
    this.randomiseLights()
    this.progress = MathUtils.mapLinear(this.framesDone, 0, this.params.frames - 1, 0, 100)

    this.framesDone++

    // 渲染完成后清理
    if (this.framesDone >= this.params.frames) {
      this.fixedCamera = null
      this.isRendering = false
    }
  }

  /**
   * 销毁所有资源，清理内存
   */
  destroy(): void {
    // 从场景中移除主组
    if (this.scene && this.progShadowGrp) {
      this.scene.remove(this.progShadowGrp)
    }

    // 清理渲染目标
    if (this.progressiveLightMap1) {
      this.progressiveLightMap1.dispose()
      ;(this as any).progressiveLightMap1 = null
    }
    if (this.progressiveLightMap2) {
      this.progressiveLightMap2.dispose()
      ;(this as any).progressiveLightMap2 = null
    }

    // 清理材质
    if (this.shadowCatcherMaterial) {
      this.shadowCatcherMaterial.dispose()
      ;(this as any).shadowCatcherMaterial = null
    }
    if (this.targetMat) {
      this.targetMat.dispose()
      ;(this as any).targetMat = null
    }
    if (this.discardMaterial) {
      this.discardMaterial.dispose()
      ;(this as any).discardMaterial = null
    }

    // 清理网格
    if (this.shadowCatcherMesh) {
      if ((this.shadowCatcherMesh as any).geometry) {
        (this.shadowCatcherMesh as any).geometry.dispose()
      }
      ;(this as any).shadowCatcherMesh = null
    }

    // 清理灯光
    this.dirLights.forEach((light) => {
      if (light.parent) {
        light.parent.remove(light)
      }
      const lightAny = light as any
      if (lightAny.dispose && typeof lightAny.dispose === 'function') {
        lightAny.dispose()
      }
    })
    ;(this as any).dirLights = []

    // 清理灯光辅助对象
    this.dirLightsHelpers.forEach((helper) => {
      if (helper.parent) {
        helper.parent.remove(helper)
      }
      const helperAny = helper as any
      if (helperAny.dispose && typeof helperAny.dispose === 'function') {
        helperAny.dispose()
      }
    })
    ;(this as any).dirLightsHelpers = []

    // 清理调试辅助对象组
    if (this.debugHelpersGroup) {
      this.debugHelpersGroup.children.forEach((child: any) => {
        if (child.geometry) child.geometry.dispose()
        if (child.material) child.material.dispose()
      })
      this.debugHelpersGroup.clear()
      ;(this as any).debugHelpersGroup = null
    }

    // 清理组
    if (this.progShadowGrp) {
      this.progShadowGrp.clear()
      ;(this as any).progShadowGrp = null
    }
    if (this.lightGroup) {
      this.lightGroup.clear()
      ;(this as any).lightGroup = null
    }
    if (this.lightOrigin) {
      this.lightOrigin.clear()
      ;(this as any).lightOrigin = null
    }

    // 清理数组
    ;(this as any).lights = []
    ;(this as any).meshes = []
    ;(this as any).objectsToHide = []

    // 清理其他引用
    ;(this as any).scene = null
    ;(this as any).renderer = null
    this.fixedCamera = null
    ;(this as any).clearColor = null
    ;(this as any).previousShadowMap = null
    ;(this as any).averagingWindow = null
    ;(this as any).params = null
  }
}

/**
 * r3f shader material which makes editing uniforms easy
 * @param {Object} uniforms
 * @param {String} vertexShader
 * @param {String} fragmentShader
 * @param {Function} onInit
 * @returns
 */
function shaderMaterial(
  uniforms: Record<string, any> = {},
  vertexShader: string,
  fragmentShader: string,
  onInit: (material: any) => void = (material: any) => {}
): any {
  const material = class extends ShaderMaterial {
    constructor(parameters: Record<string, any> = {}) {
      const entries = Object.entries(uniforms)
      // Create uniforms and shaders
      super({
        uniforms: entries.reduce((acc, [name, value]) => {
          const uniform = UniformsUtils.clone({ [name]: { value } })
          return {
            ...acc,
            ...uniform,
          }
        }, {}),
        vertexShader,
        fragmentShader,
      })
      // Create getter/setters
      entries.forEach(([name]) =>
        Object.defineProperty(this, name, {
          get: () => this.uniforms[name].value,
          set: (v) => (this.uniforms[name].value = v),
        })
      )

      // Assign parameters, this might include uniforms
      Object.assign(this, parameters)
      // Call onInit
      if (onInit) onInit(this)
    }
  }
  ;(material as any).key = MathUtils.generateUUID()
  return material
}

/**
 * r3f shadow material
 */
const SoftShadowMaterial = shaderMaterial(
  {
    transparent: true,
    color: new Color(0, 0, 0),
    alphaTest: 0.0,
    opacity: 1.0,
    map: null,
    depthWrite: false,
    toneMapped: false,
    blend: 2.0,
  },
  `varying vec2 vUv;
   void main() {
     gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(position, 1.);
     vUv = uv;
   }`,
  `varying vec2 vUv;
   uniform sampler2D map;
   uniform vec3 color;
   uniform float opacity;
   uniform float alphaTest;
   uniform float blend;
   void main() {
     vec4 sampledDiffuseColor = texture2D(map, vUv);
     gl_FragColor = vec4(color * sampledDiffuseColor.r * blend, max(0.0, (1.0 - (sampledDiffuseColor.r + sampledDiffuseColor.g + sampledDiffuseColor.b) / alphaTest)) * opacity);
     #include <tonemapping_fragment>
     #include <colorspace_fragment>
   }`
)

/**
 * r3f Discard material which helps ignore materials when rendering
 */
const DiscardMaterial = shaderMaterial({}, "void main() { }", "void main() { gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0); discard;  }")

export { ProgressiveShadows }
