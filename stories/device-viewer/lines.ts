import { css, LitElement, svg }        from 'lit'
import { Vector2 }                     from 'three'
import { customElement, query, state } from 'lit/decorators.js'

/*
*  TODO：
* 1、拖拽功能
* 2、断开连接功能
* 3、切换功能
*
* */
export class Line {
  readonly split = '--'
  uuid = ''

  valid = false

  editable = false

  highlight = false

  constructor(
    public name: string,
    public slot: string,
    public start: Vector2,
    public end: Vector2) {
    this.uuid = name + this.split + slot
  }

  update(start: Vector2, end: Vector2) {
    this.start = start.clone()
    this.end = end.clone()
  }
}

@customElement('device-lines')
export class DeviceLines extends LitElement {

  @state()
  lines = new Map<string, Line>()

  @query('.container')
  container: HTMLDivElement | undefined

  constructor(public offset: Vector2, public heightOffset: number = 0) {
    super()
    this.setOffset(this.offset.x, this.offset.y)
  }

  setOffset(x: number, y: number) {
    this.offset.set(x, y)
    this.style.setProperty('--left', this.offset.x + 'px')
    this.style.setProperty('--top', this.offset.y + 'px')
  }

  setHeightOffset(height: number) {
    this.heightOffset = height
    this.style.setProperty('--heightOffset', this.heightOffset + 'px')
  }

  hide() {
    this.container?.classList.add('hidden')
  }

  show() {
    this.container?.classList.remove('hidden')
  }

  addLine(line: Line) {
    if (!this.lines.has(line.uuid)) {
      this.lines.set(line.uuid, line)
    }
  }

  clear() {
    this.lines.clear()
  }

  removeLine(line: Line) {
    this.lines.delete(line.uuid)
  }

  getLineByName(name: string) {
    const results: Line[] = []
    this.lines.forEach((r, key) => {

      if (r.name === name) {
        results.push(r)
      }
    })
    return results
  }


  getLineBySlot(slot: string) {
    const results: Line[] = []
    this.lines.forEach((r, key) => {
      if (r.slot === slot) {
        results.push(r)
      }
    })
    return results
  }

  getPath(line: Line) {
    const end = line.end.clone()
    end.set(end.x - this.offset.x, end.y - this.offset.y)
    const left = line.start.clone().add(end).multiplyScalar(0.5)
    const right = line.start.clone().add(end).multiplyScalar(0.7)
    return `M${line.start.x} ${line.start.y} 
    C${left.x} ${line.start.y}
    ${right.x} ${line.end.y}
    ${end.x} ${end.y}
    `
  }

  updateLine(line: Line) {
    this.lines.set(line.uuid, line)
  }

  forceUpdate() {
    this.requestUpdate()
  }

  protected render(): unknown {
    return svg`
      <svg class="container">
        <g>
          ${[...this.lines.values()].map(r => {
      return svg`<path 
class="${r.valid ? 'valid' : ''} ${r.editable ? 'editable' : ''} ${r.highlight ? 'highlight' : ''}" 
d="${this.getPath(r)}"></path>`
    })}
        </g>
      </svg>`
  }

  static styles = css`
    :host {
      --left: 0px;
      --top: 0px;
      position: absolute;
      left: var(--left);
      top: var(--top);
      pointer-events: none;
      width: calc(100% - var(--left));
      height: calc(100% - var(--top));
    }
    
    svg {
      width: 100%;
      height: calc(100% + var(--heightOffset));
      &.hidden {
        display: none; 
     }
    }
    
    path {
      stroke: #787878;
      fill: transparent;
      stroke-width: 0.5px;
      stroke-dasharray: 4 4;
      
      &.valid {
        stroke-width: 1px;
        stroke: #00FF95;
        stroke-dasharray: none;
        &:hover {
          cursor: pointer;
        }
      }
      
      &.highlight {
        stroke: #0066FF;
        cursor: pointer;
        stroke-width: 2px;
      }
      
      &.editable {
        pointer-events: auto;
        &:hover {
          cursor: pointer;
          stroke: #0066FF;
          stroke-width: 2px;
          stroke-dasharray: none;
        }
      }
    }
  `
}
