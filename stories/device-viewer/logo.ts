import { ClampToEdgeWrapping, Mesh, MeshStandardMaterial, Object3D, SRGBColorSpace, Texture, Vector2 } from 'three'
import {
  LogoProps
}                                                                                                      from '@/device-viewer/scene'
import BGTransparent
                                                                                                       from '@/device-viewer/asserts/bg-transparent.png'
import {
  textureLoader
}                                                                                                      from '@/device-viewer/amr'

export class Logo {
  name = ''
  imageUrl?: string
  target: Mesh

  parent: Object3D | null

  transparentTexture: Texture = textureLoader.load(BGTransparent)

  private _offscreen = new OffscreenCanvas(0, 0)

  get imageSize() {
    return this._props.imageSize
  }

  get size() {
    return this._props.size
  }

  private readonly _defaults: LogoProps

  get defaultConfigs() {
    return {
      canvasWidth: 200,
      canvasHeight: 200,
      scaleX: 1,
      scaleY: 1,
      x: 0,
      y: 0,
      ox: 0,
      oy: 0,
      url: this._defaults.imageUrl
    }
  }

  constructor(private _props: LogoProps) {
    this._defaults = { ..._props }
    this.target = _props.target
    this.parent = this.target.parent

    this.reset()
  }

  clear() {
    this.change()
  }

  change(url?: string, scale = new Vector2(1, 1), offset: Vector2 = new Vector2()) {
    this.imageUrl = url
    let texture: Texture | undefined
    if (url) {
      const image = new Image()
      image.crossOrigin = 'anonymous'
      image.src = url
      image.onload = async () => {
        let border = window.devicePixelRatio * 2
        this._offscreen.width = image.width + border
        this._offscreen.height = image.height + border
        const ctx = this._offscreen.getContext('2d')
        ctx?.clearRect(0, 0, this._offscreen.width, this._offscreen.height)
        ctx?.drawImage(image, border / 2, border / 2, image.width, image.height)

        texture = new Texture(this._offscreen)
        texture.minFilter = 1003
        texture.colorSpace = SRGBColorSpace
        texture.wrapS = texture.wrapT = ClampToEdgeWrapping
        texture.repeat.set(1 / scale.x, 1 / scale.y)
        texture.offset.set(offset.x, offset.y)
        texture.flipY = false
        texture.needsUpdate = true

        const m = this._props.target
        if (m.material) {
          (m.material as MeshStandardMaterial).map?.dispose()
          // @ts-ignore
          if (texture) {
            (m.material as MeshStandardMaterial).map = texture;
            (m.material as MeshStandardMaterial).opacity = 1
          } else {
            (m.material as MeshStandardMaterial).map = this.transparentTexture;
            (m.material as MeshStandardMaterial).opacity = 0
          }
        }
      }
    } else {
      const m = this._props.target
      if (m.material) {
        (m.material as MeshStandardMaterial).map = this.transparentTexture
      }
    }
  }

  reset() {
    this._props = this._defaults
    this.name = this._props.name
    this.imageUrl = this._props.imageUrl
    this.target = this._props.target

    this.change(this.imageUrl || '')
  }

  dispose() {
    // @ts-ignore
    this._offscreen = null
  }
}
