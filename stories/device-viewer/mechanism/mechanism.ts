import { Object3D }    from 'three'
import { CSS2DObject } from 'three/examples/jsm/renderers/CSS2DRenderer'
import { Tween }       from '@tweenjs/tween.js'
import RotateRight     from '../asserts/btn-rotate-right.png'
import RotateLeft     from '../asserts/btn-rotate-left.png'
import ArrowDown   from '../asserts/btn-arrow-down.png'
import RotateIcon   from '../asserts/rotate-circle.png'
import EventEmitter    from 'eventemitter3'

export interface UserData {
  [key: string]: any
}

export interface MechanismProps {
  name: ''
  userData?: UserData
  target: string
  default: number
  min: number
  max: number
}

export class Mechanism extends CSS2DObject {
  private _direction = 1
  private _controllingRotate = false
  private _controlling = false
  private _startAt = 0
  private _looper = 0

  private _initial = 0

  private _canRotate = false

  private _timer: any

  private _emitter = new EventEmitter()

  addListener = this._emitter.addListener.bind(this._emitter)
  removeListener = this._emitter.removeListener.bind(this._emitter)

  mechanism: Object3D | undefined

  constructor(public props: MechanismProps, public amr: Object3D) {
    const box = document.createElement('div')
    super(box)


    if (props.userData && props.userData.target) {
      this.name = props.userData.target.name
      amr.traverse(m => {
        if (m.name === props.userData!.target.name) {
          this.mechanism = m
        }
      })
    }

    box.style.fontSize = '30px'
    box.style.color = '#0066FF'
    box.style.cursor = 'pointer'

    box.style.pointerEvents = 'auto'

    const arrowUp = document.createElement('div')
    arrowUp.style.position = 'absolute'
    arrowUp.style.left = '0'
    arrowUp.style.transform = 'translateX(-50%) rotate(-180deg)'
    arrowUp.style.marginBottom = '5px'
    arrowUp.style.width = arrowUp.style.height = `44px`
    arrowUp.style.background = `url(${ArrowDown}) center no-repeat`
    arrowUp.style.backgroundSize = `100% auto`
    arrowUp.setAttribute('data-direction', 'up')
    arrowUp.style.transition = 'all .2s'
    const arrowDown = document.createElement('div')
    arrowDown.style.position = 'absolute'
    arrowDown.style.left = '0'
    arrowDown.style.transform = 'translateX(-50%)'
    arrowUp.style.bottom = arrowDown.style.top = '8px'
    arrowDown.style.width = arrowDown.style.height = arrowUp.style.width
    arrowDown.style.background = `url(${ArrowDown}) center no-repeat`
    arrowDown.style.backgroundSize = `100% auto`
    arrowDown.style.transition = 'all .2s'
    // arrowDown.style.transform = 'rotate(90deg)'
    arrowDown.setAttribute('data-direction', 'down')
    Array.from([arrowUp, arrowDown]).forEach(item => {
      item.addEventListener('mousemove', () => {
        item.style.filter = 'invert(100%)'
      })
      item.addEventListener('mouseleave', () => {
        // item.style.backgroundImage = `url(${ArrowDown})`
        item.style.filter = ''
      })
      item.addEventListener('pointerdown', () => {
        if (!this.mechanism) return
        this._controlling = true
        this._startAt = Date.now()
        this._initial = this.mechanism.position.y
        this._direction = item.getAttribute('data-direction') === 'up' ? 1 : -1
        this._update()
      })
      item.addEventListener('pointerup', () => {
        this._controlling = false
        cancelAnimationFrame(this._looper)
      })
      item.addEventListener('pointerleave', () => {
        this._controlling = false
        cancelAnimationFrame(this._looper)
      })
    })

    if (props.userData) {
      this._canRotate = props.userData.canRotate
      if (this._canRotate) {

        const { rotateTarget, rotateDuration, rotateStep, rotateMax, rotateMin } = props.userData
        let rotateTargetObject: Object3D | undefined
        if (rotateTarget) {
          amr.traverse(m => {
            if (m.name === rotateTarget.name) {
              rotateTargetObject = m
            }
          })
          if (rotateTargetObject) {
            const rotateCircle = document.createElement('div')
            rotateCircle.style.background = `url(${RotateIcon}) center no-repeat`
            rotateCircle.style.backgroundSize = `100% auto`
            rotateCircle.style.position = 'absolute'
            rotateCircle.style.top = '-50%'
            rotateCircle.style.left = '-50%'
            rotateCircle.style.width = '160px'
            rotateCircle.style.height = `80px`
            rotateCircle.style.transform = 'translateX(-50%) translateY(-50%)'
            box.append(rotateCircle)

            const arrowLeft = document.createElement('div')
            arrowLeft.style.background = `url(${RotateLeft}) center no-repeat`
            arrowLeft.style.backgroundSize = `100% auto`
            arrowLeft.style.width = arrowLeft.style.height = `26px`
            arrowLeft.style.position = 'absolute'
            arrowLeft.style.top = '0'
            arrowLeft.style.transform = 'translateY(-50%)'
            arrowLeft.style.marginBottom = '5px'
            arrowLeft.setAttribute('data-direction', 'left')
            box.append(arrowLeft)

            const arrowRight = document.createElement('div')
            arrowRight.style.background = `url(${RotateRight}) center no-repeat`
            arrowRight.style.backgroundSize = `100% auto`
            arrowRight.style.width = arrowRight.style.height = `26px`
            // arrowRight.innerText = '→'
            arrowRight.style.position = 'absolute'
            arrowLeft.style.left = arrowRight.style.right = '68px'
            arrowRight.style.top = '0'
            arrowRight.style.transform = 'translateY(-50%)'
            arrowRight.style.marginBottom = '5px'
            arrowRight.setAttribute('data-direction', 'right')
            box.append(arrowRight)

            Array.from([arrowLeft, arrowRight]).forEach(item => {
              item.addEventListener('mousemove', () => {
                item.style.filter = 'invert(100%)'
              })
              item.addEventListener('mouseleave', () => {
                item.style.filter = ''
              })
              item.addEventListener('pointerdown', () => {
                if (this._timer && this._timer.isPlaying()) return
                this._controllingRotate = true
                this._startAt = Date.now()
                this._direction = item.getAttribute('data-direction') === 'left' ? 1 : -1

                let targetY = (rotateStep / 180) * Math.PI * this._direction + rotateTargetObject!.rotation.y
                if (targetY > rotateMax / 180 * Math.PI) {
                  targetY = rotateMax / 180 * Math.PI
                } else if (targetY < rotateMin / 180 * Math.PI) {
                  targetY = rotateMin / 180 * Math.PI
                }
                this._timer = new Tween(rotateTargetObject!.rotation)
                  .to({
                    y: targetY
                  })
                  .duration(rotateDuration)
                  .start()
                // rotateTargetObject!.rotation.y = Math.PI / 2 * this._direction

                this._update()
              })
              item.addEventListener('pointerup', () => {
                this._controllingRotate = false
                cancelAnimationFrame(this._looper)
              })
              item.addEventListener('pointerleave', () => {
                this._controllingRotate = false
                cancelAnimationFrame(this._looper)
              })
            })
          }
        }
      }
    }

    box.append(arrowUp, arrowDown)

    this.scale.set(0.01, 0.01, 0.01)

    this.matrixAutoUpdate = false
    this.updateMatrix()

    // this._update()
  }

  private _update = () => {
    this._looper = requestAnimationFrame(this._update)
    if (this._controlling) {
      let duration = (Date.now() - this._startAt) / 200
      if (duration > 4) {
        duration = 4
      }
      let next = this._initial + 0.005 * duration * this._direction
      if (next > this.props.max || next < this.props.min) {
        if (this.mechanism) {
          if (next < this.props.min) {
            this.mechanism.position.setY(this.props.min)
            this._emitter.emit('change', this.props.min)
          } else if (next > this.props.max) {
            this.mechanism.position.setY(this.props.max)
            this._emitter.emit('change', this.props.max)
          }
        }
        return
      }
      // this.mechanism.traverse(m => {
      //   if (m.morphTargetInfluences?.length) {
      //     const index = m.morphTargetDictionary[this.props.target]
      //     m.morphTargetInfluences[index] = next
      //   }
      // })
      this._initial = next
      if (!this.mechanism) return
      this.mechanism.position.setY(next)
      this._emitter.emit('change', next)
    }
  }

  hide() {
    this.element.style.display = 'none'
    this.traverse(m => {
      m.visible = false
      if (m instanceof CSS2DObject) {
        m.element.style.visibility = 'hidden'
      }
    })
    this.visible = false
  }

  show() {
    this.element.style.display = 'auto'
    this.traverse(m => {
      m.visible = true
      if (m instanceof CSS2DObject) {
        m.element.style.visibility = 'visible'
      }
    })
    this.visible = true
  }

  dispose() {
    this._emitter.removeAllListeners()
    cancelAnimationFrame(this._looper)
    this.element.remove()
  }
}
