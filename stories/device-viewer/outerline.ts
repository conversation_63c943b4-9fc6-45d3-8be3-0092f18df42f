import { DoubleSide, LineDashedMaterial, Mesh, MeshBasicMaterial, Object3D, RingGeometry, Vector3 } from 'three'
import {
  CSS2DObject
}                                                                                                   from 'three/examples/jsm/renderers/CSS2DRenderer'
import { SizeLine }                                                             from './dimension/size-line'

export class Outerline extends Object3D {
  private _text = new RadiusText('')

  private _line = new SizeLine(undefined, false)

  private _box = new Mesh()

  constructor(public radius: number, height: number) {
    super()

    this._line.updateMaterial(new LineDashedMaterial({
      color: '#555',
      dashSize: 0.06,
      gapSize: 0.06
    }))
    this._line.renderOrder = 0

    this._box.geometry = new RingGeometry(radius, radius + 0.003, 128)
    this._box.material = new MeshBasicMaterial({
      color: '#666',
      side: DoubleSide
    })

    this.position.y = height

    this._box.rotateX(-Math.PI / 2)
    this.add(this._box)
    this.add(this._line)
    this.add(this._text)
  }

  hide() {
    this._text.hide()
    this._line.visible = false
  }

  show() {
    this._text.show()
    this._line.visible = true
  }

  update(radius: number) {
    this.radius = radius
    this._box.geometry?.dispose()
    this._box.geometry = new RingGeometry(radius, radius + 0.003, 128)

    this._text.update((radius * 1000).toFixed(0))
  }

  change(pos: Vector3) {
    const target = pos.clone()
    target.setY(0)
    target.normalize().multiplyScalar(this.radius)
    this._text.position.set(target.x, target.y, target.z)

    this._line.update(target, new Vector3())
  }
}

export class RadiusText extends CSS2DObject {
  private _wrapper = document.createElement('div')

  constructor(radius: string) {
    const box = document.createElement('div')
    super(box)
    this._wrapper.style.fontSize = '11px'
    this._wrapper.style.color = '#fff'
    this._wrapper.style.whiteSpace = 'nowrap'
    this._wrapper.style.position = 'absolute'
    this._wrapper.style.transform = 'translateX(-50%)'
    this._wrapper.style.top = '6px'
    this._wrapper.style.font = 'SF Pro SC,SF Pro Text,SF Pro Icons,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif'

    const circle = document.createElement('div')
    circle.style.position = 'relative'
    circle.style.zIndex = '10'
    circle.style.width = circle.style.height = '5px'
    circle.style.borderRadius = '50%'
    circle.style.background = '#666'
    box.append(this._wrapper, circle)

    this.update(radius)
  }

  show() {
    this.element.style.display = 'block'
  }

  hide() {
    this.element.style.display = 'none'
  }

  update(radius: string) {
    this._wrapper.innerText = '旋转半径：' + radius + ' mm'
  }
}
