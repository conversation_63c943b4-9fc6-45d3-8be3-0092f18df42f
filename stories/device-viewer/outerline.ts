import { DoubleSide, LineDashedMaterial, Mesh, MeshBasicMaterial, Object3D, RingGeometry, Vector3, BufferGeometry, BufferAttribute, Line, LineBasicMaterial } from 'three'
import {
  CSS2DObject
}                                                                                                   from 'three/examples/jsm/renderers/CSS2DRenderer'
import { SizeLine }                                                             from './dimension/size-line'

export class Outerline extends Object3D {
  private _text = new RadiusText('')

  private _line = new SizeLine(undefined, false)

  private _box = new Mesh()

  private _arc = new Line()

  private _previousPosition: Vector3 | null = null

  private _updateTimer: NodeJS.Timeout | null = null

  private readonly _updateDelay = 800 // 0.8秒延迟

  constructor(public radius: number, height: number) {
    super()

    this._line.updateMaterial(new LineDashedMaterial({
      color: '#555',
      dashSize: 0.06,
      gapSize: 0.06
    }))
    this._line.renderOrder = 0

    this._box.geometry = new RingGeometry(radius, radius + 0.003, 128)
    this._box.material = new MeshBasicMaterial({
      color: '#666',
      side: DoubleSide
    })

    // 初始化圆弧线
    this._arc.geometry = new BufferGeometry() // 初始化为空几何体
    this._arc.material = new LineBasicMaterial({
      color: '#ffffff',
      linewidth: 1 // 1像素线宽
    })
    this._arc.visible = false // 初始时隐藏

    this.position.y = height

    this._box.rotateX(-Math.PI / 2)
    // this._arc.rotateX(-Math.PI / 2)
    this.add(this._box)
    this.add(this._arc)
    this.add(this._line)
    this.add(this._text)
  }

  hide() {
    this._text.hide()
    this._line.visible = false
    this._arc.visible = false
  }

  show() {
    this._text.show()
    this._line.visible = true
    this._arc.visible = true
  }

  dispose() {
    // 清理定时器
    if (this._updateTimer) {
      clearTimeout(this._updateTimer)
      this._updateTimer = null
    }

    // 清理几何体
    if (this._box.geometry) {
      this._box.geometry.dispose()
    }
    if (this._arc.geometry) {
      this._arc.geometry.dispose()
    }

    // 隐藏圆弧
    this._arc.visible = false

    // 重置位置
    this._previousPosition = null
  }

  update(radius: number) {
    this.radius = radius
    this._box.geometry?.dispose()
    this._box.geometry = new RingGeometry(radius, radius + 0.003, 128)

    this._text.update((radius * 1000).toFixed(0))
  }

  change(pos: Vector3) {
    const target = pos.clone()
    target.setY(0)
    target.normalize().multiplyScalar(this.radius)
    this._text.position.set(target.x, target.y, target.z)

    this._line.update(target, new Vector3())

    // 如果有上一个位置，绘制圆弧
    if (this._previousPosition) {
      this._updateArc(this._previousPosition, target)
    }

    // 清除之前的定时器
    if (this._updateTimer) {
      clearTimeout(this._updateTimer)
    }

    // 设置新的定时器，0.8秒后重置位置信息并清理圆弧
    this._updateTimer = setTimeout(() => {
      this._previousPosition = null
      this._clearArc()
      this._updateTimer = null
    }, this._updateDelay)

    // 如果这是第一次调用或者定时器刚被重置，更新位置
    if (!this._previousPosition) {
      this._previousPosition = target.clone()
    }
  }

  private _updateArc(startPos: Vector3, endPos: Vector3) {
    // 计算起始和结束角度
    const startAngle = Math.atan2(startPos.z, startPos.x)
    const endAngle = Math.atan2(endPos.z, endPos.x)

    // 确保角度差不超过π，选择较短的弧
    let angleDiff = endAngle - startAngle
    if (angleDiff > Math.PI) {
      angleDiff -= 2 * Math.PI
    } else if (angleDiff < -Math.PI) {
      angleDiff += 2 * Math.PI
    }

    // 如果角度差太小，不绘制圆弧
    if (Math.abs(angleDiff) < 0.01) {
      this._arc.visible = false
      return
    }

    // 创建圆弧几何体
    const arcGeometry = this._createArcGeometry(startAngle, endAngle, this.radius)

    // 清理旧几何体
    if (this._arc.geometry) {
      this._arc.geometry.dispose()
    }

    this._arc.geometry = arcGeometry
    this._arc.visible = true
  }

  private _clearArc() {
    // 清理圆弧几何体
    if (this._arc.geometry) {
      this._arc.geometry.dispose()
    }
    this._arc.visible = false
  }

  private _createArcGeometry(startAngle: number, endAngle: number, radius: number): BufferGeometry {
    const segments = 64
    const vertices: number[] = []

    // 计算角度差，确保选择较短的弧
    let angleDiff = endAngle - startAngle
    if (angleDiff > Math.PI) {
      angleDiff -= 2 * Math.PI
    } else if (angleDiff < -Math.PI) {
      angleDiff += 2 * Math.PI
    }

    const segmentAngle = angleDiff / segments

    // 生成圆弧线的顶点
    for (let i = 0; i <= segments; i++) {
      const angle = startAngle + i * segmentAngle
      const cos = Math.cos(angle)
      const sin = Math.sin(angle)

      // 在指定半径上的点
      vertices.push(radius * cos, 0, radius * sin)
    }

    const geometry = new BufferGeometry()
    geometry.setAttribute('position', new BufferAttribute(new Float32Array(vertices), 3))

    return geometry
  }
}

export class RadiusText extends CSS2DObject {
  private _wrapper = document.createElement('div')

  constructor(radius: string) {
    const box = document.createElement('div')
    super(box)
    this._wrapper.style.fontSize = '11px'
    this._wrapper.style.color = '#fff'
    this._wrapper.style.whiteSpace = 'nowrap'
    this._wrapper.style.position = 'absolute'
    this._wrapper.style.transform = 'translateX(-50%)'
    this._wrapper.style.top = '6px'
    this._wrapper.style.font = 'SF Pro SC,SF Pro Text,SF Pro Icons,PingFang SC,Helvetica Neue,Helvetica,Arial,sans-serif'

    const circle = document.createElement('div')
    circle.style.position = 'relative'
    circle.style.zIndex = '10'
    circle.style.width = circle.style.height = '5px'
    circle.style.borderRadius = '50%'
    circle.style.background = '#666'
    box.append(this._wrapper, circle)

    this.update(radius)
  }

  show() {
    this.element.style.display = 'block'
  }

  hide() {
    this.element.style.display = 'none'
  }

  update(radius: string) {
    this._wrapper.innerText = '旋转半径：' + radius + ' mm'
  }
}
