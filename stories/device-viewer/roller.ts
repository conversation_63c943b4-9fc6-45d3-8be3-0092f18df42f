import {
  BoxGeometry,
  EdgesGeometry,
  Group,
  LineSegments,
  Mesh,
  MeshBasicMaterial,
  MeshStandardMaterial,
  Object3D,
  Vector3
}                                           from 'three'
import { AMRAssembly }                      from './assembly'
import Model                                from './asserts/models/roller.glb?url'
import EventEmitter                         from 'eventemitter3'
import { clearThreeObjectMesh, ObjectSize } from './utils'

export interface RollerProps {
  goodsSize: Vector3
}

export class Roller extends AMRAssembly {
  private _goodsWidthGap = 0.01
  private _goodsLengthGap = 0.005

  private _emitter = new EventEmitter()
  addEventListener = this._emitter.addListener.bind(this._emitter)

  private _shaft: Object3D | undefined
  private _shaftInstances: Object3D[] = []

  private _length = 0

  private _container = new Group()

  private _goodsContainer: Object3D | undefined

  private _goodsSize = new ObjectSize(0, 0, 0)

  private _goods: undefined | Mesh

  constructor(private _props: RollerProps) {
    super(Model)

    this.add(this._container)
  }

  get length() {
    return this._length
  }

  loaded(glb: Object3D) {
    this._container.add(glb.clone(true))
    this._container.traverse(m => {
      if (m.name.startsWith('Shaft')) {
        this._shaft = m
      }
      if (m.userData.isContainer) {
        this._goodsContainer = m
      }
      const key = 'length'
      // @ts-ignore
      if (Object.keys(m.morphTargetDictionary || {}).includes(key)) {
        // @ts-ignore
        const index = m.morphTargetDictionary[key]
        // @ts-ignore
        this._length = m.morphTargetInfluences[index]
      }
    })
    this._shaft?.removeFromParent()
    this.setGoodsSize({
      length: this.size.length,
      width: this.size.width,
      height: this.size.height
    })
  }

  setGoodsSize({
                 length, width, height
               }: {
    length: number
    width: number
    height: number
  }) {
    this._goodsSize.length = length
    this._goodsSize.width = width
    this._goodsSize.height = height

    if (this._goods) {
      clearThreeObjectMesh(this._goods)
      this._goods.removeFromParent()
    }

    this.size.length = length + this._goodsLengthGap * 2 + 0.12
    this.size.height = height + 0.115
    this.size.width = width + this._goodsWidthGap * 2 + 0.086

    this.setShapeKey('goodsWidth', width + this._goodsWidthGap * 2)
    this.setShapeKey('goodsLength', length + this._goodsLengthGap * 2)
    this._generateShaft()
    this._emitter.emit('size-change', this.size)

    const box = new BoxGeometry(this._goodsSize.width, height, this._goodsSize.length)
    box.translate(0, height / 2, 0)
    const edges = new EdgesGeometry(box)

    this._goods = new Mesh(box, new MeshStandardMaterial({
      color: '#22f',
      opacity: 0.2,
      transparent: true
    }))
    const lines = new LineSegments(edges, new MeshBasicMaterial({
      color: '#22f'
    }))

    this._goods.add(lines)
    this._goodsContainer?.add(this._goods)
  }

  setShapeKey(key: string, value: number) {
    if (key === 'goodsLength') {
      this._length = value
    }
    const nodes: Object3D[] = []
    this._shaft?.traverse(m => nodes.push(m))
    this._container.traverse(m => nodes.push(m))
    nodes.forEach(node => {
      let val = value
      if (node.userData[`origin_${key}`]) {
        val -= node.userData[`origin_${key}`]
      }
      // @ts-ignore
      if (!Object.keys(node.morphTargetDictionary || {}).includes(key)) return

      // @ts-ignore
      const index = node.morphTargetDictionary[key]
      // @ts-ignore
      node.morphTargetInfluences && (node.morphTargetInfluences[index] = val)
    })
  }

  private _generateShaft() {
    if (!this._shaft) return
    if (this._shaftInstances.length) {
      this._shaftInstances.forEach(i => {
        clearThreeObjectMesh(i)
      })
    }

    this._shaftInstances = []
    const totalLength = this._length + 0.01
    const radius = 0.05
    let gap = 0.03519
    const length = radius + gap
    const instanceCount = Math.floor(totalLength / length)
    gap += (totalLength - (length * instanceCount + gap)) / (instanceCount + 1)
    const newLength = radius + gap
    for (let i = 0; i < instanceCount; i++) {
      const instance = this._shaft.clone(true)
      instance.position.z += newLength * i - (totalLength) / 2 + (radius / 2 + gap)
      this._container.add(instance)
      this._shaftInstances.push(instance)
    }
  }

  destroy() {
    this._emitter.removeAllListeners()
    // @ts-ignore
    this._emitter = null
    clearThreeObjectMesh(this)
  }
}
