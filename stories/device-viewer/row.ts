import { Group, Object3D } from 'three'
import { AMRAssembly }     from './assembly'
import EventEmitter        from 'eventemitter3'
import { ObjectSize }      from './utils'

export class Row extends Object3D {
  private _items: AMRAssembly[] = []
  private _group = new Group()
  private _gap = 0

  protected _size = new ObjectSize(0, 0, 0)

  private _emitter = new EventEmitter()
  addEventListener = this._emitter.addListener.bind(this._emitter)

  get size() {
    return this._size
  }

  get items() {
    return this._items
  }

  constructor(gap?: number) {
    super()
    this._gap = gap || 0

    this.add(this._group)
  }

  addItem(item: AMRAssembly) {
    this._items.push(item)
    this._group.add(item)

    this.updateLayout()
    item.addEventListener('size-change', () => {
      this.updateLayout()
    })
  }

  updateLayout() {
    // 计算所有物体加上间隔后的总宽度
    const totalWidth = this._items.reduce((acc: number, item: AMRAssembly, index: number) => {
      const { width, length } = item.size
      return acc + width + (index < this._group.children.length - 1 ? this._gap : 0)
    }, 0)

    let length = 0
    let height = 0
    // 居中排列
    let currentX = -totalWidth / 2

    this._items.forEach(item => {
      const width = item.size.width
      item.position.x = currentX + width / 2
      currentX += width + this._gap
      length = item.size.length
      height = item.size.height
    })

    this.size.width = totalWidth
    this.size.length = length
    this.size.height = height
    this._emitter.emit('size-change', this.size)
  }

  destroy() {
    this._items.forEach(item => {
      item.destroy()
    })
    this._items = []
    this._emitter.removeAllListeners()
  }
}
