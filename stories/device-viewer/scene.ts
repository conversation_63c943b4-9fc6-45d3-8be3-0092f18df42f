import {
  ACESFilmicToneMapping,
  Box3,
  Clock,
  Color,
  DoubleSide,
  ********************************,
  Euler,
  Group,
  LinearFilter,
  LoadingManager,
  Mesh,
  MeshBasicMaterial,
  MeshPhysicalMaterial,
  MeshStandardMaterial,
  Object3D,
  OrthographicCamera,
  PerspectiveCamera,
  PlaneGeometry,
  Raycaster,
  RGBAFormat,
  Scene,
  ShaderChunk,
  SRGBColorSpace,
  UnsignedByteType,
  Vector2,
  Vector3,
  WebGLRenderer,
  WebGLRenderTarget
}                                     from 'three'
import { RGBELoader }                 from 'three/examples/jsm/loaders/RGBELoader'
import { RenderPass }                 from 'three/examples/jsm/postprocessing/RenderPass'
import { OutlinePass }                from 'three/examples/jsm/postprocessing/OutlinePass'
import { ShaderPass }                 from 'three/examples/jsm/postprocessing/ShaderPass'
import { FXAAShader }                 from 'three/examples/jsm/shaders/FXAAShader'
import { UnrealBloomPass }            from 'three/examples/jsm/postprocessing/UnrealBloomPass'
import { OrbitControls }              from 'three/examples/jsm/controls/OrbitControls'
import { OutputPass }                 from 'three/examples/jsm/postprocessing/OutputPass'
// @ts-ignore
import { BVHWorker, WebGLPathTracer } from 'three-gpu-pathtracer'
import { EffectComposer }             from 'three/examples/jsm/postprocessing/EffectComposer'
import { CSS2DRenderer }              from 'three/examples/jsm/renderers/CSS2DRenderer'
import { Font }                       from 'three/examples/jsm/loaders/FontLoader'

import TWEEN, { Tween } from '@tweenjs/tween.js'
import EventEmitter     from 'eventemitter3'
import Stats            from 'stats-fps.js'
import { Pane }         from 'tweakpane'

import { calculateArrayAverage, calculateFocalLength, isAppleDevice } from '../utils'
import FontData                                                       from './asserts/fonts/type.json'
import { Sound }                                                      from './sound'
import { Slot, SLOT_EVENTS }                                          from './slot'
import { DeviceLines, Line }                                          from './lines'
import { gltfExporter }                                               from '../sglb/index'
import { clearThreeObjectMesh, getScaledSettings }                    from './utils'
import { Amr }                                                        from './amr'

// @ts-ignore
import { GenerateMeshBVHWorker } from './libs/BVH/workers/GenerateMeshBVHWorker.js'
import { Logo }                  from './logo'
import { OrbitControlsGizmo }    from '@/device-viewer/libs/gizmo/gizmo'
import { ProgressiveShadows }    from '@/device-viewer/libs/progress-shadow/ProgressiveShadows'
import { CameraPosition }        from '@/device-viewer/ui/camera-position'

// @ts-ignore
export const font = new Font(FontData)

export { Slot, DeviceLines, Line, SLOT_EVENTS, Logo }

export interface LogoProps {
  name: string
  target: Mesh
  size: {
    width: number
    height: number
  }
  imageSize: {
    width: number
    height: number
  }
  imageUrl?: string
}

export interface SkinProps {
  name: string
  target: MeshStandardMaterial
}


export class Skin {
  name = ''

  private _defaults = {
    color: ''
  }

  color = ''

  constructor(private _props: SkinProps) {
    this.name = this._props.name
    this.color = this._defaults.color = '#' + _props.target.color?.getHexString()
  }

  change(color: string) {
    this.color = color
    this._props.target.color?.set(color)
  }

  reset() {
    this.change(this._defaults.color)
  }
}

export enum SlotState {
  DEFAULT,
  CAN_INSTALL
}

const initialDamp = 0.15

export type Scene3DEvents = 'slot-select'

export interface SceneProps {
  useSound: boolean,
  fov?: number
  useAxisBall?: boolean
  axisBallOffset?: Vector2
  workerUrl?: string
}

let isTrackPad: undefined | boolean = undefined
let eventCount = 0
let eventCountStart = 0

const resetDetection = () => {
  isTrackPad = undefined
  eventCount = 0
  eventCountStart = 0
}

const depthMat = new MeshBasicMaterial({ colorWrite: false })

export class Scene3D {

  stats = new Stats()
  scene = new Scene()

  overlayScene = new Scene()
  renderer = new WebGLRenderer({
    antialias: true,
    alpha: true,
    logarithmicDepthBuffer: true,
    powerPreference: 'high-performance',
    preserveDrawingBuffer: true
  })

  private css2dRenderer = new CSS2DRenderer()
  // private css3dRenderer = new CSS3DRenderer()

  pathTracer

  mousePos = new Vector2()
  mouseCoord = new Vector2()
  offset = new Vector2()
  size = new Vector2()
  private _prevSelectedObj: Mesh | undefined

  amr: Amr = new Amr({ showSizeBox: true, showController: true })

  _composer
  _outlinePass
  _outlinePassError
  _fxaaPass
  _bloomPass

  private _progressShadow: ProgressiveShadows

  test: Mesh

  // protected _test = new Mesh(new PlaneGeometry(1, 1), new MeshStandardMaterial({
  //   side: DoubleSide,
  //   roughness: 0.2,
  //   metalness: 0.3,
  //   map: textureLoader.load('logo.png'),
  //   transparent: true
  // }))

  private _clock = new Clock(true)

  private _uiHiddenTime = this._clock.getElapsedTime()

  camera = new PerspectiveCamera(
    39.6,
    window.innerWidth / window.innerHeight,
    0.1,
    10000)

  private _mainCamera: PerspectiveCamera | OrthographicCamera

  d = 2
  orthographicCamera = new OrthographicCamera(
    -this.d * (window.innerWidth / window.innerHeight),
    this.d * (window.innerWidth / window.innerHeight),
    this.d,
    -this.d,
    0.1,
    1000
  )

  controls: OrbitControls
  private _cameraPositionUI: CameraPosition

  private _controlsGizmo: OrbitControlsGizmo | undefined
  private _axisMoving = false

  private _resizeObserver: ResizeObserver | undefined

  private _container = new Group()

  private _fitAnimation: Tween<any> | undefined

  private _raycaster = new Raycaster()
  private _selected: Object3D[] = []

  private _pointerDownTimeout = 0

  pane: Pane | undefined

  private _dragging = false

  readonly maxSample = 1024 * 2
  sampleCount = 0

  private _usePathTracing = false

  private _fpsCache: number[] = []
  private _fpsCacheMax = 20

  private _emitter = new EventEmitter()

  sound = new Sound()

  private _ground = new Mesh(new PlaneGeometry(100, 100),
    new MeshPhysicalMaterial({
      color: new Color('#eee'),
      metalness: 0.4,
      roughness: 0.1,
      clearcoat: 1,
      clearcoatRoughness: 0.1
    }))

  private _amrRender = new WebGLRenderTarget(1024, 1024, {
    format: RGBAFormat,
    type: UnsignedByteType,
    colorSpace: SRGBColorSpace,
    samples: 8
  })

  private _pointCloudMode = false

  get pointCloudMode() {
    return this._pointCloudMode
  }

  set pointCloudMode(enabled: boolean) {
    this._pointCloudMode = enabled

    this.amr.setPointCloudMode(enabled)

    if (enabled) {
      setTimeout(() => {
        console.log(this.amr.lines.children)
        // this._outlinePass.selectedObjects = this.amr.lines.children
      }, 3000)
    } else {
      this._outlinePass.selectedObjects = []
    }

    // 启用或禁用辉光效果
    if (this._bloomPass) {
      // this._bloomPass.enabled = enabled
    }

    // 设置背景颜色
    if (enabled) {
      // this.scene.background = new Color(0x000000) // 黑色背景
      // this.scene.background = new Color(0xffffff) // 黑色背景
      this.scene.fog = null
    } else {
      this.scene.background = null // 恢复默认背景
    }
  }

  get usePathTracing() {
    return this._usePathTracing
  }

  private _clearAmrContent() {
    const currentRenderTarget = this.renderer.getRenderTarget()
    // 设置 _amrRender 为当前渲染目标
    this.renderer.setRenderTarget(this._amrRender)
    this.renderer.setClearColor(0x000000, 0)
    this.renderer.clear(true, true, true)

    this.renderer.setRenderTarget(currentRenderTarget)
  }

  set usePathTracing(use: boolean) {
    // this._progressShadow.visible = !use
    this._clearAmrContent()
    if (use) {
      this.overlayScene.add(this._progressShadow.shadowCatcherMesh)

      this.amr && (this.amr.bodyOpacity = 1)

      this.hideAmrUI()
      this.pathTracer.renderToCanvas = true
      this.scene.background = null
    } else {
      this.scene.add(this._progressShadow.shadowCatcherMesh)

      this.pathTracer.renderToCanvas = false
      this.amr.logos.forEach(l => {
        l.parent?.add(l.target)
      })
      this.showAmrUI()

      if (this.amr && this.amr.opacityCache !== undefined) {
        this.amr.bodyOpacity = this.amr.opacityCache
        this.amr.opacityCache = undefined
      } else {
        this.amr && (this.amr.bodyOpacity = 1)
      }

      this.pathTracer.reset()

      this._ground.visible = false

      // this.scene.background = this.scene.environment
      // this.scene.add(this._test)
    }
    this._usePathTracing = use
    this.controls.dampingFactor = use ? 1 : initialDamp
    this.sampleCount = 0

  }

  get fpsAverage() {
    return calculateArrayAverage(this._fpsCache)
  }


  constructor(public props: SceneProps) {
    this.stats.showPanel(0)
    this.hideState()

    if (props.fov) {
      this.camera.fov = props.fov
      this.camera.updateProjectionMatrix()
    }

    this._ground.visible = false

    this.controls = new OrbitControls(this.camera, this.renderer.domElement)
    this.controls.enablePan = true
    this.controls.rotateSpeed = 0.5
    this.controls.dampingFactor = initialDamp
    this.controls.enableDamping = true
    this.controls.maxDistance = 10
    this.controls.minDistance = 1
    this.controls.zoomSpeed = isAppleDevice() ? 0.2 : 1

    this._cameraPositionUI = new CameraPosition(this.controls)

    this.renderer.setPixelRatio(window.devicePixelRatio || 1)
    this.renderer.autoClear = true
    this.renderer.toneMapping = ACESFilmicToneMapping
    this.renderer.toneMappingExposure = 1.2
    this.renderer.outputColorSpace = SRGBColorSpace
    this.renderer.localClippingEnabled = true
    this.renderer.shadowMap.enabled = true

    this.css2dRenderer.domElement.style.pointerEvents = 'none'
    this.css2dRenderer.domElement.style.position = 'absolute'
    this.css2dRenderer.domElement.style.top = '0px'

    // this.css3dRenderer.domElement.style.pointerEvents = 'none'
    // this.css3dRenderer.domElement.style.position = 'absolute'
    // this.css3dRenderer.domElement.style.top = '0px'

    ShaderChunk.tonemapping_pars_fragment =
      ShaderChunk.tonemapping_pars_fragment.replace(
        'vec3 CustomToneMapping( vec3 color ) { return color; }', `
      float startCompression = 0.8;
      float desaturation = 0.5;
      vec3 CustomToneMapping( vec3 color ) {
        color *= toneMappingExposure;

        float d = 1. - startCompression;

        float peak = max(color.r, max(color.g, color.b));
        if (peak < startCompression) return color;

        float newPeak = 1. - d * d / (peak + d - startCompression);
        float invPeak = 1. / peak;

        float extraBrightness = dot(color * (1. - startCompression * invPeak), vec3(1, 1, 1));

        color *= newPeak * invPeak;
        float g = 1. - 3. / (desaturation * extraBrightness + 3.);
        return mix(color, vec3(1, 1, 1), g);
      }`)

    this.camera.position.set(10, 6, -10)
    this.camera.lookAt(new Vector3())

    const { tiles } = getScaledSettings()
    this.pathTracer = new WebGLPathTracer(this.renderer)

    this.pathTracer.filterGlossyFactor = 0.6
    let renderScale = window.devicePixelRatio
    if (renderScale < 2) {
      renderScale = 2
    }
    this.pathTracer.renderScale = renderScale
    this.pathTracer.bounces = 10
    this.pathTracer.transmissiveBounces = 3
    this.pathTracer.tiles.set(tiles, tiles)
    this.pathTracer.fadeDuration = 0

    // this.pathTracer.tiles.set(1, 1)
    const worker = new GenerateMeshBVHWorker(props.workerUrl) as BVHWorker
    const DEFAULT_WORKER_COUNT = typeof navigator !== 'undefined' ? navigator.hardwareConcurrency : 4
    // @ts-ignore
    worker.maxWorkerCount = DEFAULT_WORKER_COUNT
    this.pathTracer.setBVHWorker(worker)

    this.orthographicCamera.left = -20
    this.orthographicCamera.right = 20
    this.orthographicCamera.top = -20
    this.orthographicCamera.bottom = 20

    // 限制只能Y轴移动 - 使用事件监听器方法
    let lastTargetX = this.controls.target.x
    let lastTargetZ = this.controls.target.z

    const restrictToYAxis = () => {
      this.controls.target.x = lastTargetX
      this.controls.target.z = lastTargetZ
    }
    this.controls.addEventListener('change', () => {
      this.sampleCount = 0

      restrictToYAxis()

      this._uiHiddenTime = this._clock.getElapsedTime()

      this.orthographicCamera.position.copy(this.camera.position)
      this.orthographicCamera.lookAt(this.controls.target)
      if (this.usePathTracing) {
        this.pathTracer.pausePathTracing = false
        this.pathTracer?.updateCamera()
      }
      this._updateCamera()
      if (Math.abs(this.camera.position.x) < 0.001 || Math.abs(this.camera.position.z) < 0.001) {
        return
      }
      if (!this._axisMoving) {
        this.mainCamera = this.camera
        if (this.usePathTracing) {
          this.generateBVH().then()
        }
      }
    })

    this._mainCamera = this.camera

    this._composer = new EffectComposer(this.renderer)
    this._composer.setPixelRatio(window.devicePixelRatio)
    const renderPass = new RenderPass(this.scene, this.camera)
    this._composer.addPass(renderPass)
    this._composer.addPass(new OutputPass())

    this._outlinePass = new OutlinePass(new Vector2(window.innerWidth, window.innerHeight), this.scene, this.camera)
    this._outlinePass.visibleEdgeColor.set(0x0066FF)
    this._outlinePass.edgeThickness = 0.5
    this._outlinePass.hiddenEdgeColor.set(0x0066FF)
    this._outlinePass.edgeStrength = 3
    this._outlinePass.edgeGlow = 0
    this._composer.addPass(this._outlinePass)

    this._outlinePassError = new OutlinePass(new Vector2(window.innerWidth, window.innerHeight), this.scene, this.camera)
    this._outlinePassError.visibleEdgeColor.set(0xff0000)
    this._outlinePassError.edgeThickness = 0.5
    this._outlinePassError.hiddenEdgeColor.set(0xff0000)
    this._outlinePassError.edgeStrength = 3
    this._outlinePassError.edgeGlow = 0
    this._composer.addPass(this._outlinePassError)

    // 添加辉光效果
    this._bloomPass = new UnrealBloomPass(
      new Vector2(window.innerWidth, window.innerHeight),
      0.1,    // strength - 降低辉光强度
      0.1,    // radius - 减小辉光范围
      0.2     // threshold - 提高阈值，只让最亮的部分产生辉光
    )
    this._bloomPass.enabled = false
    this._bloomPass.renderToScreen = false
    this._composer.addPass(this._bloomPass)

    // const gammaCorrectionPass = new ShaderPass(GammaCorrectionShader)
    // this._composer.addPass(gammaCorrectionPass)
    this._composer.renderTarget1.texture.colorSpace = SRGBColorSpace
    this._composer.renderTarget2.texture.colorSpace = SRGBColorSpace
    this._fxaaPass = new ShaderPass(FXAAShader)
    this._composer.addPass(this._fxaaPass)

    this._progressShadow = new ProgressiveShadows(this.renderer, this.scene, {
      size: 8,
      frames: 180,
      lightRadius: 8,
      ambientWeight: 0.8
    })
    this._progressShadow.lightOrigin.position.set(3, 5, 3)

    this._container.add(this.amr)
    this.scene.add(this._container)
    this.ticker()

    this._loadEnv()
    this._initEvents()

    this.scene.add(this._cameraPositionUI)

    this._ground.rotateX(-Math.PI / 2)
    // this._ground.position.y = -22.01
    this.scene.add(this._ground)

    // this._test.material.map!.flipY = false
    // this.scene.add(this._test)

    if (typeof props.useAxisBall !== 'boolean' || props.useAxisBall) {
      this._controlsGizmo = new OrbitControlsGizmo(this.controls, {
        size: 160,
        padding: 12,
        offset: props.axisBallOffset || new Vector2(10, 10),
        fontSize: 17,
        bubbleSizePrimary: 18,
        bubbleSizeSecondary: 14
      })
      this._controlsGizmo.addListener('axis-select-start', () => {
        this._axisMoving = true
        this.mainCamera = this.orthographicCamera

        if (this.usePathTracing) {
          this.generateBVH().then()
        }
      })
      this._controlsGizmo.addListener('axis-select-end', () => {
        this._axisMoving = false
      })
    }

    this.test = new Mesh(new PlaneGeometry(2, 2), new MeshBasicMaterial({
      map: this._amrRender.texture,
      opacity: 0.5,
      transparent: true,
      side: DoubleSide
    }))
    this.test.position.set(0, 1, 0)
    this.test.visible = false
    this.scene.add(this.test)
  }

  get mainCamera() {
    return this._mainCamera
  }

  set mainCamera(c: OrthographicCamera | PerspectiveCamera) {
    this._mainCamera = c
    this._composer.passes.forEach(p => {
      if (p instanceof RenderPass) {
        p.camera = c
      }
    })
    if (c instanceof OrthographicCamera) {
      this.scene.background = null
    } else {
      this.scene.background = this.scene.environment
    }
    // (this._composer.passes[0] as RenderPass).camera = c
  }

  addEventListener = this._emitter.on.bind(this._emitter)
  removeEventListener = this._emitter.on.bind(this._emitter)

  showState() {
    this.stats.dom.style.display = 'block'
  }

  hideState() {
    this.stats.dom.style.display = 'none'
  }

  hideAmrUI(all = true) {
    this.amr.slots.forEach(slot => slot.hide())
    this.amr.sizeBox.hide(all)
    this.amr.controllers.forEach(controller => controller.hide())
    this.amr.goods.forEach(g => g.hide())
  }

  showAmrUI(all = true) {
    this.amr.slots.forEach(slot => slot.show())
    this.amr.sizeBox.show(all)
    this.amr.controllers.forEach(controller => controller.show())
    this.amr.goods.forEach(g => g.show())
  }

  private _updateCamera() {
    const distance = this.mainCamera.position.distanceTo(this.controls.target) // 相机到场景中心的距离
    const frustumSize = 2.0 * Math.tan((this.camera.fov * 0.5) * Math.PI / 180.0) * distance
    const aspect = this.size.x / this.size.y
    this.orthographicCamera.left = -frustumSize * aspect / 2
    this.orthographicCamera.right = frustumSize * aspect / 2
    this.orthographicCamera.top = frustumSize / 2
    this.orthographicCamera.bottom = -frustumSize / 2
    this.orthographicCamera.updateProjectionMatrix()
  }

  private _loadEnv() {
    const loader = new RGBELoader(new LoadingManager())
    // loader.type = FloatType
    loader.load('aristea_wreck_1k.hdr', (texture) => {
      texture.mapping = ********************************
      texture.minFilter = LinearFilter
      texture.magFilter = LinearFilter
      texture.colorSpace = SRGBColorSpace
      texture.format = RGBAFormat
      // texture.type = UnsignedByteType
      texture.needsUpdate = true
      if (!this._usePathTracing) {
        this.scene.background = texture
        this.scene.backgroundBlurriness = 1
        this.scene.backgroundRotation = new Euler(0, Math.PI * 0.98/2, 0)
        this.scene.environment = texture
        this.overlayScene.environment = texture
        this.scene.environmentRotation.y = Math.PI * 0.2
        this.scene.environmentIntensity = 1.3
        this.overlayScene.environmentIntensity = 1.3
      }
      // this.scene.background = new Color('#000')
      this.pathTracer.updateEnvironment()
    }, () => {
    }, () => {
    })
  }

  private _drag = (e: DragEvent) => {
    this._dragging = true
    this._pointermove(e)
  }

  private _dragend = (e: DragEvent) => {
    this._dragging = false
  }

  private _pointermove = (e: MouseEvent) => {
    if (this._pointerDownTimeout) {
      clearTimeout(this._pointerDownTimeout)
      return this._pointerDownTimeout = 0
    }

    let { top, left } = this.renderer.domElement.getBoundingClientRect()
    this.offset.set(left, top)
    this.mousePos.set(e.clientX - left, e.clientY - top)

    this.mouseCoord.set(
      ((e.clientX - left) / (this.size.x)) * 2 - 1,
      -((e.clientY - top) / (this.size.y)) * 2 + 1
    )
    this._raycaster.setFromCamera(this.mouseCoord, this.camera)
    // const objs = this._raycaster.intersectObjects(this.amr.children, true)
    // if (objs[0]) {
    //   const obj = objs[0]
    //   const currentDirection = new Vector3(1, 0, 0) // 默认是朝向z轴负方向，你可以根据实际情况修改
    //   const desiredDirection = obj.face.normal.clone().normalize()
    //   let angle = Math.acos(currentDirection.dot(desiredDirection))
    //   // 初始方向的 bug
    //   let dir = 1
    //   if (angle === 0 || angle === Math.PI) {
    //     console.log(angle)
    //     angle = Math.PI
    //     this._test.rotation.set(0, Math.PI / 2, 0)
    //   } else {
    //     angle -= Math.PI / 2
    //     const rotationAxis = new Vector3().crossVectors(currentDirection, desiredDirection).normalize()
    //     this._test.setRotationFromAxisAngle(rotationAxis, angle)
    //   }
    //
    //   this._test.renderOrder = Infinity
    //   this._test.rotateX(-Math.PI)
    //   this._test.position.copy(objs[0].point).add(objs[0].face!.normal.clone().normalize().multiplyScalar(0.009))
    // }
  }

  private _pointerdown = (e: PointerEvent) => {
    const run = () => {
      // const newLogo = this._test.clone(true)
      // newLogo.renderOrder = 10
      // this.scene.add(newLogo)
      let selectedCache: Object3D[] | undefined = [...this._outlinePass.selectedObjects]
      this._outlinePass.selectedObjects = []
      this.amr.slots.forEach(s => s.selected = false)

      const objs = this._raycaster.intersectObjects(this.amr.slots, false)
      const obj = objs[0]
      if (obj) {
        this.amr.slots.forEach(s => {
          s.traverse(r => {
            if (r == obj.object) {
              if (selectedCache?.includes(s)) {
                selectedCache = undefined
                s.deviceSelected = false
              } else {
                this._outlinePass.selectedObjects = [s]
                s.deviceSelected = true
              }
            } else {
              s.deviceSelected = false
            }
          })
        })
      }
      this._pointerDownTimeout = 0
    }
    this._pointerDownTimeout = window.setTimeout(run, 200)
  }

  private _detectTrackpad = () => {
    const isTrackPadDefined = isTrackPad || typeof isTrackPad !== 'undefined'

    // let zoom = this._test.scale.x
    // if (e.deltaY>0) {
    //   zoom += 0.01
    // } else {
    //   zoom -= 0.01
    // }
    // this._test.scale.set(zoom, zoom, zoom)

    if (isTrackPadDefined) return

    if (eventCount === 0) {
      eventCountStart = performance.now()
    }

    eventCount++

    if (performance.now() - eventCountStart > 66) {
      if (eventCount > 5) {
        isTrackPad = true
      } else {
        isTrackPad = false
      }
      if (isTrackPad) {
        this.controls.zoomSpeed = 0.2
      } else {
        this.controls.zoomSpeed = 1

      }
      setTimeout(resetDetection, 2000)
    }
  }

  private _initEvents() {
    window.addEventListener('drag', this._drag)
    window.addEventListener('dragend', this._dragend)

    document.addEventListener('wheel', this._detectTrackpad)

    window.addEventListener('pointermove', this._pointermove)
    this.renderer.domElement.addEventListener('pointerdown', this._pointerdown)
  }

  switchCamera() {
    this.mainCamera = this.mainCamera === this.camera ? this.orthographicCamera : this.camera
    // this.mainCamera = this.orthographicCamera
  }

  toCameraTop() {
    const distance = this.camera.position.distanceTo(this.controls.target)
    this.camera.position.copy(new Vector3(0, distance + this.controls.target.y, 0))
  }

  toCameraRight() {
    const distance = this.camera.position.distanceTo(this.controls.target)
    this.camera.position.copy(new Vector3(0, this.controls.target.y, distance))
  }

  toCameraFace() {
    const distance = this.camera.position.distanceTo(this.controls.target)
    this.camera.position.copy(new Vector3(distance, this.controls.target.y, 0))
  }

  async generateBVH() {
    // this._ground.visible = true
    // this.scene.background = new Color('#000')
    this.pathTracer.pausePathTracing = false

    this.amr.slots.forEach(s => s.hide())
    if (this.amr) {
      this.amr.opacityCache = this.amr.bodyOpacity
      this.amr.bodyOpacity = 1

      this.overlayScene.clear()
      this.amr.logos.forEach(l => {
        const g = new Group()
        l.target.updateMatrixWorld(true)
        g.applyMatrix4(l.target.matrixWorld)
        g.add(l.target)
        this.overlayScene.add(g)
      })
    }

    this.usePathTracing = true
    this.pathTracer.setScene(this.scene, this.mainCamera)
  }

  async download(useRenderTarget = false): Promise<string> {
    if (useRenderTarget && !this.usePathTracing) {
      // 从 renderTarget 读取像素数据
      const width = this._amrRender.width
      const height = this._amrRender.height
      const pixels = new Uint8Array(width * height * 4)

      // 确保 renderTarget 是当前的渲染目标
      this.renderer.setRenderTarget(this._amrRender)
      this.renderer.readRenderTargetPixels(this._amrRender, 0, 0, width, height, pixels)
      this.renderer.setRenderTarget(null)

      // 创建 canvas 并将像素数据绘制到上面
      const canvas = document.createElement('canvas')
      canvas.width = width
      canvas.height = height
      const ctx = canvas.getContext('2d')!

      // 创建 ImageData
      const imageData = new ImageData(width, height)

      // 复制像素数据，注意 WebGL 的 Y 轴是反的
      for (let y = 0; y < height; y++) {
        for (let x = 0; x < width; x++) {
          const srcIndex = ((height - y - 1) * width + x) * 4
          const dstIndex = (y * width + x) * 4

          imageData.data[dstIndex] = pixels[srcIndex]     // R
          imageData.data[dstIndex + 1] = pixels[srcIndex + 1] // G
          imageData.data[dstIndex + 2] = pixels[srcIndex + 2] // B
          imageData.data[dstIndex + 3] = pixels[srcIndex + 3] // A
        }
      }

      ctx.putImageData(imageData, 0, 0)
      return canvas.toDataURL('image/png')
    } else {
      return new Promise((resolve, reject) => {
        this._uiHiddenTime = this._clock.getElapsedTime()
        this.hideAmrUI()
        const cacheBg = this.scene.background
        this.scene.background = null
        setTimeout(() => {
          const url = this.renderer.domElement.toDataURL('image/png')
          this.scene.background = cacheBg
          return resolve(url)
        }, this.fpsAverage)
      })
    }
  }

  clearAMR() {
    clearThreeObjectMesh(this.overlayScene)
    this.amr.clean()

    this.usePathTracing = false
  }

  async exportAMR() {
    const amr = this._container.clone(true)
    amr.traverse((child: Object3D | Mesh) => {
      // @ts-ignore
      if (child.dispose) {
        // @ts-ignore
        child.dispose()
        child.removeFromParent()
      }
    })
    return await gltfExporter.parseAsync(amr, {
      onlyVisible: true,
      binary: true
    })
    // if (glb) {
    //   downloadBlob(new Blob([glb as BlobPart]), 'ttt.glb')
    // }
  }

  async addAMR(url: string, type = '') {
    this.amr.init(this)
    await this.amr.load(url, type)

    this._progressShadow.clear()
    this._progressShadow.recalculate()

    this.amr.addEventListener('shape-key-changed', (key, value) => {
      this.pathTracer.reset()
      this.sampleCount = 0
      this.pathTracer.setScene(this.scene, this.camera)

      this.overlayScene.traverse(node => {
        // @ts-ignore
        if (!Object.keys(node.morphTargetDictionary || {}).includes(key)) return

        let val = value
        if (node.userData[`origin_${key}`]) {
          val -= node.userData[`origin_${key}`]
        }
        // @ts-ignore
        const index = node.morphTargetDictionary[key]
        // @ts-ignore
        node.morphTargetInfluences && (node.morphTargetInfluences[index] = val)

        if (node instanceof Mesh) {
          node.geometry.translate(0, 0, 0.00001)
        }
      })
      this._progressShadow.clear()
      this._progressShadow.recalculate()
    })
  }

  fit = () => {
    const box = new Box3()
    box.setFromObject(this._container)
    const size = this.amr.sizeBox.size.getSize(new Vector3())
    const center = size.clone().multiplyScalar(0.5)
    // box.getCenter(center)
    let maxLen = Math.max(size.x, size.y, size.z) + Math.max(size.x, size.y)
    const len = calculateFocalLength(this.camera.fov, 0.8) * 3 - 1.2
    maxLen += len
    const minLen = Math.min(size.x, size.y, size.z) + 0.2
    const lockAt = this.camera.position.clone().normalize()
    const cameraTarget = lockAt.multiplyScalar(maxLen)
    cameraTarget.setY(maxLen * Math.sin(30 / 180 * Math.PI))

    this._fitAnimation = new Tween(this.camera.position)
      .to(cameraTarget, 600)
      .easing(TWEEN.Easing.Quintic.InOut)
      .onStart(() => {
        this.controls.enabled = false
      })
      .onComplete(() => {
        this.controls.enabled = true
      })
      .start()

    this.controls.minDistance = minLen
    this.controls.maxDistance = maxLen * 2.5

    if (center.equals(new Vector3())) {
      return
    }
    center.setZ(0)
    this.controls.target.setY(center.y)
  }

  select(obj: string | Object3D | Object3D[]) {
    if (typeof obj === 'string') {
    } else if (Array.isArray(obj)) {
      this._outlinePass.selectedObjects = [...obj]
    } else {
      this._outlinePass.selectedObjects = [obj]
    }
  }

  appendTo(parent: HTMLElement) {
    parent.append(this.renderer.domElement)
    parent.append(this.stats.dom)
    parent.append(this.css2dRenderer.domElement)
    this._controlsGizmo && (this._controlsGizmo.appendTo(parent))
    // parent.append(this.css3dRenderer.domElement)
    this.stats.dom.style.bottom = '0'
    this.stats.dom.style.top = 'auto'

    this.resize()

    this._resizeObserver = new ResizeObserver(this.resize.bind(this))
    this._resizeObserver.observe(parent)
  }

  resize = () => {
    const parent = this.renderer.domElement.parentElement
    if (!parent) return
    parent.style.display = 'flex'
    this.renderer.domElement.style.width = '100%'
    this.renderer.domElement.style.height = '100%'
    this.renderer.domElement.style.flexGrow = '1'
    const width = parent.clientWidth
    const height = parent.clientHeight
    this.size.set(width, height)
    this.camera.aspect = width / height
    this.camera.updateProjectionMatrix()

    this._amrRender.setSize(width, height)

    this._updateCamera()

    this.css2dRenderer.setSize(width, height)
    // this.css3dRenderer.setSize(width, height)

    const pixelRatio = this.renderer.getPixelRatio()
    this.pathTracer?.updateCamera()
    this.sampleCount = 0

    this.renderer.setSize(width, height)
    this._composer.setSize(width, height)
    this._composer.passes.forEach(p => p.setSize(width, height))

    const uniforms = this._fxaaPass.material.uniforms
    uniforms['resolution'].value.x = 1 / (width * pixelRatio)
    uniforms['resolution'].value.y = 1 / (height * pixelRatio)

    // 更新辉光通道尺寸
    if (this._bloomPass) {
      this._bloomPass.setSize(width, height)
    }
  }

  add(node: Object3D) {
    this._container.add(node)
  }

  updatePosition() {
  }

  ticker = () => {
    this.stats.begin()

    this.renderer.domElement.dispatchEvent(new CustomEvent('ticker'))

    requestAnimationFrame(this.ticker)

    this.renderer.autoClear = false
    this.renderer.clear()
    if (this.usePathTracing) {
      this.hideAmrUI()
      if (this.sampleCount < this.maxSample) {
        this.sampleCount++
      } else {
        this.pathTracer.pausePathTracing = true
      }

      this.pathTracer.renderSample()

      const originAutoClear = this.renderer.autoClear
      this.renderer.autoClear = false
      this.scene.overrideMaterial = depthMat
      this.renderer.clearDepth()
      this.renderer.render(this.scene, this.mainCamera)
      this.scene.overrideMaterial = null

      // render real time floating objects
      this.renderer.render(this.overlayScene, this.mainCamera)

      this.renderer.autoClear = originAutoClear

    } else {
      const cost = this._clock.getElapsedTime() - this._uiHiddenTime
      if (cost > 0.6) {
        this.showAmrUI(true)
      } else {
        this.hideAmrUI(false)
      }

      const fps = this.stats.fps()
      if (!this.usePathTracing) {
        if (this._fpsCache.length > this._fpsCacheMax) {
          this._fpsCache.shift()
        }
        this._fpsCache.push(fps)
      }

      this.amr.slots.forEach((s) => {
        if (this._dragging) {
          if (s.getCanInstall()) {
            s.render(this)
          }
        } else {
          s.render(this)
        }
      })

      TWEEN.update()

      this.controls.update()
      this._progressShadow.update(this.mainCamera)

      this.css2dRenderer.render(this.scene, this.mainCamera)
      this.updatePosition()

      const bg = this.scene.background
      this.scene.overrideMaterial = null
      this.scene.background = null
      this.renderer.setRenderTarget(this._amrRender)
      this.renderer.setClearColor(0x000000, 0);
      this.renderer.clear(true, true, true);
      this.renderer.render(this.scene, this.mainCamera)
      // this._composer.render()
      this.renderer.setRenderTarget(null)
      this.renderer.setClearColor(0x000000, 0);
      this.scene.background = bg
      this.renderer.render(this.scene, this.mainCamera)
      // this.renderer.render(this.overlayScene, this.camera)
    }

    this.stats.end()
  }

  removeFromParent() {
    const parent = this.renderer.domElement.parentElement
    if (parent) {
      this._resizeObserver?.unobserve(parent)
      parent.removeChild(this.renderer.domElement)
    }
  }

  clean() {
    this.amr.clean()
    this._progressShadow.clear()
  }

  destroy() {
    this._clock.stop()
    this._controlsGizmo?.destroy()
    this.amr.destroy()
    this.amr.removeFromParent()

    this._progressShadow.destroy()

    TWEEN.removeAll()

    this.stats.dom.remove()
    this._emitter.removeAllListeners()
    this._fitAnimation?.stop()

    window.removeEventListener('drag', this._drag)
    document.removeEventListener('wheel', this._detectTrackpad)
    window.removeEventListener('pointermove', this._pointermove)
    this.renderer.domElement.removeEventListener('pointerdown', this._pointerdown)
    const parent = this.renderer.domElement.parentElement
    if (parent) {
      this._resizeObserver?.unobserve(parent)
    }
    this.controls.dispose()
    this.renderer.domElement.remove()
    this.renderer.dispose()
    this._composer.dispose()

    clearThreeObjectMesh(this.scene)
    this.scene.clear()
  }
}
