import { Group, Object3D, Vector2, Vector3 } from 'three'
import { CSS2DObject }                       from 'three/examples/jsm/renderers/CSS2DRenderer'
import EventEmitter                          from 'eventemitter3'
import { clearThreeObjectMesh }              from './utils'
import { SGLBLoader }                        from '../sglb'
import { SlotItem }                          from './slot/slot-item'
import { Scene3D }                           from './scene'

export interface SlotProps {
  name: string,
  follows: Map<string, {
    scale: number,
    origin: number
  }>
  slot: Object3D
  show: boolean,
  isMirror: boolean
  mirrorTarget?: string
}

export interface DeviceInfo {
  name: string
  id: string
  url: string
  logo?: string
  bracketUrl?: string
}

export const SLOT_EVENTS = {
  ON_DROP: 'slot-on-drop',
  ON_DEVICE_DELETED: 'slot-on-device-deleted',
  ON_DEVICE_REPLACED: 'slot-on-device-replaced',
  ON_BEFORE_SELECTED: 'slot-on-before-selected',
  ON_SELECTED: 'slot-on-selected',
  ON_DEVICE_SELECTED: 'slot-on-device-selected',
  ON_DROP_ERROR: 'slot-on-drop-error',
  ON_ERROR_STATE_CHANGE: 'slot-on-error-state-change',
  ON_FUNCTION_REMOVED: 'slot-on-function-removed',
  ON_FUNCTION_ADDED: 'slot-on-function-added'
}

export class Slot extends CSS2DObject {
  private _emitter = new EventEmitter()
  private _slotContainer = new Group()

  private _slotUI: SlotItem

  private _bracketSlots: Object3D[] = []

  private _hasError = false
  useBracket = false

  addEventListener = this._emitter.addListener.bind(this._emitter)
  removeEventListener = this._emitter.removeListener.bind(this._emitter)

  private _canInstall = false
  private _loading = false
  private _selected = false
  private _deviceSelected = false

  private _allFunctions: Set<string> = new Set()
  private _functions: Set<string> = new Set()

  private _timer = 0

  private _parentPos = new Vector3()

  private _wrapper = new Group()

  get hasDevice() {
    return this._slotContainer.children.length > 0
  }

  deviceInfo: DeviceInfo | undefined

  holeTarget: Object3D | undefined

  mirrorTarget: Slot | undefined

  private _showSlot = true

  private _isMirror = false

  get isMirror() {
    return this._isMirror
  }

  constructor(public props: SlotProps) {
    const box = document.createElement('div')
    super(box)

    this._showSlot = typeof props.show === 'boolean' ? props.show : true

    this.name = props.name
    this._isMirror = props.isMirror
    this._slotUI = new SlotItem({
      name: props.name,
      onRemove: () => {
        this.clearSlot()
        this._emitter.emit(SLOT_EVENTS.ON_DEVICE_DELETED)
      }
    })
    this._slotUI.slot = this
    this._slotUI.hide = !this._showSlot || this.isMirror

    box.append(this._slotUI)
    this._parentPos.copy(this.props.slot.position)

    this._slotUI.addEventListener('function-removed', ({ detail }: any) => {
      this.removeFunction(detail)
    })
    this._slotUI.addEventListener('function-added', ({ detail }: any) => {
      this.addFunction(detail)
    })

    this.add(this._slotContainer)

    this.props.slot.add(this)
    setTimeout(() => {
      this.props.slot.parent?.add(this._wrapper)
      this._wrapper.add(this.props.slot)
    }, 10);

    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
      box.addEventListener(eventName, this._preventDefaults, false)
    })

    box.addEventListener('click', (e) => {
      e.preventDefault()
      e.stopPropagation()
      this.selected = !this.selected
    })
    box.addEventListener('dragover', e => {
      e.preventDefault()
      // @ts-ignore
      e.dataTransfer.dropEffect = 'copy'
      if (!this._canInstall) {
        if (e.dataTransfer) {
          e.dataTransfer.dropEffect = 'none'
        }
        return
      }
    })
    box.addEventListener('drop', async (e: DragEvent) => {
      e.preventDefault()
      e.stopPropagation()
      if (!this._canInstall) return
      const jsonDataString = e.dataTransfer?.getData('application/json') // 获取传输的数据
      if (jsonDataString) {
        const r = JSON.parse(jsonDataString) // 将字符串转换回JSON对象
        try {
          await this.install(r)
          this._emitter.emit(SLOT_EVENTS.ON_DROP, this)
        } catch (e) {
          this._emitter.emit(SLOT_EVENTS.ON_DROP_ERROR, e)
        }
      } else {
        this._emitter.emit(SLOT_EVENTS.ON_DROP_ERROR, new Error('No device info'))
      }
    })
  }

  private _preventDefaults = (e: any) => {
    e.preventDefault()
    e.stopPropagation()
  }

  shapeKeyChanged(key: string, value: number) {
    if (this.props.follows.has(key)) {
      const obj = this.props.follows.get(key)
      if (obj) {
        let direction: string = 'x'
        if (['height', 'mast'].includes(key)) {
          direction = 'y'
        } else if (['width', 'liftOuterWidth'].includes(key)) {
          direction = 'z'
        }
        // @ts-ignore
        const dr = this.props.slot?.position[direction] > 0 ? 1 : -1
        // @ts-ignore
        this._wrapper.position[direction] = (value - obj.origin) * obj.scale * dr
      }
    }
  }

  async loadGlb(sglb: string) {
    const sglbLoader = new SGLBLoader({ useCache: true })
    const result = await sglbLoader.loadAsync(sglb)
    if (!result.glb) return
    return result.glb.clone(true)
  }

  async installBracket(url: string) {
    const sglbLoader = new SGLBLoader({ useCache: true })
    const result = await sglbLoader.loadAsync(url)
    if (!result.glb) return
    const glb = result.glb.clone(true)
    glb.traverse(m => {
      m.userData.isBracket = true
    })
    this.useBracket = true
    this._slotContainer.add(glb)
  }

  async install(info: DeviceInfo) {
    if (this._loading) return
    this._loading = true
    try {
      const glb = await this.loadGlb(info.url)
      if (!glb) {
        throw new Error(`Fail to load device from: ${info.url}`)
      }
      glb.traverse(m => {
        m.userData.isDevice = true
      })
      if (this.isMirror) {
        glb.scale.set(1, 1, -1)
      }
      // 如需要支架，先安装支架
      if (info.bracketUrl) {
        const bracket = await this.loadGlb(info.bracketUrl)
        if (!bracket) {
          throw new Error(`Fail to load bracket from: ${info.bracketUrl}`)
        } else {
          this.clearSlot()
          this._emitter.emit(SLOT_EVENTS.ON_DEVICE_REPLACED)
          bracket.traverse(m => {
            if (m.name.startsWith('Slot')) {
              this._bracketSlots.push(m)
            }
            m.userData.isBracket = true
          })
          this._slotContainer.add(bracket)
        }
        this._bracketSlots.forEach(b => {
          b.add(glb)
        })
      } else {
        this.clearSlot()
        this._emitter.emit(SLOT_EVENTS.ON_DEVICE_REPLACED)
        this._slotContainer.add(glb)
      }

      if (this.mirrorTarget) {
        this.mirrorTarget.install(info)
      }
      this.deviceInfo = info
      this._slotUI.deviceName = info.name
      this._slotUI.filled = true
      if (this.holeTarget) {
        this.holeTarget.visible = false
      }
    } catch (e) {
      throw e
    } finally {
      this._loading = false
    }
  }

  getWP = this._slotContainer.getWorldPosition.bind(this._slotContainer)

  get device() {
    if (this._slotContainer.children.length) {
      return this._slotContainer.children[0]
    } else {
      return null
    }
  }

  get allFunctions(): string[] {
    return [...this._allFunctions]
  }

  set allFunctions(fs: string[]) {
    this._functions.forEach(f => {
      if (!fs.includes(f)) {
        this._functions.delete(f)
      }
    })
    this._allFunctions = new Set(fs)
    this._slotUI.allFunctions = fs
  }

  get functions(): string[] {
    return [...this._functions]
  }

  addFunction(f: string) {
    if (!this._allFunctions.has(f)) return
    this._functions.add(f)
    this._slotUI.functions = [...this.functions]
    this._emitter.emit(SLOT_EVENTS.ON_FUNCTION_ADDED, f)
  }

  removeFunction(f: string) {
    if (this._functions.has(f)) {
      this._functions.delete(f)
      this._slotUI.functions = [...this.functions]
      this._emitter.emit(SLOT_EVENTS.ON_FUNCTION_REMOVED, f)
    }
  }

  clearFunctions() {
    this._functions.forEach(f => this.removeFunction(f))
  }

  addFunctions(fs: string[]) {
    fs.forEach(f => this.addFunction(f))

    this._slotUI.functions = this.functions
  }

  changeName(name: string) {
    this.name = name
    this.props.slot.userData.name = name
    this.props.name = name
    this.element?.setAttribute('data-name', this.props.name)
  }

  clearSlot() {
    this._bracketSlots = []
    this._slotContainer.children.forEach(s => {
      s.removeFromParent()
      clearThreeObjectMesh(s)
    })
    this._slotUI.filled = false
    this.deviceInfo = undefined
    this._slotUI.deviceName = ''

    if (this.holeTarget) {
      this.holeTarget.visible = true
    }
    if (this.mirrorTarget) {
      this.mirrorTarget.clearSlot()
    }
  }

  enable() {
    this._slotUI.disable = false
  }

  disable() {
    this._slotUI.disable = true
  }

  hide() {
    this._slotUI.hide = true
  }

  show() {
    if (this.isMirror) return
    this._slotUI.hide = false
  }

  get selected() {
    return this._selected
  }

  set selected(s: boolean) {
    if (s) {
      this._emitter.emit(SLOT_EVENTS.ON_BEFORE_SELECTED, s)
    }
    this._selected = s
    this._slotUI.selected = s
    this._emitter.emit(SLOT_EVENTS.ON_SELECTED, s)

    this.element.style.zIndex = '100'
    if (this.selected) {
      this.renderOrder = Infinity
    } else {
      this.renderOrder = 1
    }
  }

  get deviceSelected() {
    return this._deviceSelected
  }

  set deviceSelected(s: boolean) {
    this._deviceSelected = s
    this._emitter.emit(SLOT_EVENTS.ON_DEVICE_SELECTED, s)
  }

  get error() {
    return this._hasError
  }

  set error(hasError) {
    if (this._loading) {
      setTimeout(() => {
        this.error = hasError
      }, 100)
    }
    this._hasError = hasError
    this._slotUI.hasError = hasError
    this._emitter.emit(SLOT_EVENTS.ON_ERROR_STATE_CHANGE, hasError)
  }

  getCanInstall() {
    return this._canInstall
  }

  canInstall(c: boolean) {
    this._canInstall = c
    this._slotUI.canInstall = c
  }

  dispose() {
    cancelAnimationFrame(this._timer);

    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
      this.element.addEventListener(eventName, this._preventDefaults, false)
    })

    this.clearSlot()
    this._emitter.removeAllListeners()
    clearThreeObjectMesh(this._slotContainer)
    this.element.remove()
  }

  render(app: Scene3D) {
    const transform = this.element.style.transform
    const match = /translate\(-?\d*\.?\d*%?,?\s-?\d*\.?\d*%?\)\s*translate\((-?\d*\.?\d*)px,\s*(-?\d*\.?\d*)px\)/.exec(transform)
    if (!match) return
    const p = new Vector2(parseFloat(match[1]), parseFloat(match[2]))
    const distance = app.mousePos.distanceTo(p)
    let initialSize = 18
    let max = 38
    let offset = 30
    if (this._canInstall) {
      initialSize = 34
      max = 60
      offset = 60
    }
    if (distance < offset) {
      let size = (offset - distance) * 1 + initialSize
      if (distance < 20) {
        size = max
      }
      if (size > max) {
        size = max
      }
      this._slotUI.setSize(size)
    } else {
      this._slotUI.setSize(initialSize)
    }
  }

  clone(recursive?: boolean): this {
    return new Group() as any
  }
}
