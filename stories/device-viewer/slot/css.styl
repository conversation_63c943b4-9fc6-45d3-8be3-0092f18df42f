* {
  box-sizing border-box
}

.slot-item-container {
  .main {
    pointer-events: auto;
    cursor: pointer;
    font-size: 12px;
    color: #fff;
    width: 22px;
    height: 22px;
    background: rgba(255, 255, 255, 0);
    border: 2px solid #fff;
    border-radius: 0;
    transition: background .2s, width 0.14s, height 0.14s, border 0.14s;

    &.disable {
      cursor: not-allowed;
    }

    &.drag-over {
      border-color: red;
    }

    &.can-install {
      position: relative;
      width: 44px;
      height: 44px;
      background: rgba(0, 102, 255, 0.1);
      border-color: #0066FF;
    }

    &.filled {
      border-color: #00FF95;
      border-radius 50%
    }

    &.selected {
      border-width 4px;

      & + .info {
        pointer-events auto
        overflow visible
        width auto
      }
    }

    &.has-error {
      border-color: #ff0000;
    }

    &.hidden {
      display: none;

      & + .info {
        display none;
      }
    }

    //
    //&:hover:after {
    //  pointer-events: auto;
    //  transition: all .2s ease-in-out;
    //  opacity: 1;
    //  transform: translate3d(-50%, -4px, 0);
    //}

    &:after {
      pointer-events: none;
      content: attr(data-name);
      transition: all .2s 0.3s;
      opacity: 0;
      position: absolute;
      left: 50%;
      bottom: 100%;
      transform: translate3d(-50%, 5px, 0);
      white-space: nowrap;
      font-size: 12px;
      color: #000;
      padding: 2px 8px;
      border-radius: 2px;
      background: #fff;
    }

    &:hover {
      background: rgba(255, 255, 255, 0.5);

      & + .info {
        pointer-events auto
        overflow visible
        width auto
      }
    }
  }

  .name {
    position relative
  }

  .name-wrapper {
    margin-top -10px
    white-space: nowrap;
    font-size: 12px;
    color: #444E69;
    padding: 2px 8px;
    border-radius: 2px;
    background: #fff;
  }

  .functions-wrapper {
    z-index 20000
    position absolute
    left 0
    top 100%


    > .functions {
      display inline-block
      min-width 100px
      padding 6px 0
      border-left 1px solid #00FF95

      border-radius: 0px 2px 2px 0px;
      background: rgba(0, 0, 0, 0.2);
      backdrop-filter: blur(10px);

      .btn-add {
        cursor pointer
        padding 2px 0
        margin 0 8px
        display flex
        justify-content center
        align-content center
        align-items center
        border: 1px dashed #eee
        font-size 12px
        color: #eee;
        border-radius 2px
        backdrop-filter none

        background: rgba(0, 5, 10, 0.5)

        &.btn-not-allowed {
          cursor not-allowed
        }
      }

      .function {
        display flex
        justify-content space-between
        white-space nowrap
        margin-bottom 6px
        padding 0 8px
        font-size 12px
        color #fff

        span {
          margin-right 4px
        }

        .btn-remove {
          pointer-events none
          opacity 0
          cursor pointer
          align-self center
          margin-right 0
          height 14px
          width @height
          background url("../asserts/btn-close.png") center no-repeat
          background-size 10px 10px
        }

        &:hover {
          .btn-remove {
            pointer-events auto
            opacity 1
            transform scale(1.2)
          }
        }
      }
    }
  }

  .device-name {
    position relative
    margin 2px 0
    display flex
    min-width 120px
    align-items center
    align-content center
    justify-content flex-start
    white-space nowrap
    height 20px
    padding 0 8px
    padding-right 20px
    font-size 14px
    font-weight bold
    color #444E69

    span {
      display block
      white-space nowrap
      text-overflow ellipsis
      overflow hidden
      max-width 250px
    }

    &:hover {
      border-radius: 0px 2px 2px 0px;
      background: rgba(0, 0, 0, 0.2);
      backdrop-filter: blur(10px);

      .btn-close {
        opacity 1
        pointer-events auto
        filter brightness(1.5)
      }
    }

    .btn-close {
      opacity 0
      pointer-events none
      cursor pointer
      position absolute
      right 2px
      top 2px
      height 16px
      width @height
      background url("../asserts/btn-close2.png") center no-repeat
      background-size 10px 10px
    }

    &:before {
      content ''
      position absolute
      left 0
      height 10px
      width 3px
      background #00FF95
    }

    &.empty {
      height 6px

      &:before {
        height 0
      }
    }
  }

  .all-functions {
    opacity 0
    pointer-events none
    min-width 80px
    position absolute
    left -10px
    margin-left 100%
    bottom 0
    padding 6px 0
    background #fff
    border-radius 2px
    transition all .3s

    &.show {
      opacity 1
      pointer-events auto
      left 16px
    }

    label {
      margin-left 8px
      margin-bottom 20px
      color #444E69
      font-size 14px
    }

    .btn-close {
      cursor pointer
      position absolute
      right 7px
      top @right
      height 16px
      width @height
      background url("../asserts/btn-close2.png") center no-repeat
      background-size 10px 10px
    }

    .functions {
      display inline-block
      max-height 200px
      overflow-x hidden
      overflow-y auto

      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background-color: #fff;
      }

      &::-webkit-scrollbar-thumb {
        background: #787878;
        border-radius: 25px;
      }
    }

    .function {
      display flex
      justify-content space-between
      white-space nowrap
      padding 5px 10px
      font-size 12px
      color #787878

      span {
        display flex
        align-content center
        align-items center
        margin-right 4px
      }

      &:hover {
        background rgba(68, 78, 105, 0.2)
      }
    }

    .btn-add {
      cursor pointer
      width 16px
      font-size 16px
      text-align center
      font-style normal
    }
  }

  .info {
    overflow hidden
    margin-left -2px
    margin-top -19px
    position absolute;
    left 100%
    top 50%
    transform translateY(-50%)
    width: 0;
    display flex
    align-items flex-start
    transition all 0.3s

    img {
      flex 0 0 auto
      width 53px
      height 29px
    }
  }
}

