import { css, html, LitElement }          from 'lit'
import { customElement, property, state } from 'lit/decorators.js'
import styles                             from './css.styl?inline'
import SlotPath                           from '../asserts/slot-path.png'

export interface SlotItemProps {
  name: string
  onRemove?: () => void
}

@customElement('slot-item')
export class SlotItem extends LitElement {

  @state()
  private _size = 12

  @state()
  private _showDialog = false

  // @ts-ignore
  static styles = css([styles])

  constructor(public props: SlotItemProps) {
    super()
  }

  @property({ type: Boolean })
  selected = false

  @property({ type: Boolean })
  hasError = false

  @property({ type: Boolean })
  canInstall = false

  @property({ type: Boolean })
  hide = false

  @property({ type: Boolean })
  disable = false

  @property({ type: Boolean })
  filled = false

  @property({ type: Object })
  allFunctions: string[] = []

  @property({ type: Object })
  functions: string[] = []

  @property({ type: String })
  deviceName: string = ''

  @property()
    //@ts-ignore
  slot

  get hasMoreFunctions() {
    return this.allFunctions.length > this.functions.length
  }

  removeDevice = () => {
    if (this.props.onRemove) {
      this.props.onRemove()
    }
  }

  onClick(e: MouseEvent) {
    e.preventDefault()
    e.stopPropagation()
  }

  setSize(size = 12) {
    this._size = size
  }

  addFunction(f: string) {
    this.dispatchEvent(new CustomEvent('function-added', { detail: f }))
  }

  protected render(): unknown {
    return html`
      <div class="slot-item-container">
        <div class="main 
          ${this.selected ? 'selected' : ''} 
          ${this.hasError ? 'has-error' : ''}
          ${this.canInstall ? 'can-install' : ''}
          ${this.hide ? 'hidden' : ''}
          ${this.disable ? 'disable' : ''}
          ${this.filled ? 'filled' : ''}
        "
             style="${`width: ${this._size + 'px'}; height:${this._size + 'px'}`}"
        ></div>
        <div class="info" @click="${this.onClick}">
          <img alt="" src="${SlotPath}">
          <div class="name">
            <div class="name-wrapper">${this.slot?.name}</div>
            <div class="functions-wrapper">
              <div class="device-name ${!this.deviceName && 'empty'}">
                <span>${this.deviceName}</span>
                <i class="btn btn-close" @click=${this.removeDevice}></i>
              </div>
              ${
                this.allFunctions.length
                  ? html`
                    <div class="functions">
                      ${
                        this.functions.map(f => {
                          return html`
                            <div class="function"><span>${f}</span><i class="btn btn-remove"
                                                                      @click=${() => {
                                                                        this.dispatchEvent(new CustomEvent('function-removed', { detail: f }))
                                                                      }}></i>
                            </div>
                          `
                        })
                      }
                      ${
                        html`
                          <div class="btn btn-add ${!this.hasMoreFunctions && 'btn-not-allowed'}" @click=${() => {
                            this._showDialog = !this._showDialog
                          }}>＋添加
                          </div>`
                      }
                    </div>
                  `
                  : html``

              }

              ${
                this.hasMoreFunctions && this.allFunctions.length
                  ? html`
                    <div class="all-functions ${this._showDialog && 'show'}">
                      <label>添加功能
                        <i class="btn btn-close" @click=${() => {
                          this._showDialog = false
                        }}></i>
                      </label>
                      <div class="functions">
                        ${
                          this.allFunctions.map(f => {
                            if (this.functions.includes(f)) {
                              return html``
                            }
                            return html`
                              <div class="function">
                                <span>${f}</span>
                                <i class="btn btn-add" @click=${() => this.addFunction(f)}>＋</i>
                              </div>
                            `
                          })
                        }
                      </div>
                    </div>`
                  : html``
              }
            </div>
          </div>
        </div>
      </div>
    `
  }
}
