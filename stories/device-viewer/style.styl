.slot-item {
  pointer-events: auto;
  cursor: pointer;
  font-size: 12px;
  color: #fff;
  width: 12px;
  height: 12px;
  background: rgba(255, 255, 255, 0);
  border: 1px solid #fff;
  border-radius: 50%;
  transition: background .2s, width 0.14s, height 0.14s, border 0.14s;

  &.disable {
    cursor: not-allowed;
  }

  &.hidden {
    display: none;
  }

  &.drag-over {
    border-color: red;
  }

  &.can-install {
    position: relative;
    width: 34px;
    height: 34px;
    background: rgba(0, 102, 255, 0.1);
    border-color: #0066FF;
    // &:before {
    //   content: "";
    //   position: absolute;
    //   left: 50%;
    //   top: 50%;
    //   width:3px;
    //   height:3px;
    //   transform: translate3d(-50%,-50%,0);
    //   border-radius: 50%;
    //   background: #00FF95;
    // }
  }

  &.filled {
    border-color: #00FF95;
  }

  &.selected {
    border-width 4px;
  }

  &.has-error {
    border-color: #ff0000;
  }

  &:hover {
    background: rgba(255, 255, 255, 0.5);
  }

  &:hover:after {
    pointer-events: auto;
    transition: all .2s ease-in-out;
    opacity: 1;
    transform: translate3d(-50%, -4px, 0);
  }

  &:after {
    pointer-events: none;
    content: attr(data-name);
    transition: all .2s 0.3s;
    opacity: 0;
    position: absolute;
    left: 50%;
    bottom: 100%;
    transform: translate3d(-50%, 5px, 0);
    white-space: nowrap;
    font-size: 12px;
    color: #000;
    padding: 2px 8px;
    border-radius: 2px;
    background: #fff;
  }
}
