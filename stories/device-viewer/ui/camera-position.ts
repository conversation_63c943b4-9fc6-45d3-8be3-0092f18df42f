import { CSS2DObject }   from 'three/examples/jsm/renderers/CSS2DRenderer'
import IconCamera        from '../asserts/btn-camera2.png'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'
import { Vector3 }       from 'three'

export class CameraPosition extends CSS2DObject {
  constructor(private _controls: OrbitControls) {
    const box = document.createElement('div')
    super(box)

    // const icon = document.createElement('div')
    // box.append(icon)
    //
    // icon.style.pointerEvents = `auto`
    // icon.style.background = `url(${IconCamera}) center no-repeat`
    // icon.style.backgroundSize = `100% auto`
    // icon.style.width = icon.style.height = `48px`
    // icon.style.position = 'absolute'
    // icon.style.top = '0'
    // icon.style.transform = 'translateY(-50%) scaleX(-1)'
    // icon.style.marginBottom = '5px'
    //
    // this._controls.addEventListener('change', () => {
    //   const dir = this._controls.target.clone().sub(this._controls.object.position)
    //   let ref = new Vector3(0, 0, 1)
    //   if (Math.abs(dir.dot(ref)) > 0.99) {
    //     ref = new Vector3(0, 1, 0)
    //   }
    //   const distance = dir.length()
    //   const perpendicular = new Vector3().crossVectors(dir, ref).normalize()
    //   const target = this._controls.target.clone().add(perpendicular.multiplyScalar(distance/2))
    //
    //   this.position.copy(target)
    // })
  }
}
