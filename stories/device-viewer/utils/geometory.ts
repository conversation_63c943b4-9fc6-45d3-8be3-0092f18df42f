import { <PERSON><PERSON>er<PERSON><PERSON><PERSON>, Mesh, Object3D, Scene } from 'three'
import { mergeGeometries }                       from 'three/examples/jsm/utils/BufferGeometryUtils'

export function isMesh(m: Object3D) {
  if (m.type) {
    return m.type === 'Mesh'
  } else {
    // @ts-ignore
    return m.isMesh
  }
}

export const mergeObject = (model: Object3D | Scene) => {
  const timeStart = window.performance.now()
  const geometries: BufferGeometry[] = []
  model.updateWorldMatrix(true, true)
  model.traverse(c => {
    if (isMesh(c) || (c as Mesh).geometry) {
      const clone = (c as Mesh).geometry.clone()
      clone.applyMatrix4(c.matrixWorld)

      // 确保 morphTargetsRelative 属性一致
      clone.morphTargetsRelative = false

      // 删除所有的 morph 属性
      if (clone.morphAttributes) {
        for (const key in clone.morphAttributes) {
          delete clone.morphAttributes[key]
        }
      }

      for (const key in clone.attributes) {
        if (key !== 'position') {
          clone.deleteAttribute(key)
        }
      }

      geometries.push(clone)
    }
  })

  const mergedGeometry = mergeGeometries(geometries, false)
  const mergeTime = window.performance.now() - timeStart
  console.log(`Merge object: ${model.name} time: ${mergeTime.toFixed(2)} ms`)
  return mergedGeometry
}
