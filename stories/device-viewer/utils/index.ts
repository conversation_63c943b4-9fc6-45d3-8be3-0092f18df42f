import { Box3, <PERSON><PERSON>, MeshStandardMaterial, Object3D, Vector3 } from 'three'

export const clearThreeObjectMesh = (object: Object3D) => {
  // @ts-ignore
  if (typeof object.destroy === 'function') {
    // @ts-ignore
    object.destroy()
    return
  }
  while (object.children.length > 0) {
    clearThreeObjectMesh(object.children[0])
    object.children[0]?.removeFromParent()
  }

  if (object instanceof Mesh) {
    try {
      object.geometry?.dispose()
    } catch (e) {

    }
  } else { // @ts-ignore
    // if (object.dispose) { // @ts-ignore
    //   object.dispose()
    // }
  }
}

export const uuid = (): string => {
  return URL.createObjectURL(new Blob([])).substr(-36)
}

export function getElementLeftPosition(element: any) {
  // 获取元素的bounding box
  var rect = element.getBoundingClientRect()

  // 如果存在父元素，获取父元素的bounding box，否则设为{ left: 0, top: 0 }
  var parentRect = element.parentNode ? element.parentNode.getBoundingClientRect() : { left: 0, top: 0 }

  // 计算元素中心点的相对位置
  var xPos = rect.left - parentRect.left
  var yPos = rect.top - parentRect.top + rect.height / 2

  // 容错处理，如果没有父元素，或父元素是body，直接基于视口计算
  if (!element.parentNode || element.parentNode === document.body) {
    xPos = rect.left + window.scrollX
    yPos = rect.top + window.scrollY + rect.height / 2
  }

  return {
    x: xPos,
    y: yPos
  }
}

export function getRelativePosition(child: Element, target: Element) {
  const childRect = child.getBoundingClientRect()
  const targetRect = target.getBoundingClientRect()

  // 计算相对位置
  const relativeTop = childRect.top - targetRect.top
  const relativeLeft = childRect.left - targetRect.left

  return { y: relativeTop, x: relativeLeft }
}

export function getVector3FromArray(arr: number[]) {
  return new Vector3(arr[0], arr[1], arr[2])
}

export function getLocationPosition(obj: Object3D, wp: Vector3) {
  obj.updateMatrixWorld(true)
  const worldMatrix = obj.matrixWorld.invert()
  return wp.clone().applyMatrix4(worldMatrix)
}

export function getScaledSettings() {

  let tiles = 3
  let renderScale = Math.max(1 / window.devicePixelRatio, 0.5)

  // adjust performance parameters for mobile
  const aspectRatio = window.innerWidth / window.innerHeight
  if (aspectRatio < 0.65) {

    tiles = 4
    renderScale = 1 / window.devicePixelRatio

  }

  return { tiles, renderScale }

}

export function generateMorphTargetWithTarget(target: Mesh) {
  try {
    const originArr = target.geometry.attributes.position.array
    const originPoint = new Vector3(...originArr)
    return generateMorphTarget(originPoint, target.geometry.morphAttributes.position, target.morphTargetInfluences || [])

  } catch (e) {
    return new Vector3()
  }
}

export function generateMorphTarget(originPoint: Vector3, morphPositions: any[], influences: number[]) {
  const target = originPoint.clone()
  const displacements = morphPositions.map(morphPosition => {
    const displacement = new Vector3()

    for (let i = 0; i < morphPosition.count; i++) {
      displacement.set(morphPosition.getX(i), morphPosition.getY(i), morphPosition.getZ(i))
    }

    return displacement
  })
  displacements.forEach((dist: Vector3, index) => {
    const influence = influences[index] || 0 // 防止 influences 数组长度不匹配
    target.add(dist.clone().multiplyScalar(influence))
  })
  return target
}

export function calculateTotalDimensionsUsingVertices(object: Object3D): {
  box: Box3,
  maxRadius: number,
  height: number,
  hasLength: boolean
  hasWidth: boolean
  hasHeight: boolean
} {
  let hasLength = false, hasWidth = false, hasHeight = false

  let minX = Infinity, minY = Infinity, minZ = Infinity
  let maxX = -Infinity, maxY = -Infinity, maxZ = -Infinity
  let maxRadius = 0
  let height = 0

  object.traverse((child: Object3D) => {
    if ((child as Mesh).isMesh && (child as Mesh).geometry) {
      const mesh = child as Mesh
      if (mesh.userData.ignoreSize) {
        return
      }
      if ((mesh.material as MeshStandardMaterial).userData.isLOGO) {
        return
      }
      mesh.geometry.computeBoundingBox()
      let positionAttribute = mesh.geometry.attributes.position

      const morphAttributes = mesh.geometry.morphAttributes.position
      let morphInfluences = mesh.morphTargetInfluences

      const keys = Object.keys(mesh.morphTargetDictionary || {})
      if (keys.includes('width')) hasWidth = true
      if (keys.includes('length')) hasLength = true
      if (keys.includes('height')) hasHeight = true

      for (let i = 0; i < positionAttribute.count; i++) {
        let x = positionAttribute.getX(i)
        let y = positionAttribute.getY(i)
        let z = positionAttribute.getZ(i)

        height = Math.max(y, height)

        if (morphAttributes && morphInfluences) {
          morphAttributes.forEach((morphAttr, index) => {
            const val = (morphInfluences && morphInfluences[index]) || 0
            x += morphAttr.getX(i) * val
            y += morphAttr.getY(i) * val
            z += morphAttr.getZ(i) * val
          })
        }

        if (x < minX) minX = x
        if (y < minY) minY = y
        if (z < minZ) minZ = z
        if (x > maxX) maxX = x
        if (y > maxY) maxY = y
        if (z > maxZ) maxZ = z

        // Calculate distance from the origin (0, 0, 0) to the current vertex
        const distance = Math.sqrt(x * x + z * z)
        if (distance > maxRadius) maxRadius = distance
      }
    }
  })

  // Create and return the bounding box and maximum radius
  const box = new Box3(new Vector3(minX, minY, minZ), new Vector3(maxX, maxY, maxZ))
  return { box, maxRadius, height, hasWidth, hasLength, hasHeight }
}

export function calculateInscribedCircleRadius(length: number, width: number): number {
  // 计算对角线长度
  const diagonal = Math.sqrt(length * length + width * width)
  // 返回半径，即对角线长度的一半
  return diagonal / 2
}


export function calculateCircleRadiusByBox3(box: Box3): number {
  const center = new Vector3()
  return Math.max(
    center.distanceTo(new Vector3(box.min.x, box.min.y, box.min.z)),
    center.distanceTo(new Vector3(box.min.x, box.min.y, box.max.z)),
    center.distanceTo(new Vector3(box.min.x, box.max.y, box.min.z)),
    center.distanceTo(new Vector3(box.min.x, box.max.y, box.max.z)),
    center.distanceTo(new Vector3(box.max.x, box.min.y, box.min.z)),
    center.distanceTo(new Vector3(box.max.x, box.min.y, box.max.z)),
    center.distanceTo(new Vector3(box.max.x, box.max.y, box.min.z)),
    center.distanceTo(new Vector3(box.max.x, box.max.y, box.max.z))
  )
}

export function debounce(fn: any, delay: number, scope: any = null) {
  let timer = 0
  // 返回函数对debounce作用域形成闭包
  return function () {
    // setTimeout()中用到函数环境总是window,故需要当前环境的副本；
    // @ts-ignore
    let context = scope || this, args = arguments
    // 如果事件被触发，清除timer并重新开始计时
    clearTimeout(timer)
    timer = window.setTimeout(function () {
      fn.apply(context, args)
    }, delay)
  }
}

export class ObjectSize {
  constructor(public length: number, public width: number, public height: number) {
  }
}

export const generateLogoSize = (m: Mesh) => {
  let width = 0
  let height = 0

  if (m.geometry) {
    m.geometry.computeBoundingBox()
    const size = new Vector3()
    m.geometry.boundingBox?.getSize(size)
    if (size.y) {
      height = size.y
      if (!size.x && size.z) {
        width = size.z
      } else if (size.x && !size.z) {
        width = size.x
      } else if (size.x && size.z) {
        width = size.z
      }
    } else {
      width = size.z
      height = size.x
    }
  }

  return { width, height }
}
