import { SAH }                       from 'three-mesh-bvh'
import { PathTracingSceneGenerator } from 'three-gpu-pathtracer'
import { GenerateMeshBVHWorker }     from './generate-mesh-bvh-worker'
import { Object3D }                  from 'three'

export class PathTracingSceneWorker extends PathTracingSceneGenerator {
  bvhGenerator = new GenerateMeshBVHWorker()

  constructor() {
    super()
  }

  generate(scene: Object3D[] | Object3D, options = {}) {
    const { bvhGenerator } = this
    const { geometry, materials, textures, lights, spotLights } = this.prepScene(scene)

    const bvhOptions = { strategy: SAH, ...options, maxLeafTris: 1 }
    const bvhPromise = bvhGenerator.generate(geometry, bvhOptions)
    return bvhPromise.then(bvh => {

      return {
        scene,
        materials,
        textures,
        lights,
        spotLights,
        bvh
      }

    })

  }

  dispose() {

    this.bvhGenerator.dispose()

  }

}
