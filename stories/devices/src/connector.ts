import Pin              from '@/devices/src/pin'
import { generateUUID } from 'three/src/math/MathUtils'

export default class Connector {
  readonly uuid: string
  connections: Pin[] = []

  constructor(uuid?: string) {
    if (uuid) {
      this.uuid = uuid
    } else {
      this.uuid = generateUUID()
    }
    this.connections = []
  }

  addConnection(pin: Pin) {
    this.connections.push(pin)
  }

  describe() {
    console.log(`Interface ID: ${this.uuid}`)
    this.connections.forEach(pin => pin.describe())
  }
}
