export interface ConstraintExecResult {
  valid: boolean;
  message?: string;
}

export abstract class Constraintable {
  protected _valid: boolean

  protected constructor() {
    this._valid = false
  }

  get isValid(): boolean {
    return this._valid
  }

  abstract exec(): ConstraintExecResult;
}

export default class Constraint extends Constraintable {
  constructor() {
    super()
  }

  exec(): ConstraintExecResult {
    // 实现具体的约束执行逻辑
    this._valid = true  // 例如，某种条件使得 valid 变为 true
    return {
      valid: this._valid,
      message: 'Constraint successfully executed.'
    }
  }

  addConnection() {
    // 添加连接的具体逻辑
  }

  describe() {
    console.log('Connections for this constraint:')
  }
}
