import { ConstraintExecResult, Constraintable } from '@/devices/src/constraints/constraint'
import Pin                                     from '@/devices/src/pin'

export default class SameDeviceConstraint extends Constraintable {
  constructor(public source: Pin, public target: Pin) {
    super()
  }

  setTarget(target: Pin) {
    this.target = target
    return this
  }

  exec(): ConstraintExecResult {
    this._valid =
      this.source.deviceId === this.target.deviceId
    return {
      valid: this.isValid
    }
  }
}
