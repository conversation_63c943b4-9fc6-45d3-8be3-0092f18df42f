import Pin from '@/devices/src/pin'

export default class Device {

  pins: Pin[] = []

  get deviceId() {
    return this._deviceId
  }

  get name() {
    return this._name
  }

  constructor(private _deviceId: string, private _name: string) {
    this.pins = []
  }

  addPin(pin: Pin) {
    pin.deviceId = this.deviceId
    this.pins.push(pin)
  }

  describe() {
    console.log(`Device ID: ${this.deviceId}`)
    this.pins.forEach(pin => pin.describe())
  }
}
