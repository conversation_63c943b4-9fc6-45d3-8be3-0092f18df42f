import { Constraintable } from '@/devices/src/constraints/constraint'

export enum PIN_TYPES {
  VCC = 'VCC',
  '24VDC IN-' = '24VDC IN-',
  GND = 'GND',
  'DI-NPN' = 'DI-NPN',
  PDO = 'PDO',
  '485-B' = '485-B',
  '485-A' = '485-A',
  '232-T' = '232-T',
  '232-R' = '232-R',
  'CAN-L' = 'CAN-L',
  'CAN-H' = 'CAN-H',
  'SWITCH-N' = 'SWITCH-N',
  'SWITCH-O' = 'SWITCH-O',
  'BOOT-KEY' = 'BOOT-KEY',
  'BOOT-LIGHT' = 'BOOT-LIGHT',
  'MANUAL-CHARGING' = 'MANUAL-CHARGING',
  'EMC IN' = 'EMC IN',
  'EMC P-' = 'EMC P-',
  'EMC P+' = 'EMC P+',
  LAN = 'LAN',
  USB = 'USB',
  HDMI = 'HDMI',
  SPK = 'SPK',
  MIC = 'MIC',
  WIFI = 'WIFI',
  'Contactor O' = 'Contactor O',
  'Contactor N' = 'Contactor N'
}

export default class Pin {
  private _deviceId: string = ''

  private _constraints: Constraintable[] = []

  get deviceId() {
    return this._deviceId
  }

  set deviceId(deviceId: string) {
    this._deviceId = deviceId
  }

  constructor(
    private pinID: string, private pinType: PIN_TYPES, private group: any,
    private voltageMin: number, private voltageMax: number, private currentMax: number) {
  }

  describe() {
    console.log(`Pin ID: ${this.pinID}, Type: ${this.pinType}, Group: ${this.group}, Voltage: ${this.voltageMin}-${this.voltageMax} V, Max Current: ${this.currentMax} A`)
  }
}
