import { getDoubleAt } from "../utils/GetNumber";
import { mirrorAngle } from "../utils/mirror";
import { Entity } from "./Entity";
import type { DrawingProperties } from "../utils/DrawingProperties";
import { Point } from "./Point"


class Arc extends Entity {
    /**
     * @center coordinate of the Arc center in CAD metric
     */
    public center: Point;
    /**
   * @radius radius of the Arc in CAD metric
   */
    public radius: number;
    /**
   * @start_angle start angle of the Arc in CAD metric (rad)
   */
    public start_angle: number;

    /**
     * @end_angle end angle of the Arc in CAD metric (rad)
     */
    public end_angle: number;

    /**
      * @start_point start point coordinate of the Arc in CAD metric
      */
    public start_point: Point;

    /**
     * construct a Arc entity from bytes
     * @param bytes 
     */
    constructor(bytes: Uint8Array) {
        super(bytes);
        let index: number = this.HeaderLen;
        this.center = new Point(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 4));
        this.radius = getDoubleAt(bytes, index + 8);

        this.start_angle = getDoubleAt(bytes, index + 12);
        this.end_angle = getDoubleAt(bytes, index + 16);
        this.start_point = new Point(this.radius * Math.cos(this.start_angle) + this.center.x, this.radius * Math.sin(this.start_angle) + this.center.y);
    }

    /**
     * display this Arc entity on Canvas
     * @param ctx 
     * @param drawing_pro 
     * @param color 
     */
    public display(ctx: CanvasRenderingContext2D, drawing_pro: DrawingProperties, color: string) {
        let scale = drawing_pro.scale;
        ctx.moveTo(this.start_point.x * scale, this.start_point.y * scale * -1);
        ctx.arc(this.center.x * scale, this.center.y * scale * -1, this.radius * scale, mirrorAngle(this.start_angle), mirrorAngle(this.end_angle), true);
    }

    /**
     * combine properties of this Arc entity into a string
     * @returns 
     */
    public ToString(): string {
        return super.ToString() + " center " + this.center.ToString() + "; radius: " +
            this.radius.toString() + ";start angle:" + this.start_angle.toString() + ";end angle:" + this.end_angle.toString();
    }
}

export { Arc }