import type { Entity } from "./Entity";
import type { Point } from "./Point";

export enum BlockType {
    Normal = 1,
    Polygon = 2,
    SinglePath = 3, // 里面的每个图元绘制的时候需要换画笔.
    Dash = 4,
}
export class Block {
    private _entities: Entity[] = [];
    public color: string;
    public line_weight: number;
    public type: BlockType;
    public get entities(): Entity[] {
        return this._entities;
    }
    public AddOneEntity(entity: Entity) {
        this._entities.push(entity);
    }
}