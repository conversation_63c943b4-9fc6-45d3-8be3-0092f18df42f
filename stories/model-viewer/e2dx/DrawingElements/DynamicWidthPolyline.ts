import { getBooleanAt, getDoubleAt, getUint32At, getUint8At } from "../utils/GetNumber";
import { Entity } from "./Entity";
import type { DrawingProperties } from "../utils/DrawingProperties";
import { Point } from "./Point";


class DynamicWidthPolyline extends Entity {

    /**
     * 动态宽度值.
     */
    public dynamic_width = 0;

    /**
     * @vertices list stores vertices of the polyline
     */
    public vertices: Point[] = [];

    constructor(bytes: Uint8Array) {
        super(bytes);
        let index = this.HeaderLen;
        this.dynamic_width = getDoubleAt(bytes, index);
        index += 4;
        let vertices_count = getUint32At(bytes, index);
        index += 4;
        for (let i = 0; i < vertices_count; ++i) {
            this.vertices.push(new Point(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 4)));
            index += 8;
        }
        
    }

    /**
     * display this polyline on the Canvas
     * @param ctx 
     * @param drawing_pro 
     * @param color 
     * @returns 
     */
    public display(ctx: CanvasRenderingContext2D, drawing_pro: DrawingProperties, color: string): void {
        ctx.lineCap = 'square';
        let scale = drawing_pro.scale;

        let len = this.vertices.length;
        if (len <= 0) return;
        ctx.lineWidth = this.dynamic_width * scale;
        ctx.moveTo(this.vertices[0].x * scale, this.vertices[0].y * scale * -1);
        

        for (let i = 1; i < len; ++i) {
            ctx.lineTo(this.vertices[i].x * scale, this.vertices[i].y * scale * -1);
        }
    }

    /**
     * 
     * @returns  all properties of the Polyline
     */
    public ToString() {
        let vertex;
        let vertices_info = "";
        for (vertex in this.vertices) {
            vertices_info += vertex.ToString();
        }

        return "isPoint:" + this.dynamic_width.toString() + "\nvertices info" + vertices_info;
    }
}

export { DynamicWidthPolyline }