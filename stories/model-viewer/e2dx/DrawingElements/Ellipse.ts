import { Entity } from './Entity'
import { Point } from './Point';
import { getDoubleAt, getUint8At } from "../utils/GetNumber";
import { mirrorAngle } from '../utils/mirror';
import type { DrawingProperties } from "../utils/DrawingProperties";
import { DirVector } from '../utils/SubElement';
import { transition_in } from 'svelte/internal';

function cal_start_point(center:Point,start_angle:number,major_radius:number,minor_radius:number,rotation:number):Point{
    let start_point:Point;
    
    if(start_angle%(2*Math.PI)==0){

        start_point= new Point(major_radius,0);

    }else if(start_angle%(3/2*Math.PI)==0){

        start_point=new Point(0,-minor_radius);

    }else if(start_angle%(Math.PI)==0){

        start_point= new Point(-major_radius,0);

    }
    else if(start_angle%(Math.PI/2)==0){

        start_point= new Point(0,minor_radius);

    }else{
        let theta=Math.atan2(minor_radius/major_radius*Math.tan(start_angle),1);
        start_point=new Point(major_radius*Math.cos(theta),minor_radius*Math.sin(theta));
    }


    let start_point_x=start_point.x*Math.cos(rotation)+start_point.y*Math.sin(rotation)+center.x;
    let start_point_y=start_point.x*Math.sin(rotation)+start_point.y*Math.cos(rotation)+center.y;

    return new Point(start_point_x,start_point_y);

}

/**
 * 暂时废弃
 * @classdesc
 */
class EllipseArc extends Entity {
    public center: Point;
    public major_radius: number;
    public minor_radius: number;
    public rotation:number;
    public start_angle: number;
    public end_angle: number;
    public start_point:Point;

    constructor(bytes: Uint8Array) {
        super(bytes);
        let index: number = this.HeaderLen;
        
        this.center = new Point(getDoubleAt(bytes, index),
            getDoubleAt(bytes, index + 4));
        index += 8;
        
        this.major_radius=getDoubleAt(bytes,index);
        index+=4;

        this.minor_radius=getDoubleAt(bytes,index);
        index+=4;

        this.rotation=getDoubleAt(bytes,index);
        index+=4;

        this.start_angle = getDoubleAt(bytes, index);
        index += 4;

        this.end_angle = getDoubleAt(bytes, index);
        index + 4;

        this.start_point=cal_start_point(this.center,this.start_angle,this.major_radius,this.minor_radius,this.rotation);

    }

    public display(ctx:CanvasRenderingContext2D, drawing_pro: DrawingProperties,color:string): void {
        return;
            let scale = drawing_pro.scale;
 
            let a=this.major_radius;
            let b=this.minor_radius;
            let alpha=this.rotation*-1;


            let start_angle=this.start_angle;
            let end_angle=this.end_angle;

           
            start_angle=mirrorAngle(start_angle);
            end_angle=mirrorAngle(end_angle);
            ctx.moveTo(this.start_point.x*scale,this.start_point.y*scale*-1);
            //ctx.beginPath();
            ctx.ellipse(this.center.x*scale,this.center.y*scale*(-1),a*scale,b*scale,alpha,start_angle,end_angle,true);    
           
    }

    public ToString(): string {
        return super.ToString() + " center " + this.center.ToString() + "\nmajor axis:" + this.major_radius.toString()
            + "\nminor axis:" + this.minor_radius.toString() + "\nstart angle:" + this.start_angle.toString() + "\nend angle:" + this.end_angle.toString();
    }

}

/**
 * 暂时废弃
 * @classdesc
 */
class EllipseArcFilled extends Entity {
    public fillType: number
    public center: Point;
    public major_radius:number;
    public minor_radius:number;
    public rotation:number;
    public start_angle: number;
    public end_angle: number;
    public start_point:Point;

    constructor(bytes: Uint8Array) {
        super(bytes);

        let index: number = this.HeaderLen;

        this.fillType = getUint8At(bytes, index);
        index + 1;

        this.center = new Point(getDoubleAt(bytes, index),
            getDoubleAt(bytes, index + 4));
        index += 8;

        this.major_radius=getDoubleAt(bytes,index);
        index+=4;

        this.minor_radius=getDoubleAt(bytes,index);
        index+=4;

        this.rotation=getDoubleAt(bytes,index);
        index+=4;

        this.start_angle = getDoubleAt(bytes, index);
        index += 4;

        this.end_angle = getDoubleAt(bytes, index);
        index + 4;

        this.start_point=cal_start_point(this.center,this.start_angle,this.major_radius,this.minor_radius,this.rotation);

    }

    public display(ctx: CanvasRenderingContext2D, drawing_pro: DrawingProperties, color: string): void {
        return;
  

    }

    public ToString(): string {
        return super.ToString() + " center " + this.center.ToString() + "\nmajor axis:" + this.major_radius.toString()
            + "\nminor axis:" + this.minor_radius.toString() + "\nstart angle:" + this.start_angle.toString() + "\nend angle:" + this.end_angle.toString();
    }

}

export { EllipseArc, EllipseArcFilled };