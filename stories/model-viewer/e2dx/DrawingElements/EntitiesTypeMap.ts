import { EntitiesType } from "./Entity";
import { Arc } from "./Arc";
import { EllipseArc, EllipseArcFilled } from "./Ellipse";
import { Hatch } from "./Hatch";
import { EntityPoint } from "./EntityPoint";
import { PolyLine } from "./PolyLine";
import { <PERSON> } from "./Ray";
import { RasterImage } from "./RasterImage";
import { InfLine } from "./InfLine";
import { Polygon } from "./Polygon";
import { EntityText } from "./Text";
import { DynamicWidthPolyline } from "./DynamicWidthPolyline";


// import { Point } from "./Point";

export const entitiesTypeFuncMap = {

    [EntitiesType.Polyline]: (bytes: Uint8Array) => { return new PolyLine(bytes); },
    [EntitiesType.Arc]: (bytes: Uint8Array) => { return new Arc(bytes); },
    // [EntitiesType.EllipseArc]: (bytes: Uint8Array) => { return new EllipseArc(bytes); },
    // [EntitiesType.EllipseArcFilled]: (bytes: Uint8Array) => { return new EllipseArcFilled(bytes); },
    [EntitiesType.Polygon]: (bytes: Uint8Array) => { return new Polygon(bytes); },
    [EntitiesType.Hatch]: (bytes: Uint8Array) => { return new Hatch(bytes); },
    [EntitiesType.RasterImage]: (bytes: Uint8Array) => { return new RasterImage(bytes); },
    [EntitiesType.Ray]: (bytes: Uint8Array) => { return new Ray(bytes); },
    [EntitiesType.XLine]: (bytes: Uint8Array) => { return new InfLine(bytes); },
    [EntitiesType.Text]: (bytes: Uint8Array) => { return new EntityText(bytes); },
    [EntitiesType.DynamicWidthPolyline]: (bytes: Uint8Array) => { return new DynamicWidthPolyline(bytes); },
}
