import { getBooleanAt, getDoubleAt, getUint32At, getUint8At } from "../utils/GetNumber";
import { getColorAt } from "../utils/GetNumber";
import type { DrawingProperties } from "../utils/DrawingProperties";
import type { Point } from "./Point";
import type { E2dxLoader } from "../E2dxLoader";

export enum EntitiesType {
    Polyline = 1,
    Arc = 2,
    EllipseArc = 3,
    Polygon = 4,
    Hatch = 5,
    RasterImage = 6,
    Ray = 7,
    XLine = 8,
    Text = 10,
    EntityPoint = 11,
    DynamicWidthPolyline = 12,
};

export abstract class Entity {
    Type: EntitiesType;
    LayerIdx: number;
    LineWeight: number;
    Dashes: number[] = [];
    DashOffset: number;
    HeaderLen: number = 0;
    UsingDash: boolean = false;
    public Loader: E2dxLoader;
    /**
     * 通过bytes数组初始化图元
     * @param byte 从type开始的bytes(不包含data len), 仅包含完整数据即可.
     */
    constructor(byte: Uint8Array) {
        var index: number = 0;
        this.Type = byte[0];
        index += 1;

        this.LayerIdx = getUint8At(byte, index);
        index += 1;

        this.UsingDash = getBooleanAt(byte, index);
        index += 1;
        if(this.UsingDash) {
            this.DashOffset = getDoubleAt(byte, index);
            index += 4;
            let dash_num = getUint32At(byte, index);
            index += 4;
            for (let i = 0; i < dash_num; ++i) {
                this.Dashes.push(getDoubleAt(byte, index));
                index += 4;
            }
        }
        
        this.HeaderLen = index;
    }

    public abstract display(ctx: CanvasRenderingContext2D, drawing_pro: DrawingProperties, color: string): void;

    public draw(ctx: CanvasRenderingContext2D, drawing_pro: DrawingProperties, color: string) {
        // ctx.strokeStyle = color;

        if (!this.Loader.GetLayers()[this.LayerIdx].Visible) {
            return;
        } else {
            this.display(ctx, drawing_pro, color)
        }
    }

    public single_draw(ctx: CanvasRenderingContext2D, drawing_pro: DrawingProperties, color: string) {
        ctx.save();
        ctx.beginPath();
        this.draw(ctx, drawing_pro, color);
        ctx.stroke();
        ctx.closePath();
        ctx.restore();
    }

    public dash_draw(ctx: CanvasRenderingContext2D, drawing_pro: DrawingProperties, color: string) {
        if (this.Dashes.length != 0) {
            let dashes: number[] = [];
            this.Dashes.forEach((dash) => {
                let dash_temp = dash * drawing_pro.scale;
                if(dash_temp < 1) dash_temp = 1;
                dashes.push(dash_temp);
            })
            ctx.setLineDash(dashes);
        } else {
            ctx.setLineDash([]);
        }
        ctx.lineDashOffset = this.DashOffset * drawing_pro.scale;

        ctx.beginPath();
        this.draw(ctx, drawing_pro, color);
        ctx.stroke();
        ctx.closePath();
        
    }
    public ToString(): string {
        return '';
        // return "color: " + this.Color + " line weight: " + this.LineWeight.toString() + "layer idx:" + this.LayerIdx.toString() + ";line type idx:" + this.LineTypeIdx.toString();
    }
}

