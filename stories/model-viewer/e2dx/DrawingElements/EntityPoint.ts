import { Entity } from "./Entity";
import type { DrawingProperties } from "../utils/DrawingProperties";
import { Point } from "./Point";
import { getDoubleAt } from "../utils/GetNumber";


export class EntityPoint extends Entity {

    /**
     * @point point（x，y）
     */
    public point: Point;

    constructor(bytes: Uint8Array) {
        super(bytes);
        let index: number = this.HeaderLen;
        this.point = new Point(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 4));
    }

    /**
     * display the point on Canvas
     * @param ctx 
     * @param dwg_pro 
     * @param color 
     */
    public display(ctx: CanvasRenderingContext2D, dwg_pro: DrawingProperties, color: string): void {
        let scale = dwg_pro.scale;
        ctx.moveTo(this.point.x * scale, this.point.y * scale * (-1));
        ctx.lineTo((this.point.x ) * scale, (this.point.y) * scale * (-1));
    }

    /**
     * 
     * @returns properties of the point
     */
    public ToString() {
        return this.point.ToString();
    }
}