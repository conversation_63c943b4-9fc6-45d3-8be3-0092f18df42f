import { Entity } from './Entity'
import { Point } from './Point';
import { getBooleanAt, getDoubleAt } from "../utils/GetNumber";
import type { DrawingProperties } from "../utils/DrawingProperties";

class Face extends Entity {
    public vertices: Point[] = [];
    public edge_visbility: boolean[] = [];

    constructor(bytes: Uint8Array) {
        super(bytes);
        let index: number = this.HeaderLen;

        for (let i = 0; i < 4; i++) {
            this.vertices.push(new Point(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 8)));
            this.edge_visbility.push(getBooleanAt(bytes, index + 16));
            index += 17;
        }

    }

    public display(ctx: CanvasRenderingContext2D, drawing_pro: DrawingProperties, color: string): void {
        let scale = drawing_pro.scale;
        ctx.beginPath();
        ctx.strokeStyle = color;
        for (let i = 0; i < 4; i++) {
            if (this.edge_visbility[i] == true) {
                ctx.moveTo(this.vertices[i].x * scale, this.vertices[i].y * scale*(-1));
                ctx.lineTo(this.vertices[i == 3 ? 0 : i + 1].x * scale, this.vertices[i == 3 ? 0 : i + 1].y * scale * (-1));
            }
        }
        ctx.stroke();
        ctx.closePath();
    }

    public ToString(): string {
        let vertices_info = '';
        let edge_info='';
        for (let i = 0; i < 4; i++) {
            vertices_info += "No." + i.toString() + " vertex:" + this.vertices[i].ToString() + "\n";
        }

        for(let i=0;i<4;i++){
            edge_info+="No."+i.toString()+" visibility:"+this.edge_visbility[i]+"\n";
        }

        return super.ToString() + "\nvertices info:\n" + vertices_info+"\nedges info:\n"+edge_info;
    }

}

export { Face };