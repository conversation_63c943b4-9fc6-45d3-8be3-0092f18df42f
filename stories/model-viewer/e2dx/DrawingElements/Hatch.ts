import { Entity } from './Entity'
import { Point } from './Point';
import { getColorAt, getDoubleAt, getStringAt, getUint32At, getUint8At } from "../utils/GetNumber";
// import { PatternLine } from './PatternLine';
import type { DrawingProperties } from "../utils/DrawingProperties";
import { CalArcWithBulge } from "../utils/E2dx_math"
import { mirrorAngle } from '../utils/mirror';

enum IslandStyleType {
    Normal = 1,
    Outter = 2,
    Ignore = 3
}

class HatchFill {
    public line_verties: Point[] = [];
    public background_color: string;

    constructor(bytes: Uint8Array, index: number) {
        this.background_color = getColorAt(bytes, index);
        index += 4;
        let count = getUint32At(bytes, index);
        index += 4;
        for (let i = 0; i < count; ++i) {
            this.line_verties.push(new Point(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 4)));
            this.line_verties.push(new Point(getDoubleAt(bytes, index + 8), getDoubleAt(bytes, index + 12)));
            index += 16;
        }
    }
}
class HatchGradient {
    public type: number;
    public name: string;
    public begin_offset: number;
    public begin_color: string;
    public end_offset: number;
    public end_color: string;
    public angle: number;
    public offset: number;

    constructor(bytes: Uint8Array, index: number) {
        this.type = bytes[index];
        index += 1;
        let [str, num] = getStringAt(bytes, index);
        this.name = str as string;
        index += num as number;
        this.begin_offset = getDoubleAt(bytes, index);
        index += 4;
        this.begin_color = getColorAt(bytes, index);
        index += 4;
        this.end_offset = getDoubleAt(bytes, index);
        index += 4;
        this.end_color = getColorAt(bytes, index);
        index += 4;
        this.angle = getDoubleAt(bytes, index);
        index += 4;
        this.offset = getDoubleAt(bytes, index);
    }

    public ToString() {
        return "Gradient" + " type " + `${this.type}` +
            "; name " + `${this.name}` +
            "; begin_offset " + `${this.begin_offset}` +
            "; begin_color " + `${this.begin_color}` +
            "; end_offset " + `${this.end_offset}` +
            "; end_color " + `${this.end_color}` +
            "; angle " + `${this.angle}` +
            "; offset " + `${this.offset}`;
    }
}

function CreatePatten(width: number, height: number, type: string, begin_color: string, end_color: string, begin_offset: number, end_offset: number, angle: number) {
    let pcanvas = document.createElement("canvas") as HTMLCanvasElement;
    if(height < 1) height = 1;
    if(width < 1) width = 1;

    pcanvas.height = height;
    pcanvas.width = width;
    let pctx = pcanvas.getContext("2d");

    let gradient: CanvasGradient;

    if (type == "LINEAR") { // 线性
        gradient = pctx.createLinearGradient(0, 0, width, 0)
        gradient.addColorStop(begin_offset, begin_color);
        gradient.addColorStop(end_offset, end_color);
    } else if (type == "INVLINEAR") { // 反线
        gradient = pctx.createLinearGradient(0, 0, width, 0)
        gradient.addColorStop(begin_offset, end_color);
        gradient.addColorStop(end_offset, begin_color);
    } else if (type == "CYLINDER") { // 圆柱
        let total = begin_offset + end_offset;
        let mid_offset_left = total / 9 * 4;
        let mid_offset_right = total / 9 * 5;

        gradient = pctx.createLinearGradient(0, 0, width, 0)
        gradient.addColorStop(begin_offset, begin_color);
        gradient.addColorStop(mid_offset_left, end_color);
        gradient.addColorStop(mid_offset_right, end_color);
        gradient.addColorStop(end_offset, begin_color);
    } else if (type == "INVCYLINDER") {// 反圆柱
        let total = begin_offset + end_offset;
        let mid_offset_left = total / 9 * 4;
        let mid_offset_right = total / 9 * 5;

        gradient = pctx.createLinearGradient(0, 0, width, 0)
        gradient.addColorStop(begin_offset, end_color);
        gradient.addColorStop(mid_offset_left, begin_color);
        gradient.addColorStop(mid_offset_right, begin_color);
        gradient.addColorStop(end_offset, end_color);
    } else if (type == "SPHERICAL") {//圆
        let out_radius = Math.max(width / 2, height / 2);
        gradient = pctx.createRadialGradient(width / 2, height / 2, out_radius / 8, width / 2, height / 2, out_radius);

        gradient.addColorStop(begin_offset, end_color);
        gradient.addColorStop(end_offset, begin_color);
    } else if (type == "INVSPHERICAL") {//反圆
        let out_radius = Math.max(width / 2, height / 2);
        gradient = pctx.createRadialGradient(width / 2, height / 2, out_radius / 5, width / 2, height / 2, Math.max(width / 2, height / 2));

        gradient.addColorStop(begin_offset, begin_color);
        gradient.addColorStop(end_offset, end_color);
    } else if (type == "CURVED") {// 曲线
        // 直线近似显示.
        let total = begin_offset + end_offset;
        let mid_offset = total / 4 * 3;

        gradient = pctx.createLinearGradient(0, 0, 0, height)
        gradient.addColorStop(begin_offset, begin_color);
        gradient.addColorStop(mid_offset, end_color);

        gradient.addColorStop(end_offset, end_color);
        // let arc = CalArcWithBulge(new Point(0, height/2), new Point(width, height/2), height/2/width);
        // let y = Math.sqrt(arc.Radius*arc.Radius - width*width / 4);
        // gradient = pctx.createRadialGradient(width / 2, height, width/8, width/2, y + height/4, arc.Radius);

        // gradient.addColorStop(begin_offset, end_color);
        // // gradient.addColorStop()
        // gradient.addColorStop(end_offset, begin_color);
    } else if (type == "INVCURVED") {// 反曲线
        // 直线近似显示.
        let total = begin_offset + end_offset;
        let mid_offset = total / 4 * 3;

        gradient = pctx.createLinearGradient(0, 0, 0, height)
        gradient.addColorStop(begin_offset, end_color);
        gradient.addColorStop(mid_offset, begin_color);

        gradient.addColorStop(end_offset, begin_color);
    } else if (type == "HEMISPHERICAL") {// 半圆
        gradient = pctx.createRadialGradient(width / 2, height, 1, width / 2, height, Math.max(height, width));

        gradient.addColorStop(begin_offset, end_color);
        gradient.addColorStop(end_offset, begin_color);
    } else if (type == "INVHEMISPHERICAL") {// 反半圆
        gradient = pctx.createRadialGradient(width / 2, height, 1, width / 2, height, Math.max(height, width));

        gradient.addColorStop(begin_offset, begin_color);
        gradient.addColorStop(end_offset, end_color);

    } else { // make it linear.
        gradient = pctx.createLinearGradient(0, 0, width, 0)
        gradient.addColorStop(begin_offset, begin_color);
        gradient.addColorStop(end_offset, end_color);
    }

    pctx.fillStyle = gradient;
    pctx.fillRect(0, 0, width, height);

    return pcanvas;
}

class Hatch extends Entity {
    public island_style: number;
    public loop_points: Point[][] = [];
    public loop_convexities: number[][] = [];
    public gradient: HatchGradient;
    public BoundingRectLowLeft: Point;
    public BoundingRectUpRight: Point;

    constructor(bytes: Uint8Array) {
        super(bytes);
        let index = this.HeaderLen;
        this.BoundingRectLowLeft = new Point(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 4));
        index += 8;
        this.BoundingRectUpRight = new Point(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 4));
        index += 8;

        this.island_style = bytes[index];
        index += 1;

        let loop_count = getUint32At(bytes, index);
        index += 4;
        for (let i = 0; i < loop_count; ++i) {
            let point_count = getUint32At(bytes, index);
            index += 4;
            this.loop_points.push([]);
            for (let j = 0; j < point_count; ++j) {
                this.loop_points[i].push(new Point(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 4)));
                index += 8;
            }

            let loop_con_count = getUint32At(bytes, index);
            index += 4;
            this.loop_convexities.push([]);
            for (let ii = 0; ii < loop_con_count; ++ii) {
                this.loop_convexities[i].push(getDoubleAt(bytes, index));
                index += 4;
            }
        }

        this.gradient = new HatchGradient(bytes, index);

    }

    public display(ctx: CanvasRenderingContext2D, drawing_pro: DrawingProperties, color: string): void {
        let draw_loops = () => {
            let len = this.loop_points.length;
            if (this.island_style == IslandStyleType.Ignore) {
                len = 1;
            }
            for (let i = 0; i < len; ++i) {
                let loopver = this.loop_points[i];
                if (this.island_style == IslandStyleType.Outter) {
                    let if_counterclock = false;

                    // 计算是否时逆时针.

                    if ((loopver[0].x <= loopver[1].x && loopver[0].y >= loopver[1].y) ||
                        (loopver[0].x >= loopver[1].x && loopver[0].y <= loopver[1].y)
                    ) {
                        if_counterclock = false;
                    } else {
                        if_counterclock = true;
                    }
                    if (this.loop_convexities[i].length > 0 && this.loop_convexities[i][0] >= 0) {
                        if_counterclock = !if_counterclock;
                    }
                    if (loopver.length == 2) {
                        console.log(loopver[0], loopver[1], if_counterclock.toString(), this.loop_convexities[i][0].toString());
                    }
                    if (i != 0 && if_counterclock) { // 结束最外圈.
                        continue;
                    }
                }
                // 画线.
                ctx.moveTo(loopver[0].x * drawing_pro.scale, loopver[0].y * drawing_pro.scale * -1);
                for (let ii = 0; ii < loopver.length - 1; ++ii) {
                    if (this.loop_convexities[i].length == 0 || this.loop_convexities[i][ii] == 0) {
                        ctx.lineTo(loopver[ii + 1].x * drawing_pro.scale, loopver[ii + 1].y * drawing_pro.scale * -1);
                    } else {
                        let arc = CalArcWithBulge(loopver[ii + 1], loopver[ii], this.loop_convexities[i][ii]);
                        ctx.arc(arc.center.x * drawing_pro.scale, arc.center.y * drawing_pro.scale * -1, arc.Radius * drawing_pro.scale, mirrorAngle(arc.start_angle), mirrorAngle(arc.end_angle));
                    }

                }

                let len = loopver.length - 1;
                ctx.moveTo(loopver[len].x * drawing_pro.scale, loopver[len].y * drawing_pro.scale * -1);
                if (this.loop_convexities[i].length == 0 || this.loop_convexities[i][len] == 0) {
                    ctx.lineTo(loopver[len].x * drawing_pro.scale, loopver[0].y * drawing_pro.scale * -1);
                } else {
                    let arc = CalArcWithBulge(loopver[0], loopver[len], this.loop_convexities[i][len]);
                    ctx.arc(arc.center.x * drawing_pro.scale, arc.center.y * drawing_pro.scale * -1, arc.Radius * drawing_pro.scale, mirrorAngle(arc.start_angle), mirrorAngle(arc.end_angle));
                }

            }
        };

        let width = this.BoundingRectUpRight.x - this.BoundingRectLowLeft.x;
        let height = this.BoundingRectUpRight.y - this.BoundingRectLowLeft.y;
        if(width == 0 || height == 0) return;
        width *= drawing_pro.scale;
        height *= drawing_pro.scale;


        ctx.save();
        ctx.beginPath();

        // draw and fill loops.
        draw_loops();

        ctx.closePath();
        ctx.translate(this.BoundingRectLowLeft.x * drawing_pro.scale, this.BoundingRectUpRight.y * drawing_pro.scale * -1);
        // let pcanvas = CreatePatten(width, height, this.gradient.name, this.gradient.begin_color, this.gradient.end_color, this.gradient.begin_offset, this.gradient.end_offset, this.gradient.angle);
        let pcanvas = CreatePatten(width, height, this.gradient.name, this.gradient.begin_color, this.gradient.end_color, this.gradient.begin_offset, this.gradient.end_offset, this.gradient.angle);
        let pattern = ctx.createPattern(pcanvas, 'repeat');
        ctx.fillStyle = pattern;

        ctx.fill();
        // ctx.fillRect(this.loop_points[0][0].x * drawing_pro.scale, this.loop_points[0][0].y * drawing_pro.scale * -1, 100, 100);
        ctx.restore();
    }

    public ToString(): string {
        return '';
    }
}

export { Hatch, HatchFill, HatchGradient };