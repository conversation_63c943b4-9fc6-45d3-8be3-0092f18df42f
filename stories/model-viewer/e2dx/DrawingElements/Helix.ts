import { Entity } from './Entity'
import  { Point } from './Point';
import { getBooleanAt, getDoubleAt } from "../utils/GetNumber";
import { mirrorAngle } from '../utils/mirror';
import type { DrawingProperties } from "../utils/DrawingProperties";

/**
 * 暂时废弃
 */

class Helix extends Entity {
    public center: Point;
    public start_point: Point;
    public end_point:Point;
    public turns:number;
    public bottom_radius:number;
    public top_radius:number;
    public counterclockwise:boolean;
 
    constructor(bytes:Uint8Array) {
        super(bytes);
        let index:number = this.HeaderLen;
      
        this.start_point=new Point(getDoubleAt(bytes,index),getDoubleAt(bytes,index+8));
        index+=16;

        this.end_point=new Point(getDoubleAt(bytes,index),getDoubleAt(bytes,index+8));
        index+=16;

        this.center =new Point ( getDoubleAt(bytes, index), getDoubleAt(bytes, index+8));
        index+=16;

        this.top_radius=getDoubleAt(bytes,index);
        index+=8;

        this.bottom_radius=getDoubleAt(bytes,index);
        index+=8;

        this.turns=getDoubleAt(bytes,index);
        index+=8;

        this.counterclockwise=getBooleanAt(bytes,index);
        index++;

    }

    public display(ctx:CanvasRenderingContext2D, drawing_pro: DrawingProperties,color:string): void {
        let scale=drawing_pro.scale;
        let a=this.bottom_radius;
        let b=(this.top_radius-this.bottom_radius)/(2*Math.PI*this.turns);

        let start_angle=(Math.atan2(this.start_point.y-this.center.y,this.start_point.x-this.center.x));
        let end_angle=this.counterclockwise?(start_angle+Math.PI*2*this.turns):(start_angle-Math.PI*2*this.turns);

        let intervals=150;//用于控制拟合精度;TODO:考虑根据turns和长短轴长度进行动态优化

        ctx.beginPath();
        ctx.strokeStyle=color;
        ctx.moveTo(((a+b*start_angle)*Math.cos(start_angle)+this.center.x)*scale,((a+b*start_angle)*Math.sin(start_angle)+this.center.y)*(-1)*scale);

        var theta=start_angle;
        let step_angle=(end_angle-start_angle)/intervals;
        
        var r=0;
        for(let i=0;i<intervals;i++)
        {
            r=this.counterclockwise?(a+b*theta):(a+b*(-1*theta));
            ctx.lineTo((r*Math.cos(theta)+this.center.x)*scale,(r*Math.sin(theta)+this.center.y)*(-1)*scale);
           theta=theta+step_angle;
        }

        ctx.stroke();
        ctx.closePath();
    }

    public ToString() :  string {
        return super.ToString() + " center " + this.center.ToString() + "\nstart point:"+this.start_point.ToString()
        +"\nend point:"+this.end_point.ToString()+"\nturns:"+this.turns.toString()+"\nbottom radius:"+this.bottom_radius.toString()
        +"\ntop radius:"+this.top_radius.toString()+"\ncounter clockwise:"+this.counterclockwise.toString();
    }

}

export { Helix };