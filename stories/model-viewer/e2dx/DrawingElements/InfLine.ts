import { getDoubleAt } from "../utils/GetNumber";
import { Entity } from "./Entity";
import type { DrawingProperties } from "../utils/DrawingProperties";
import { Point } from "./Point";
import { DirVector } from "../utils/SubElement";


class InfLine extends Entity {
    /**
    * @base base point coordinate of Ray in CAD metric
    */
    public base: Point;
    /**
* @direction direction vector of Ray in CAD metric
*/
    public direction: DirVector;
    constructor(bytes: Uint8Array) {
        super(bytes);
        let index: number = this.HeaderLen;

        this.base = new Point(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 4));
        index += 8;

        this.direction = new DirVector(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 4));
        index += 8;

    }

    /**
 * display the xline on Canvas
 * @param ctx 
 * @param drawing_pro 
 * @param color 
 * @returns 
 */
    public display(ctx: CanvasRenderingContext2D, drawing_pro: DrawingProperties, color: string): void {
        let scale = drawing_pro.scale;

        var valid_points = this.CalIntercept(drawing_pro);

        if (valid_points.length == 2) {
            ctx.moveTo(valid_points[0].x * scale, valid_points[0].y * (-1) * scale);
            ctx.lineTo(valid_points[1].x * scale, valid_points[1].y * (-1) * scale);
        }
    }

    /**
 * 
 * @returns properties of this ray
 */
    public ToString(): string {
        return super.ToString() + "base point:" + this.base.ToString()
            + "direction:" + this.direction.ToString();
    }

    /**
  * 
  * @param drawing_pro 
  * @returns intercepts of xline and current viewport
  */
    CalIntercept(drawing_pro: DrawingProperties) {

        //视口框在CAD坐标系的几何描述
        let x1 = drawing_pro.cad_ul_p.x;
        let y1 = drawing_pro.cad_ul_p.y;
        let x2 = drawing_pro.cad_ul_p.x + drawing_pro.cad_width;
        let y2 = drawing_pro.cad_ul_p.y - drawing_pro.cad_height;
        var valid_points: Point[] = [];

        //垂直x轴的情况
        if (this.direction.delta_x == 0) {
            if (this.base.x >= x1 && this.base.x <= x2) {
                valid_points.push(new Point(this.base.x, y1));
                valid_points.push(new Point(this.base.x, y2));
                return valid_points;

            } else if (this.base.x < x1 || this.base.x > x2) {
                return valid_points;
            }
        }

        //平行x轴的情况
        if (this.direction.delta_y == 0) {
            if (this.base.y <= y1 && this.base.y >= y2) {
                valid_points.push(new Point(x1, this.base.y));
                valid_points.push(new Point(x2, this.base.y));
                return valid_points;

            } else if (this.base.x < x1 || this.base.x > x2) {
                return valid_points;

            }
        }

        //计算直线与视口框的四条边以及其延长线的交点
        let x_y1 = (y1 - this.base.y) / (this.direction.delta_y / this.direction.delta_x) + this.base.x;
        let x_y2 = (y2 - this.base.y) / (this.direction.delta_y / this.direction.delta_x) + this.base.x;
        let y_x1 = (x1 - this.base.x) * (this.direction.delta_y / this.direction.delta_x) + this.base.y;
        let y_x2 = (x2 - this.base.x) * (this.direction.delta_y / this.direction.delta_x) + this.base.y;


        //与upper side相交
        if (x_y1 > x1 && x_y1 <= x2) {
            valid_points.push(new Point(x_y1, y1));
        }

        //与right side相交
        if (y_x2 < y1 && y_x2 >= y2) {
            valid_points.push(new Point(x2, y_x2));
        }

        //与bottom side相交
        if (x_y2 >= x1 && x_y2 < x2) {
            valid_points.push(new Point(x_y2, y2));
        }

        //与left side相交
        if (y_x1 > y2 && y_x1 <= y1) {
            valid_points.push(new Point(x1, y_x1));
        }

        return valid_points;

    }

}

export { InfLine }