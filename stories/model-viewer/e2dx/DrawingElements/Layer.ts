import { getColorAt, getStringAt, getUint32At, getUint8At } from "../utils/GetNumber";

export class Layer {
    public Name : string; // 图层名字
    public LinetypeName: string; // 线型名字
    public LinetypeDescription: string; // 线型字符描述
    public Color: string; // 颜色
    public LineWeight: number; // 需要注意的是: 特殊值253(-3)表示线宽为默认, lineweight在[0-255], 并且是离散的, 单位为 lineweight/100 mm.
    public Visible : boolean; // 可见性(是否打开)
    public Frozen : boolean; // 是否冻结
    public DataLen : number;

    constructor(bytes : Uint8Array) {
        var index = 0;
        
        let [str, num] = getStringAt(bytes, index);
        this.Name = str as string;
        index += num as number;

        let [ltn_str, ltn_num] = getStringAt(bytes, index);
        this.LinetypeName = ltn_str as string;
        index += ltn_num as number;

        let [lt_str, lt_num] = getStringAt(bytes, index);
        this.LinetypeDescription = lt_str as string;
        index += lt_num as number;

        this.LineWeight = getUint8At(bytes, index);
        index += 1;
        this.Visible = !!bytes[index];
        this.Frozen = !!bytes[index + 1];
        index += 3; // 3个bool
        this.Color = getColorAt(bytes, index);
        index += 4;
        this.DataLen = index;
    }
}