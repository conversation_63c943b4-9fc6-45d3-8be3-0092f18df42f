import { getBooleanAt, getDoubleAt, getStringAt, getUint32At } from '../utils/GetNumber'
import { Point } from './Point'

export class Viewport {
  /**
   *视口左上点在layout空间中的坐标
   */
  public UpperLeft: Point
  /**
   * 视口在layout空间中的高度
   */
  public Height: number
  /**
   * 视口在layout空间中的宽度
   */
  public Width: number

  public Factor: number

  /**
   * 视口旋转角度
   */
  public Angle: number
  /**
   *视口左上点在model space中的坐标
   */
  public ModelUpperLeft: Point
  /**
   *视口在model space中的宽度
   */
  public ModelWidth: number
  public DisplayAsGrid: boolean
  /**
   * 视口是否被裁剪
   */
  public clip: boolean
  /**
   * 裁剪路径
   */
  public clip_path: Point[] = []
  public len: number = 0
  public first_time_draw = true

  constructor(bytes, index) {
    let start_index = index
    this.UpperLeft = new Point(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 4))
    index += 8
    this.Width = getDoubleAt(bytes, index)
    index += 4
    this.Height = getDoubleAt(bytes, index)
    index += 4
    // this.Factor = getDoubleAt(bytes, index);
    // index += 4;

    this.Angle = getDoubleAt(bytes, index)
    index += 4

    this.ModelUpperLeft = new Point(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 4))
    index += 8
    this.ModelWidth = getDoubleAt(bytes, index)
    index += 4

    this.DisplayAsGrid = getBooleanAt(bytes, index)
    index += 1

    this.clip = getBooleanAt(bytes, index)
    index += 1
    if (this.clip) {
      let count = getUint32At(bytes, index)
      index += 4

      for (let i = 0; i < count; ++i) {
        this.clip_path.push(new Point(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 4)))
        index += 8
      }
    }
    this.len = index - start_index
  }
}

const ActViewLen = 16

export class ActiveView {
  public upper_left: Point
  public width: number
  public height: number

  constructor(bytes, index) {
    this.upper_left = new Point(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 4))
    index += 8
    this.width = getDoubleAt(bytes, index)
    index += 4
    this.height = getDoubleAt(bytes, index)
    index += 4
  }
}

class Layout {
  public name: string
  /**
   * 是否是模型空间
   */
  public is_model: boolean
  /**
   * 视口数组
   */
  public viewports: Viewport[] = []
  public tab_order: number
  public tab_selected: boolean
  public lower_left: Point
  public upper_right: Point
  public anno_display: boolean
  public active_view: ActiveView
  public DataLen


  constructor(bytes: Uint8Array) {
    let index = 0
    let [str, num] = getStringAt(bytes, index)
    this.name = str as string
    index += num as number

    this.is_model = getBooleanAt(bytes, index)
    index += 1

    this.tab_order = getUint32At(bytes, index)
    index += 4

    this.tab_selected = getBooleanAt(bytes, index)
    index += 1

    this.lower_left = new Point(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 4))
    index += 8

    this.upper_right = new Point(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 4))
    index += 8

    this.anno_display = getBooleanAt(bytes, index)
    index += 1
    if (this.is_model) {
      this.active_view = new ActiveView(bytes, index)
      index += ActViewLen
    }


    let view_port_count = getUint32At(bytes, index)
    index += 4
    // for (let i = 0; i < view_port_count; ++i) {
    //     let viewport = new Viewport(bytes, index);
    //     this.viewports.push(viewport);
    //     index += viewport.len;
    // }
    this.DataLen = index
  }
}

export { Layout }