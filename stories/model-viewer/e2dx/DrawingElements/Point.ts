/**
 * @classdesc 只用于描述图元上的点信息，非图元类，没有display函数
 */
class Point {
    /**
     * @x x coordinate in CAD metric
     */
    public x: number;
    /**
  * @y y coordinate in CAD metric
  */
    public y: number;
    constructor(x: number, y: number) {
        this.x = x;
        this.y = y;
    }
    /**
     * 
     * @returns coordinates of the point
     */
    public ToString(): string {
        return "( " + this.x.toString() + " , " + this.y.toString() + ")\n";
    }
}

export { Point };