import { getBooleanAt, getDoubleAt, getUint32At, getUint8At } from "../utils/GetNumber";
import { mirrorAngle } from "../utils/mirror";
import { Entity } from "./Entity";
import type { DrawingProperties } from "../utils/DrawingProperties";
import { Point } from "./Point";
import { Segment } from "../utils/SubElement";
import { CalArcWithBulge, CalQuadranlge } from "../utils/E2dx_math";


class PolyLine extends Entity {
    /**
     * @vertices list stores vertices of the polyline
     */
    public vertices: Point[] = [];
    /**
 * @isPoint whether it is an isolated point
 */
    public isPoint: boolean;

    constructor(bytes: Uint8Array) {
        super(bytes);
        let index = this.HeaderLen;
        let vertices_count = getUint32At(bytes, index);
        index += 4;
        for (let i = 0; i < vertices_count; ++i) {
            this.vertices.push(new Point(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 4)));
            index += 8;
        }

        //Judge whether it is an isolated point
        if (this.vertices.length == 2 && this.vertices[0].x == this.vertices[1].x && this.vertices[0].y == this.vertices[1].y) {
            this.isPoint = true;
        } else {
            this.isPoint = false;
        }
        
    }

    /**
     * display this polyline on the Canvas
     * @param ctx 
     * @param drawing_pro 
     * @param color 
     * @returns 
     */
    public display(ctx: CanvasRenderingContext2D, drawing_pro: DrawingProperties, color: string): void {
        let scale = drawing_pro.scale;

        let len = this.vertices.length;
        if (len <= 0) return;
        ctx.moveTo(this.vertices[0].x * scale, this.vertices[0].y * scale * -1);

        // 如果是point的画, 手动画一个像素.
        if (this.isPoint) {
            ctx.lineTo(this.vertices[0].x * scale + 1, this.vertices[0].y * scale * -1 + 1);
            return;
        }

        for (let i = 1; i < len; ++i) {
            ctx.lineTo(this.vertices[i].x * scale, this.vertices[i].y * scale * -1);
        }
    }

    /**
     * 
     * @returns  all properties of the Polyline
     */
    public ToString() {
        if (this.isPoint) {
            return "isPoint:" + this.isPoint.toString() + "\n Position:" + this.vertices[0].ToString()
        }

        let vertex
        let vertices_info = ""
        for (vertex in this.vertices) {
            vertices_info += vertex.ToString()
        }

        return "isPoint:" + this.isPoint.toString() + "\nvertices info" + vertices_info
    }
}

export { PolyLine }