import { getBooleanAt, getColorAt, getDoubleAt, getUint32At, getUint8At } from "../utils/GetNumber";
import { mirrorAngle } from "../utils/mirror";
import { Entity } from "./Entity";
import type { DrawingProperties } from "../utils/DrawingProperties";
import { Point } from "./Point";
import { Segment } from "../utils/SubElement";
import { CalArcWithBulge, CalQuadranlge } from "../utils/E2dx_math";
import { ReplaceColor } from "../utils/ReplaceColor";



class Polygon extends Entity {
    /**
     * @vertices list stores all vertices of the polygon
     */
    public vertices: Point[] = [];
    /**
   * @fill_color list stores all vertices of the polygon
   */
    public fill_color: string;
    constructor(bytes: Uint8Array) {
        super(bytes);
        let index = this.HeaderLen;

        let vertices_count = getUint32At(bytes, index);
        index += 4;

        for (let i = 0; i < vertices_count; ++i) {
            this.vertices.push(new Point(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 4)));
            index += 8;
        }
        this.fill_color = getColorAt(bytes, index);
        index += 4;

    }
    /**
     * display the polygon on Canvas
     * @param ctx 
     * @param drawing_pro 
     * @param color 
     * @returns 
     */
    public display(ctx: CanvasRenderingContext2D, drawing_pro: DrawingProperties, color: string): void {
        let scale = drawing_pro.scale;
        let len = this.vertices.length;
        //如果是黑色背景，则需要对颜色进行处理
        ctx.fillStyle = ReplaceColor(this.fill_color, drawing_pro.using_black);
        ctx.strokeStyle = ReplaceColor(this.fill_color, drawing_pro.using_black);

        if (len <= 0) return;

        ctx.moveTo(this.vertices[0].x * scale, this.vertices[0].y * scale * -1);
        for (let i = 1; i < len; ++i) {
            ctx.lineTo(this.vertices[i].x * scale, this.vertices[i].y * scale * -1);
        }
        ctx.lineTo(this.vertices[0].x * scale, this.vertices[0].y * scale * -1);
    }

    /**
     * 
     * @returns all properties of the polygon
     */
    public ToString() {
        let str = 'polygon ';
        this.vertices.forEach((point) => {
            str += point.ToString();
        });
        return str;
    }
}

export { Polygon }