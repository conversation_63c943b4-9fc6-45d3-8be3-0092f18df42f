import { getBooleanAt, getDoubleAt } from "../utils/GetNumber";
import { Entity } from "./Entity";
import type { DrawingProperties } from "../utils/DrawingProperties";
import { Point } from "./Point";

/**
 * @classdesc 暂时废弃
 */
class PolygonMesh extends Entity{
    public m:number;
    public n:number;
    public mDensity:number;
    public nDensity:number;
    public mClosed:boolean;
    public nClosed:boolean;

    public vertices:Point[];

    constructor(bytes:Uint8Array){
        super(bytes);
        let index=this.HeaderLen;
        this.m=getDoubleAt(bytes,index);
        index+=8;

        this.n=getDoubleAt(bytes,index);
        index+=8;

        this.mDensity=getDoubleAt(bytes,index);
        index+=8;

        this.nDensity=getDoubleAt(bytes,index);
        index+=8;

        this.mClosed=getBooleanAt(bytes,index);
        index+=1;

        this.nClosed=getBooleanAt(bytes,index);
        index+=1;

        for(let i=0;i<this.m*this.n;i++)
        {       
                this.vertices[i]=new Point(getDoubleAt(bytes,index),getDoubleAt(bytes,index+8));
                index+=16;      
        }
    }

    public display(ctx:CanvasRenderingContext2D, drawing_pro: DrawingProperties,color:string):void{

    }

    public ToString():string{
        let vertices_info;
        for(let i=0;i<this.m*this.n;i++){
           vertices_info=+"m idx="+(Math.floor(i/this.n)-1).toString()+"  n idx="+(i%this.n-1).toString()+ this.vertices[i].ToString()+";";
        }

        return super.ToString()+";m:"+this.m.toString()+";n:"+this.n.toString()+"mDensity:"+this.mDensity.toString()
        +"mClosed:"+this.mClosed.toString()+";nClosed"+this.nClosed.toString()+vertices_info;
    }
}

export{PolygonMesh}