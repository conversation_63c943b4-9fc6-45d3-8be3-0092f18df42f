import { Entity } from './Entity'
import { Point } from './Point';
import { getDoubleAt, getStringAt, getUint32At } from "../utils/GetNumber";
import { DirVector } from '../utils/SubElement';
import type { DrawingProperties } from '../utils/DrawingProperties';
import { mirrorAngle } from '../utils/mirror';


class RasterImage extends Entity {
    /**
     * @height_scale 高度缩放
     */
    public height_scale: number;

    /**
     * @width_scale 宽度缩放
     */
    public width_sacle: number;

    /**
     * @origin origin point(lowerleft) coordinate of raster image in CAD metric
     */
    public origin: Point;

    /**
     * @u_dir u direction vector of raster image from origin point
     */
    public u_dir: DirVector;

    /**
     * @u_dir v direction vector of raster image from origin point
     */
    public v_dir: DirVector;
    /**
     * @vertices list stores vertices of the image.The same as clip_vertices[] if the image is clipped.
     */
    public verties: Point[] = [];
    /**
     * @vertices list stores vertices of the clip path of the image.The same as vertices[] if the image is clipped.
     */
    public clip_verties: Point[] = [];

     /**
     * @path the path to directory where source image stored
     */
    public path: string;
    
    constructor(bytes: Uint8Array) {
        super(bytes);
        let index = this.HeaderLen;

        this.origin = new Point(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 4));
        index += 8;

        this.u_dir = new DirVector(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 4));
        index += 8;

        this.v_dir = new DirVector(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 4));
        index += 8;

        this.height_scale = getDoubleAt(bytes, index);
        index += 4;
        this.width_sacle = getDoubleAt(bytes, index);
        index += 4;

        let vert_count = getUint32At(bytes, index);
        index += 4;
        for (let i = 0; i < vert_count; ++i) {
            this.verties.push(new Point(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 4)));
            index += 8;
        }

        let is_clip = !!bytes[index];
        index += 1;
        if (is_clip) {
            let clip_count = getUint32At(bytes, index);
            index += 4;

            for (let i = 0; i < clip_count; ++i) {
                this.clip_verties.push(new Point(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 4)));
                index += 8;
            }
        }
        let [str, _] = getStringAt(bytes, index);
        this.path = str as string;

    }

    /**
     * display this raster image
     * @param ctx 
     * @param drawing_pro 
     * @param color 
     */
    public display(ctx: CanvasRenderingContext2D, drawing_pro: DrawingProperties, color: string): void {
        let scale = drawing_pro.scale;

        let v = this.verties;

        let new_origin = this.origin;
        let angle = this.u_dir.toAngle();

        var img = new Image();

        //TODO:这里需要重新设计服务器获取图片的方式
        // img.src=this.path;
        img.src = "rasterimage237.png";

        var patt = document.createElement("canvas");
        patt.width = this.u_dir.magnitude() * drawing_pro.scale;
        patt.height = this.v_dir.magnitude() * drawing_pro.scale;


        console.log("patt w:" + patt.width + ",h:" + patt.height);
        console.log("scale:", scale);
        console.log("angle:", angle / Math.PI * 180);
        console.log("mir angle:", mirrorAngle(angle) / Math.PI * 180);
        angle = mirrorAngle(angle);


        let trans = ctx.getTransform();
        console.log("Trans:", ctx.getTransform());
        img.onload = function () {
            ctx.save();
            //onload的时候，canvas原点回回退到上文的位置，所以要取得最近的转换矩阵，得到旋转和偏移量，在onload里手动平移
            ctx.translate(trans.e, trans.f);

            ctx.beginPath();
            ctx.moveTo(v[0].x * scale, v[0].y * scale * -1);
            for (let i = 1; i < v.length; i++) {
                ctx.lineTo(v[i].x * scale, v[i].y * scale * -1);
            }
            ctx.lineTo(v[0].x * scale, v[0].y * scale * -1);

            //将canvas原点移到图片origin point
            ctx.translate(new_origin.x * scale, new_origin.y * scale * -1);
            //绕着origin point旋转angle
            ctx.rotate(angle);
            //将canvas原点移到图片左上点
            ctx.translate(0, patt.height * -1);

            let pctx = patt.getContext("2d");
            pctx.drawImage(img, 0, 0, patt.width, patt.height);

            ctx.fillStyle = ctx.createPattern(patt, "no-repeat");


            ctx.fill();

            //标记坐标圆心
            ctx.closePath();
            ctx.restore();

        };

    }

    /**
     * 
     * @returns all properties of this raster image
     */
    public ToString(): string {
        let str = super.ToString();

        str += "\n Verties: ";
        this.verties.forEach((point, index) => {
            str += " verties" + index.toString() + " " + point.ToString() + "\n";
        });

        str += "\n Clip verties: ";
        this.clip_verties.forEach((point, index) => {
            str += "point" + index.toString() + " " + point.ToString() + "\n";
        });

        str += "\nheight_scale" + this.height_scale.toString() +
            "\nwidth_sacle" + this.width_sacle.toString() +
            "\npath" + this.path + "\n origin:(" + this.origin.x + ", " + this.origin.y + ")" +
            "\n u:(" + this.u_dir.delta_x + "," + this.u_dir.delta_y + ")" +
            "\n v:(" + this.v_dir.delta_x + "," + this.v_dir.delta_y + ")"
        return str;
    }

    /**
     * create a pattern filled with a specific image
     * @param width 
     * @param height 
     * @returns 
     */
    CreatePatten(width: number, height: number) {

        let pcanvas = document.createElement("canvas") as HTMLCanvasElement;
        pcanvas.height = height;
        pcanvas.width = width;
        let pctx = pcanvas.getContext("2d");

        var img = new Image();   // 创建一个<img>元素
        img.src = this.path;    //设置源地址
        img.onload = function () {
            pctx.drawImage(img, 0, 0, pcanvas.width, pcanvas.height);
        }

        return pcanvas;
    }


}


export { RasterImage };