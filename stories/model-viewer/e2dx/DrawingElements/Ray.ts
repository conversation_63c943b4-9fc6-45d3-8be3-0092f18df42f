import { getDoubleAt } from "../utils/GetNumber";
import { Entity } from "./Entity";
import type { DrawingProperties } from "../utils/DrawingProperties";
import { Point } from "./Point";
import { DirVector } from "../utils/SubElement";

class Ray extends Entity {

    /**
     * @base base point coordinate of Ray in CAD metric
     */
    public base: Point;
    /**
    * @direction direction vector of Ray in CAD metric
    */
    public direction: DirVector;

    constructor(bytes: Uint8Array) {
        super(bytes);
        let index = this.HeaderLen;
        this.base = new Point(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 4));
        index += 8;

        this.direction = new DirVector(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 4));
        index += 8;

    }

    /**
     * display the ray on Canvas
     * @param ctx 
     * @param drawing_pro 
     * @param color 
     * @returns 
     */
    public display(ctx: CanvasRenderingContext2D, drawing_pro: DrawingProperties, color: string) {
        //计算射线与视口的交点
        var valid_points = this.CalIntercept(drawing_pro);

        let scale = drawing_pro.scale;

        //如果没有交点，不需要绘制
        if (valid_points.length == 0) {
            return;

        }
        //只有一个交点。说明base在视口内，直接连线base和交点
        else if (valid_points.length == 1) {
            ctx.moveTo(this.base.x * scale, this.base.y * (-1) * scale);
            ctx.lineTo(valid_points[0].x * scale, valid_points[0].y * (-1) * scale);

        }
        //有两个交点，连线这两个交点
        else {
            ctx.moveTo(valid_points[0].x * scale, valid_points[0].y * (-1) * scale);
            ctx.lineTo(valid_points[1].x * scale, valid_points[1].y * (-1) * scale);

        }
    }

    /**
     * 
     * @returns properties of this ray
     */
    public ToString(): string {
        return super.ToString() + "\nbase point:" + this.base.ToString() + "direction:" + this.direction.ToString();
    }

    /**
     * 
     * @param drawing_pro 
     * @returns intercepts of ray and current viewport
     */
    CalIntercept(drawing_pro: DrawingProperties) {
        var valid_points: Point[] = [];

        //视口框在CAD坐标系的几何描述
        let x1 = drawing_pro.cad_ul_p.x;
        let y1 = drawing_pro.cad_ul_p.y;
        let x2 = drawing_pro.cad_ul_p.x + drawing_pro.cad_width;
        let y2 = drawing_pro.cad_ul_p.y - drawing_pro.cad_height;

        //如果射线垂直x轴
        if (this.direction.delta_x == 0) {

            //如果base在视口内
            if (this.IsInsideViewPort(drawing_pro)) {
                valid_points.push(new Point(this.base.x, this.direction.delta_y > 0 ? y1 : y2));//相交于上边or下边
                return valid_points;

                //base点在视口框下方，且射线方向是朝上的
            } else if (this.base.y < y2 && this.direction.delta_y > 0) {
                valid_points.push(new Point(this.base.x, y1));
                valid_points.push(new Point(this.base.x, y2));
                return valid_points;

                //base点在视口框上方，且射线方向是朝下的
            } else if (this.base.y > y1 && this.direction.delta_y < 0) {
                valid_points.push(new Point(this.base.x, y1));
                valid_points.push(new Point(this.base.x, y2));
                return valid_points;

                //不相交，返回空数组
            } else {
                return valid_points;
            }
        }

        //如果射线垂直y轴
        if (this.direction.delta_y == 0) {
            //如果base在视口内
            if (this.IsInsideViewPort(drawing_pro)) {
                valid_points.push(new Point(this.direction.delta_x > 0 ? x2 : x1, this.base.y)); //相交于左边or右边
                return valid_points;

                //base点在视口框左方，且射线方向是朝右
            } else if (this.base.x < x1 && this.direction.delta_x > 0) {
                valid_points.push(new Point(x1, this.base.y));
                valid_points.push(new Point(x2, this.base.y));
                return valid_points;

                //base点在视口框右方，且射线方向是朝左
            } else if (this.base.x > x2 && this.direction.delta_x < 0) {
                valid_points.push(new Point(x1, this.base.y));
                valid_points.push(new Point(x2, this.base.y));
                return valid_points;

                //不相交，返回空数组
            } else {
                return valid_points;
            }
        }

        //计算射线与视口框的四条边以及其延长线的交点
        let x_y1 = (y1 - this.base.y) / (this.direction.delta_y / this.direction.delta_x) + this.base.x;
        let x_y2 = (y2 - this.base.y) / (this.direction.delta_y / this.direction.delta_x) + this.base.x;
        let y_x1 = (x1 - this.base.x) * (this.direction.delta_y / this.direction.delta_x) + this.base.y;
        let y_x2 = (x2 - this.base.x) * (this.direction.delta_y / this.direction.delta_x) + this.base.y;

        let upper_valid = true;
        let bottom_valid = true;
        let left_valid = true;
        let right_valid = true;

        //upper
        x_y1 > x1 && x_y1 <= x2 && ((y1 - this.base.y) * this.direction.delta_y + (x_y1 - this.base.x) * this.direction.delta_x) > 0 ? valid_points.push(new Point(x_y1, y1)) : upper_valid = false;
        //right
        y_x2 < y1 && y_x2 >= y2 && ((y_x2 - this.base.y) * this.direction.delta_y + (x2 - this.base.x) * this.direction.delta_x) > 0 ? valid_points.push(new Point(x2, y_x2)) : right_valid = false;
        //bottom
        x_y2 >= x1 && x_y2 < x2 && ((y2 - this.base.y) * this.direction.delta_y + (x_y2 - this.base.x) * this.direction.delta_x) > 0 ? valid_points.push(new Point(x_y2, y2)) : bottom_valid = false;
        //left
        y_x1 > y2 && y_x1 <= y1 && ((y_x1 - this.base.y) * this.direction.delta_y + (x1 - this.base.x) * this.direction.delta_x) > 0 ? valid_points.push(new Point(x1, y_x1)) : left_valid = false;


        return valid_points;


    }

    /**
     * 
     * @param drawing_pro 
     * @returns whether it is in the viewport
     */
    IsInsideViewPort(drawing_pro: DrawingProperties): boolean {
        return (this.base.x >= drawing_pro.cad_ul_p.x && this.base.x <= (drawing_pro.cad_ul_p.x + drawing_pro.cad_width) && this.base.y >= (drawing_pro.cad_ul_p.y - drawing_pro.cad_height) && this.base.y <= drawing_pro.cad_ul_p.y);
    }
}

export { Ray }