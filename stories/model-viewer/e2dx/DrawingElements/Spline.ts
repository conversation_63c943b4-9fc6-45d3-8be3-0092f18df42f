import { getBooleanAt, getDoubleAt, getUint32At, getUint8At } from "../utils/GetNumber";
import { Entity } from "./Entity";
import type { DrawingProperties } from "../utils/DrawingProperties";
import {Point} from "./Point";
import { Spline_generator } from "../utils/E2dx_math";

/**
 * @classdesc 暂时废弃
 */

class Spline extends Entity{
    public eidit_by_fitpoints:boolean;
    public closed:boolean;
    public rational:boolean;
    public control_tolerance:number;
    public knot_tolerance:number;
    public vertices_num:number;
    public vertices:Point[]=[];
    public weights:number[]=[];
    public order:number;
    public knots_num:number;
    public knots:number[]=[];

    public fit_results:Point[]=[];

    constructor(bytes:Uint8Array){
        super(bytes);
        let index=this.HeaderLen;
        this.eidit_by_fitpoints=getBooleanAt(bytes,index);
        index++;

        this.closed=getBooleanAt(bytes,index);
        index++;

        this.order=getUint32At(bytes,index);
        index+=4;

        this.rational=getBooleanAt(bytes,index);
        index++;

        this.control_tolerance=getDoubleAt(bytes,index);
        index+=8;

        this.knot_tolerance=getDoubleAt(bytes,index);
        index+=8;
        
        this.vertices_num=getUint32At(bytes,index);
        index+=4;

        for(let i=0;i<this.vertices_num;i++)
        {
            this.vertices.push(new Point(getDoubleAt(bytes,index),getDoubleAt(bytes,index+8)));
            index+=16;
            this.weights.push(getDoubleAt(bytes,index));
            index+=8;
        }
        
        function isZeroWeight(weight:number){
            return weight>0;
       }

        if(!this.weights.every(isZeroWeight)){
            for(let i=0;i<this.vertices_num;i++)
            {
                this.weights[i]=1/this.vertices_num;
            }
        }

        this.knots_num=getUint32At(bytes,index);
        index+=4;

        for(let i=0;i<this.knots_num;i++){
            this.knots.push(getDoubleAt(bytes,index));
            index+=8;
        }

        this.fit_results=Spline_generator(this.vertices,this.order,this.knots,this.weights,this.vertices_num*this.order*30);
    }

    public display(ctx:CanvasRenderingContext2D,drawing_pro: DrawingProperties,color:string):void {
        
        let scale=drawing_pro.scale;
        ctx.strokeStyle="red";
        ctx.beginPath();
        ctx.moveTo(this.vertices[0].x*scale,this.vertices[0].y*scale*-1);
        for(let i=0;i<this.vertices_num;i++){
            ctx.lineTo(this.vertices[i].x*scale,this.vertices[i].y*scale*-1);
        }
        ctx.stroke();
        ctx.closePath();
              
        ctx.strokeStyle=color;
       
        ctx.beginPath();
        ctx.moveTo(this.fit_results[0].x*scale,this.fit_results[0].y*scale*-1);
        this.fit_results.forEach(function(p){
            ctx.lineTo(p.x*scale,p.y*scale*-1);
        });
        ctx.stroke();
        ctx.closePath();
        
    }

    public ToString():string{
        let vertices_info:string = '';
        let knots_info:string = '';
        for(let i=0;i<this.vertices_num;i++){
            vertices_info+=("No."+i.toString()+" vertex:"+this.vertices[i].ToString()+"weight:"+this.weights[i].toString()+"\n");
        }

        for(let i=0;i<this.vertices_num+this.order+1;i++){
            knots_info+=("No."+i.toString()+" knot vector:"+this.knots[i].toString()+"\n");
        }

        return super.ToString()+";edit by fit points:"+this.eidit_by_fitpoints.toString()+";closed:"+this.closed.toString()
        +"\nrational:"+this.rational.toString()+"\nnumber of vertices:\n"+this.vertices_num.toString()
        +"\nvertices list:"+vertices_info+"\norder:"+this.order.toString()+"\nknot list:\n"+knots_info;

    }
    
}

export{Spline}