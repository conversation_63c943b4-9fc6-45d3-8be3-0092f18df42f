import { getBooleanAt, getColorAt, getDoubleAt, getStringAt, getUint32At, getUint8At } from "../utils/GetNumber";
import { mirrorAngle } from "../utils/mirror";
import { Entity } from "./Entity";
import type { DrawingProperties } from "../utils/DrawingProperties";
import { Point } from "./Point";
import { DirVector } from "../utils/SubElement";
import { ReplaceColor } from "../utils/ReplaceColor";

class EntityText extends Entity {
    public position: Point;
    public x_angle: number;
    public y_angle: number;
    public msg: string;
    public font: string;
    public is_bold: boolean;
    public is_italic: boolean;
    public height: number;
    public over_lined: boolean;
    public under_lined: boolean;
    public a: number;
    public b: number;
    public c: number;
    public d: number;

    public color: string;

    constructor(bytes: Uint8Array) {
        super(bytes);
        let index: number = this.HeaderLen;

        let [msg, len] = getStringAt(bytes, index);
        this.msg = msg as string;
        index += len as number;

        let [font, len_font] = getStringAt(bytes, index);
        this.font = font as string;
        index += len_font as number;

        this.is_italic = getBooleanAt(bytes, index);
        index += 1;
        this.is_bold = getBooleanAt(bytes, index);
        index += 1;
        this.over_lined = getBooleanAt(bytes, index);
        index += 1;
        this.under_lined = getBooleanAt(bytes, index);
        index += 1;

        this.height = getDoubleAt(bytes, index);
        index += 4;
        this.color = getColorAt(bytes, index);
        index += 4;



        this.a = getDoubleAt(bytes, index)
        this.b = getDoubleAt(bytes, index + 4);
        index += 8;
        this.c = getDoubleAt(bytes, index)
        this.d = getDoubleAt(bytes, index + 4);
        index += 8;
        this.position = new Point(getDoubleAt(bytes, index), getDoubleAt(bytes, index + 4));
        index += 4;

        // replace unicode eg: \U+00B0 => \u{00B0}.
        const reg = /\\U\+([\da-zA-Z]{4,4})/g
        let msg_replace = '';
        let j = -1;
        for(let i =0; i< this.msg.length;) {
            j = this.msg.slice(i).search(reg);
            if(j == -1) break;
            msg_replace += this.msg.slice(i, j);
            let rst = this.msg.substr(j, j + 7);
            msg_replace += String.fromCodePoint(parseInt(rst.replace(reg, '$1'), 16));
            i = j + 7;
        }
        if(msg_replace.length > 0)
            this.msg = msg_replace;
    }

    public display(ctx: CanvasRenderingContext2D, drawing_pro: DrawingProperties, color: string): void {
        let scale = drawing_pro.scale;
        ctx.save();
        ctx.beginPath();

        ctx.translate(this.position.x * scale, this.position.y * scale * -1);
        let angle = Math.atan(this.b / this.a) * -1;
        ctx.rotate(angle);

        let scale_x = this.a / Math.cos(angle);
        let scale_y = scale_x;
        ctx.scale(scale_x, scale_y);

        ctx.fillStyle = ReplaceColor(this.color, drawing_pro.using_black);

        let font_style = (this.height * scale * 0.6).toString() + "px "
        font_style += (this.font + (this.is_bold ? " bold " : " ") + (this.is_italic ? " italic" : ""));
        ctx.font = font_style;
        ctx.fillText(this.msg, 0, 0);
        ctx.closePath();
        ctx.restore();
    }

    public ToString(): string {
        return '';
    }
}

export { EntityText }