import { getBooleanAt, getDoubleAt } from "../utils/GetNumber";
import { Entity } from "./Entity";
import type { DrawingProperties } from "../utils/DrawingProperties";
import { Point } from "./Point";

/**
 * @classdesc 暂时废弃
 */
class Viewport extends Entity{
    public center_layout:Point;
    public height_layout:number;
    public width_layout:number;
    public center_modelspace:Point;
    public height_modelspace:number;
    public is_perspective:boolean;
    public perspective_lenslength:number;
    public twist_angle:number;

    constructor(bytes:Uint8Array){
        super(bytes);
        let index=this.HeaderLen;
        this.center_layout=new Point(getDoubleAt(bytes,index),getDoubleAt(bytes,index+8));
        index+=16;

        this.height_layout=getDoubleAt(bytes,index);
        index+=8;

        this.width_layout=getDoubleAt(bytes,index);
        index+=8;

        this.center_modelspace=new Point(getDoubleAt(bytes,index),getDoubleAt(bytes,index+8));
        index+=8;

        this.height_modelspace=getDoubleAt(bytes,index);
        index+=8;

        this.is_perspective=getBooleanAt(bytes,index);
        index++;

        this.perspective_lenslength=getDoubleAt(bytes,index);
        index+=8;

        this.twist_angle=getDoubleAt(bytes,index);
        index+=8;
    }

    public display(ctx:CanvasRenderingContext2D, drawing_pro: DrawingProperties,color:string):void{}

    public ToString():string{
        return super.ToString()+";center in layout:"+this.center_layout.ToString()
        +";viewport height in layout:"+this.height_layout+";viewport width in layout:"+this.width_layout
        +";center in model space:"+this.center_modelspace.ToString()+";view port height in model space:"+this.height_modelspace.toString()
        +";perspective is on:"+this.is_perspective.toString()+";lens length of perspective:"+this.perspective_lenslength.toString()
        +"twist angle:"+this.twist_angle.toString();
    }

}

export{Viewport}