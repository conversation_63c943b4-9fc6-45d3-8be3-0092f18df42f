import { Block, BlockType } from './DrawingElements/Block'
import { EntitiesType, Entity } from './DrawingElements/Entity'
import { Layer } from './DrawingElements/Layer'
import { getColorAt, getUint32At, getUint8At } from './utils/GetNumber'
import { entitiesTypeFuncMap } from './DrawingElements/EntitiesTypeMap'
import { Layout } from './DrawingElements/Layout'
import { TypedArray } from 'three/src/core/BufferAttribute'
import { Color, RingGeometry } from 'three'
import PolylineGeometry from '@/utils/polyline-geometry'
import { Point } from '@/libs/e2dx/utils/SubElement'

export interface MeshData {
  position: TypedArray
  indices: TypedArray
  color: Color
  type?: EntitiesType
  colors?: Color[]
  layerIndex: number
}

interface ShapeData {
  points: Point[]
  color: string
}

class E2dxLoader {
  private data: Uint8Array
  private _blocks: Block[] = []
  private _layers: Layer[] = []
  private _layouts: Layout[] = []
  private _entity_count = 0
  private _has_3d = false

  meshes: MeshData[] = []

  private _polyline = new PolylineGeometry(1 / 1000)
  private _shapes: ShapeData[] = []
  private _arcs: RingGeometry[] = []

  constructor(url: string, callback) {
    this.load_binary_resource(url, callback)
  }

  public Parse = () => {
    var index: number = 0
    if (!this.check_e2dx()) console.error('this file is not an e2dx file.')
    index += 4//e2dx
    console.log('version is %s', this.get_version())
    index += 2//version
    let has_3d_val = getUint8At(this.data, index)
    this._has_3d = (has_3d_val & 0b10000000) == 0b10000000
    // console.log('has 3d: ', this._has_3d)
    index += 2// hole

    let layer_offset = getUint32At(this.data, index)
    index += 4
    let linetype_offset = getUint32At(this.data, index)
    index += 4
    let textstyle_offset = getUint32At(this.data, index)
    index += 4
    let layout_offset = getUint32At(this.data, index)
    index += 4
    let block_offset = getUint32At(this.data, index)
    index += 4

    // 分析Layer
    let layer_count = getUint32At(this.data, layer_offset)
    index = layer_offset + 4
    for (let i = 0; i < layer_count; ++i) {
      let layer = new Layer(this.data.slice(index, -1))
      this._layers.push(layer)
      index += layer.DataLen
    }

    // TODO: // generate layout
    let layout_count = getUint32At(this.data, layout_offset)
    index = layout_offset + 4
    for (let i = 0; i < layout_count; ++i) {
      let layout = new Layout(this.data.slice(index, -1))
      this._layouts.push(layout)
      index += layout.DataLen
    }

    // 分析blocks.
    // console.log('block offset is %d', this._layouts.length)
    // this._layouts.sort((layout_a, layout_b) => {
    //   if (layout_a.tab_order > layout_b.tab_order) return 1
    //   else if (layout_a.tab_order < layout_b.tab_order) return -1
    //   else return 0
    // })

    // 分析blocks.
    // console.log('block offset is %d', block_offset)

    let blocks_count = getUint32At(this.data, block_offset)
    index = block_offset + 4
    // console.log('block count %d', blocks_count)
    let blockref_entities: Entity[] = []
    for (let i = 0; i < blocks_count; ++i) {
      let block = new Block()
      // block.is_layout = getBooleanAt(this.data, index);
      // index += 1;
      block.color = getColorAt(this.data, index)
      index += 4
      block.line_weight = Math.round(getUint8At(this.data, index) / 25)
      if (block.line_weight < 1) {
        block.line_weight = 1
      }
      index += 1
      block.type = getUint8At(this.data, index)
      index += 1

      let entities_count = getUint32At(this.data, index) // 此block图元数量
      index += 4
      for (let j = 0; j < entities_count; ++j) {
        let data_len = getUint32At(this.data, index) // 图元数据长度
        index += 4 // 数据长度占用4bytes
        if (data_len > this.data.length - index) {
          console.error('logic error,e2dx binary may be broken')
          return
        }
        let dash: number
        let dashes: number[] = []

        let type: EntitiesType = this.data[index]
        let entity_data = this.data.slice(index, index + data_len)

        index += data_len
        if (!(type in entitiesTypeFuncMap)) {
          console.warn('type %s process function not found', EntitiesType[type])
        } else {
          let entity: Entity = entitiesTypeFuncMap[type](entity_data)
          // entity.Loader = this

          if (type === EntitiesType.Polyline || type === EntitiesType.DynamicWidthPolyline) {
            this._polyline.append(entity.vertices, false, new Color(block.color))
          } else if (type === EntitiesType.Polygon) {
            this._shapes.push({
              points: entity.vertices.map(r => new Point(r.x / 1000, r.y / 1000)),
              color: entity.fill_color
            })
          } else if (type === EntitiesType.Arc) {
            const { center, radius, start_angle, end_angle } = entity

            const startAngle = start_angle
            const endAngle = end_angle
            const thetaLength = endAngle - startAngle

            const innerRadius = radius / 1000 - 0.01
            const outerRadius = radius / 1000 + 0.01

            this._arcs.push({
              center: new Point(center.x / 1000, center.y / 1000),
              innerRadius,
              outerRadius,
              startAngle,
              thetaLength,
              color: entity.fill_color || block.color || '#000000'
            })
          } else {
            console.log(type)
          }

          block.AddOneEntity(entity)
          entity_data = null
        }
      }
      if (block.type != BlockType.Normal) {
        this._entity_count += block.entities.length
      }
      this._blocks.push(block)
    }
  }

  public GetLayers() {
    return this._layers
  }

  public GetLayouts() {
    return this._layouts
  }

  public Has3d() {
    return this._has_3d
  }

  public GetTextStyles() {
  }

  public GetLineTypes() {
  }

  public GetBlocks() {
    return this._blocks
  }

  public GetComplexEntityCount() {
    return this._entity_count
  }

  private load_binary_resource(url: string, callback) {
    let byteArray = []
    let req = new XMLHttpRequest()
    req.addEventListener('loadend', () => {
      if (req.status != 200) return byteArray
      for (let i = 0; i < req.responseText.length; ++i) {
        byteArray.push(req.responseText.charCodeAt(i) & 0xff)
      }

      this.data = new Uint8Array(byteArray)

      this.Parse()

      this.meshes = [this._polyline.getInfo()]
      this._shapes.length && (
        this.meshes.push({
          type: EntitiesType.Polygon,
          position: [],
          indices: [],
          colors: [],
          shapes: this._shapes
        })
      )
      if (this._arcs.length) {
        this.meshes.push({
          type: EntitiesType.Arc,
          position: [],
          indices: [],
          colors: [],
          shapes: this._arcs
        })
      }
      callback(this)
      this.data = null
      this._polyline = []
      this._arcs = []
      this._shapes = []
    })
    req.open('GET', url, true)
    req.overrideMimeType('text\/plain; charset=x-user-defined')
    req.send(null)
  }

  private check_e2dx(): boolean {
    return new TextDecoder().decode(this.data.slice(0, 4)) === 'E2DX'
  }

  private get_version(): string {
    return this.data[4].toString() + '.' + this.data[5].toString()
  }
}

export { E2dxLoader }