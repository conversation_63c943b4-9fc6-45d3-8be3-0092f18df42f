import type { Point } from "../DrawingElements/Point";

export class DrawingProperties {
    /**
     * 视口左上角坐标(CAD metric)
     */
    public cad_ul_p: Point;

    /**
     * 缩放系数
     */
    public scale: number;

    /**
     *CAD视口的高度 (CAD metric)
     */
    public cad_height: number;
    /**
     * CAD视口的宽度 (CAD metric)
     */
    public cad_width: number;

    /**
     * 是否使用黑色背景
     */
    public using_black: boolean;

    constructor(ul: Point, scale: number, canvas_height: number, canvas_width: number, using_black: boolean) {
        this.cad_ul_p = ul;
        this.scale = scale;
        this.cad_width = canvas_width / scale;
        this.cad_height = canvas_height / canvas_width * canvas_width / scale;
        this.using_black = using_black;
    }
}