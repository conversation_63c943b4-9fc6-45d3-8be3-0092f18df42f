import { Point } from "../DrawingElements/Point";
import { ArcElement, Segment } from "./SubElement";

/**
 * Generate a Arc with bulge value
 * @param start start point for the Arc in CAD metric
 * @param end end point for the Arc in CAD metric
 * @param bulge bulge parameter
 * @returns Arc expression with center,radius ,start/end angle  (CAD metric)
 */
export function CalArcWithBulge(start: Point, end: Point, bulge: number) {

    let Axis = Math.atan2(end.y - start.y, end.x - start.x);
    let L = Math.sqrt(Math.pow((start.x - end.x), 2) + Math.pow((start.y - end.y), 2));
    let b = 0.5 / (1 / bulge - bulge);
    let r = L / 2 * b;
    let tan_alpha_pow2 = 4 * Math.pow(bulge, 2) / Math.pow(1 - Math.pow(bulge, 2), 2);
    let sin_alpha = Math.sqrt(1 - 1 / (1 + tan_alpha_pow2))
    let cos_alpha = Math.sqrt(1 / (1 + tan_alpha_pow2));

    let Radius = L / 2 / sin_alpha;
    // console.log("Radius:" + Radius + "\n");
    bulge = 0.5 * (1 / bulge - bulge);

    var center = new Point(0.5 * ((start.x + end.x) - (end.y - start.y) * bulge), 0.5 * ((start.y + end.y) + (end.x - start.x) * bulge));

    let start_angle = (Math.atan2(start.y - center.y, start.x - center.x));
    let end_angle = (Math.atan2(end.y - center.y, end.x - center.x));


    return new ArcElement(center, Radius, start_angle, end_angle);

}

/**
 * Calculate the geometrical info of a polyline segment whose start and end width are not equal
 * @param vertex_start :start vertex point (CAD metric)
 * @param vertex_end :end vertex point (CAD metric)
 * @param seg :segment info (CAD metric)
 * @returns list of points stores quadrAngle vertices
 */
export function CalQuadranlge(vertex_start: Point, vertex_end: Point, seg: Segment) {
    let Axis = (vertex_end.y - vertex_start.y) / (vertex_end.x - vertex_start.x);
    let k = Math.atan(-1 / Axis);

    var QuadrAngle: Point[] = [];
    QuadrAngle.push(new Point(vertex_start.x + seg.width_begin / 2 * Math.cos(k), vertex_start.y + seg.width_begin / 2 * Math.sin(k)));
    QuadrAngle.push(new Point(vertex_end.x + seg.width_end / 2 * Math.cos(k), vertex_end.y + seg.width_end / 2 * Math.sin(k)));
    QuadrAngle.push(new Point(2 * vertex_end.x - QuadrAngle[1].x, 2 * vertex_end.y - QuadrAngle[1].y));
    QuadrAngle.push(new Point(2 * vertex_start.x - QuadrAngle[0].x, 2 * vertex_start.y - QuadrAngle[0].y));

    return QuadrAngle;

}

/**
 * Generate a serial of fitting points of a spline
 * @param control_points 控制点
 * @param order  阶数
 * @param node_vector 节点向量
 * @param weights 各个控制点的权重
 * @param accuracy 拟合点数量，用于控制拟合精度，default=1000
 * @returns list of fitting points
 */
export function Spline_generator(control_points: Point[], order: number, node_vector: number[], weights: number[], accuracy: number = 1000): Point[] {
    let fit_results: Point[] = [];
    let Nik_w: number[] = [];
    for (let i = 0; i < control_points.length; i++) {
        Nik_w.push(0);
    }

    let u_step = node_vector[node_vector.length - 1] / accuracy;
    for (let i = 0; i <= accuracy; i++) {

        let Dominator = 0;
        let u = i * u_step;
        for (let j = 0; j < control_points.length; j++) {
            Nik_w[j] = (BaseFunction(j, order, u, node_vector) * weights[j]);
            Dominator += Nik_w[j];
        }


        let x = 0;
        let y = 0;
        control_points.forEach(function (cp, index) {
            x += cp.x * Nik_w[index] / Dominator;
            y += cp.y * Nik_w[index] / Dominator;
        });

        fit_results.push(new Point(x, y));
    }

    return fit_results;
}

/**
 * calculate distance between two points
 * @param p1 :point 1 (CAD metric)
 * @param p2 :point 2 (CAD metric)
 * @returns distance between two points (CAD metric)
 */
export function DistanceOf(p1: Point, p2: Point): number {
    return Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));
}

/**
 * Calculate Base function
 * @param i 
 * @param order 
 * @param u 
 * @param node_vector 
 * @returns 
 */
function BaseFunction(i: number, order: number, u: number, node_vector: number[]):number {

    if (i < 0) {
        return 1;
    }

    if (order == 0) {


        if (u >= node_vector[i] && u < node_vector[i + 1]) {

            return 1;

        } else {

            return 0;

        }

    } else {

        let length1 = node_vector[i + order] - node_vector[i];
        let length2 = node_vector[i + order + 1] - node_vector[i + 1];
        length1 = (length1 == 0 ? 1 : length1);
        length2 = (length2 == 0 ? 1 : length2);

        return (u - node_vector[i]) / length1 * BaseFunction(i, order - 1, u, node_vector) + (node_vector[i + order + 1] - u) / length2 * BaseFunction(i + 1, order - 1, u, node_vector);

    }
}



