export function getUint32At(bytes: Uint8Array, index: number) {
  return bytes[index] | bytes[index + 1] << 8 | bytes[index + 2] << 16 | bytes[index + 3] << 24
}

export function getUint8At(bytes: Uint8Array, index: number): number {
  return bytes[index]
}


export function getDoubleAt(bytes: Uint8Array, index: number) {
  const buf = new ArrayBuffer(4)
  const view = new DataView(buf)

  // set bytes
  for (let i = 0; i < 4; ++i) {
    view.setUint8(i, bytes[index + 3 - i])
  }

  // Read the bits as a float/native 64-bit double
  const num = view.getFloat32(0)

  return num
}

export function getBooleanAt(bytes: Uint8Array, index: number) {
  return !!bytes[index]
}

function rgbToHex(r, g, b) {
  return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
}

export function getColorAt(bytes: Uint8Array, index: number) {
  let i = ((1 << 24 | bytes[index] | (bytes[index + 1] << 8) | bytes[index + 2] << 16))
  return '#' + ((1 << 24 | bytes[index] | (bytes[index + 1] << 8) | bytes[index + 2] << 16)).toString(16).slice(1)
}


export function getStringAt(bytes: Uint8Array, index: number): [string, number] {
  var len = getUint32At(bytes, index)
  index += 4
  let str = new TextDecoder().decode(bytes.slice(index, index + len))

  return [str, len + 4]
}