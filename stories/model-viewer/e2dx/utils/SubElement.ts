class Point {
    public x: number;
    public y: number;
    constructor(x: number, y: number) {
        this.x = x;
        this.y = y;
    }

    public ToString(): string {
        return "( " + this.x.toString() + " , " + this.y.toString() + ")\n";
    }
}

class ArcElement {
    /**
     * @center center of the arc (CAD metric)
     */
    public center: Point;

    /**
     * @Radius radius of the arc (CAD metric)
     */
    public Radius: number;

    /**
     * @start_angle start angle of the arc (CAD metric)
     */
    public start_angle: number;

    /**
   * @end_angle end angle of the arc (CAD metric)
   */
    public end_angle: number;

    constructor(center: Point, Radius: number, start_angle: number, end_angle: number) {
        this.center = center;
        this.Radius = Radius;
        this.start_angle = start_angle;
        this.end_angle = end_angle;
    }

    public display(): void {

    }
}


class Segment {
    /**
    * @width_begin 起始宽度
    */
    public width_begin: number;

    /**
    * @width_end 终止宽度
    */
    public width_end: number;

    /**
    * @bulge 凸度
    */
    public bulge: number;

    constructor(width_begin: number, width_end: number, bulge: number) {
        this.width_begin = width_begin;
        this.width_end = width_end;
        this.bulge = bulge;
    }

    public ToString(): string {
        return "begin width: " + this.width_begin.toString() + ";end width:" + this.width_end.toString()
            + ";bulge:" + this.bulge.toString();
    }
}

class DirVector {
    /**
     * @delta_x :x 分量 
     */
    public delta_x: number;
    /**
    * @delta_y :y 分量 
    */
    public delta_y: number;

    constructor(x: number, y: number) {
        this.delta_x = x;
        this.delta_y = y;

    }

    public ToString(): string {
        return "delta x:" + this.delta_x.toString() + ";delta y:" + this.delta_y.toString() + "\n";
    }

    /**
     * Calculate magnitude of this vector
     * @returns magnitude of this vector
     */
    public magnitude(): number {
        return Math.sqrt(Math.pow(this.delta_x, 2) + Math.pow(this.delta_y, 2));
    }

    /**
     * convert this vector to direction angle [-PI,-PI]
     * @returns angle in rad [-PI,-PI]
     */
    public toAngle(): number {
        return Math.atan2(this.delta_y, this.delta_x);
    }
}

export { Point, ArcElement, Segment, DirVector }