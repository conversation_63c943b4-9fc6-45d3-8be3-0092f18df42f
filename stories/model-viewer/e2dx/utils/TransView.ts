import { Point } from "../DrawingElements/Point" //这里根据实际项目导入Point类

export class TransViewer {
    private _cur_scr_org_layout: Point //当前屏幕原点（左上角点）对应layout空间的坐标
    private _scale: number  //累积缩放系数,一单位原始layout长度对应多少当前帧layout长度
    private _ratio;//一个单位的屏幕像素对应多少单位原始layout长度
    private _cur_scale_cen_layout: Point//当前缩放中心,layout空间
    private _cur_scale_cen_scr: Point;// 当前缩放中心，screen空间

    constructor(cur_scr_org_layout: Point, ratioo_scr_layout: number) {

        this._cur_scr_org_layout = cur_scr_org_layout;

        this._cur_scale_cen_layout = this._cur_scr_org_layout; //第一帧时，缩放中心定在左上角点
        this._cur_scale_cen_scr = new Point(0, 0);

        this._ratio = ratioo_scr_layout;//一个单位的屏幕像素对应多少单位layout长度
        this._scale = this._ratio;
    }

    //取得缩放
    public get_scale(): number {
        return this._scale;
    }

    //输入layout空间坐标值，得到相应屏幕空间坐标值。
    public from_layout_to_screen(p_layout: Point): Point {
        let x = (p_layout.x - this._cur_scale_cen_layout.x) / this._scale + this._cur_scale_cen_scr.x;
        let y = -1 * (p_layout.y - this._cur_scale_cen_layout.y) / this._scale + this._cur_scale_cen_scr.y;
        return new Point(x, y);
    }

    //输入屏幕空间坐标值，得到相应layout空间坐标值。
    public from_screen_to_layout(p_scr: Point): Point {
        let x = (-this._cur_scale_cen_scr.x + p_scr.x) * (this._scale) + this._cur_scale_cen_layout.x;
        let y = (this._cur_scale_cen_scr.y - p_scr.y) * (this._scale) + this._cur_scale_cen_layout.y;
        return new Point(x, y);
    }

    //平移操作
    public set_translate(move_x: number, move_y: number) {
        this._cur_scr_org_layout.x -= (move_x * this._scale);
        this._cur_scr_org_layout.y += (move_y * this._scale); //屏幕坐标轴向下，所以是+
        this._cur_scale_cen_layout = this._cur_scr_org_layout;
        this._cur_scale_cen_scr = new Point(0, 0);
    }

    //缩放操作
    public set_scale(scale_center_scr: Point, scale: number) {
        this._cur_scale_cen_layout = this.from_screen_to_layout(scale_center_scr);
        this._cur_scale_cen_scr = scale_center_scr;

        this._scale *= scale;

        this._cur_scr_org_layout.x = this._cur_scale_cen_layout.x - scale_center_scr.x * this._scale;
        this._cur_scr_org_layout.y = this._cur_scale_cen_layout.y + scale_center_scr.y * this._scale;
    }

}