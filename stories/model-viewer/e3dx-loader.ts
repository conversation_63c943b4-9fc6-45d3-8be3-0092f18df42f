// @ts-ignore
import Worker                                                               from './worker?worker'
import { DoubleSide, Group, Matrix4, Mesh, MeshStandardMaterial, Object3D } from 'three'
import {
  DRACOLoader
}                                                                           from 'three/examples/jsm/loaders/DRACOLoader'

const dracoPath = new URL('./draco/', import.meta.url).href

const dracoLoader = new DRACOLoader()
dracoLoader.setDecoderPath(dracoPath + '/')
dracoLoader.setDecoderConfig({ type: 'wasm' })

const getTransform = (transform: number[]) => {
  //@ts-ignore
  return new Matrix4().set(...Array.from({ length: 12 }, (_, i) => transform[Math.floor(i / 4) + (i % 4) * 3]), 0, 0, 0, 1)
}

export default class E3dxLoader {
  private _worker: Worker | undefined

  group = new Group()

  constructor() {
    this.group.scale.set(1, 1, 1)
  }

  private async _addMesh(item: any, g: Object3D) {
    let newG: Object3D
    if (item.face) {
      try {
        newG = await this._loadGeo(item.face, item.color)
        newG.name = item.name

        newG.visible = !item.hidden
        g.add(newG)
      } catch (e) {
        console.log(e)
        return
      }
    } else {
      newG = new Group()
    }

    if (item.transform) {
      newG.matrixAutoUpdate = true
      newG.applyMatrix4(getTransform(item.transform))
    }

    if (item.children) {
      item.children.forEach((obj: any) => {
        this._addMesh(obj, newG)
      })
    }
    g.add(newG)
  }

  async load(path: string): Promise<Group> {
    this._worker = new Worker()
    this._worker.postMessage(path)

    return new Promise((resolve, reject) => {
      this._worker.addEventListener('message', (e: any) => {
        const data = e.data
        const g = new Group()
        g.name = data.name
        this._addMesh(data, g)
        this.group.add(g)
        g.scale.set(0.001, 0.001, 0.001)
        resolve(g)
      })
    })
  }

  private _loadGeo(geo: any, color: string | undefined): Promise<Mesh> {
    return new Promise((resolve, reject) => {
      const blob = new Blob([geo], { type: 'application/octet-stream' })
      const url = URL.createObjectURL(blob)
      dracoLoader.load(url, (geometry) => {
        const material = new MeshStandardMaterial({
          color: color || 0xff00ff,
          metalness: 0.4,
          roughness: 0.2,
          side: DoubleSide
        })
        const mesh = new Mesh(geometry, material)
        resolve(mesh)

        URL.revokeObjectURL(url)
      }, undefined, function (error) {
        // console.error('An error happened:', error)
        reject(error)
      })
    })
  }
}
