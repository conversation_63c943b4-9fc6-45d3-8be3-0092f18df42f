import { css, html, LitElement, PropertyValues } from 'lit'
import { customElement, property, query }        from 'lit/decorators.js'
import Scene3D                                   from './scene'
import E3dxLoader                                from './e3dx-loader'
import { Group }                                 from 'three'

export interface Props {
  e3dx: string
}

@customElement('model-viewer')
export default class ModelViewer extends LitElement {

  scene = new Scene3D()

  private _loader = new E3dxLoader()

  @query('.container')
  container: HTMLDivElement | undefined

  constructor() {
    super()
  }

  protected firstUpdated(_changedProperties: PropertyValues) {
    super.firstUpdated(_changedProperties)
    if (!this.container) return
    this.scene.appendTo(this.container)

    if (this.e3dx) {
      this._loader.load(this.e3dx).then((g: Group) => {
        this.scene.add(g)
        setTimeout(() => {
          this.scene.fit()
        },100)
      })
    }
  }

  @property()
  e3dx = ''

  fit = () => {
    this.scene.fit()
  }

  protected render(): unknown {
    return html`
      <div class="container">
        <div class="actions">
          <button @click=${this.fit}>聚焦</button>
        </div>
      </div>`
  }

  static styles = css`
    :host {
      display: block;
      width: 100%;
      height: 100%;
    }

    canvas {
      width: 100%;
      height: 100%;
    }
    
    .actions {
      position: absolute;
      left: 0;
      top: 0;
    }
  `
}