import {
  ACESFilmicToneMapping,
  Box3,
  ********************************,
  Group,
  LinearFilter,
  LoadingManager,
  Material,
  Mesh,
  Object3D,
  PerspectiveCamera,
  Scene,
  ShaderChunk,
  Vector3,
  WebGLRenderer
}                          from 'three'
import { ArcballControls } from 'three/examples/jsm/controls/ArcballControls'
import { RGBELoader }      from 'three/examples/jsm/loaders/RGBELoader'
import TWEEN, { Tween }    from '@tweenjs/tween.js'

const hdr = new URL('./asserts/lightroom_14b.hdr', import.meta.url).href

export default class Scene3D {

  scene = new Scene()
  renderer = new WebGLRenderer({
    antialias: true,
    alpha: true,
    powerPreference: 'high-performance',
    preserveDrawingBuffer: true
  })

  camera = new PerspectiveCamera(
    60,
    window.innerWidth / window.innerHeight,
    0.1,
    10000)

  controls: ArcballControls
  private _resizeObserver: ResizeObserver | undefined

  private _container = new Group()

  private _fitAnimation: Tween<any> | undefined

  constructor() {
    this.controls = new ArcballControls(this.camera, this.renderer.domElement)
    this.controls.enablePan = false

    this.renderer.setPixelRatio(window.devicePixelRatio || 2)
    this.renderer.autoClear = false
    this.renderer.toneMapping = ACESFilmicToneMapping
    this.renderer.toneMappingExposure = 1.4

    ShaderChunk.tonemapping_pars_fragment =
      ShaderChunk.tonemapping_pars_fragment.replace(
        'vec3 CustomToneMapping( vec3 color ) { return color; }', `
      float startCompression = 0.8;
      float desaturation = 0.5;
      vec3 CustomToneMapping( vec3 color ) {
        color *= toneMappingExposure;
        
        float d = 1. - startCompression;

        float peak = max(color.r, max(color.g, color.b));
        if (peak < startCompression) return color;

        float newPeak = 1. - d * d / (peak + d - startCompression);
        float invPeak = 1. / peak;
        
        float extraBrightness = dot(color * (1. - startCompression * invPeak), vec3(1, 1, 1));
        
        color *= newPeak * invPeak;
        float g = 1. - 3. / (desaturation * extraBrightness + 3.);
        return mix(color, vec3(1, 1, 1), g);
      }`)

    this.camera.position.set(0, 0, 3)
    this.camera.lookAt(new Vector3())

    const loader = new RGBELoader(new LoadingManager())
    loader.load(hdr, (texture) => {
      texture.mapping = ********************************
      texture.minFilter = LinearFilter
      texture.magFilter = LinearFilter
      texture.needsUpdate = true
      this.scene.environment = texture
    }, () => {
    }, () => {
    })

    this.scene.add(this._container)
    this.ticker()
  }

  fit() {
    const box = new Box3()
    box.setFromObject(this._container)
    const center = new Vector3()
    box.getCenter(center)
    const cameraTarget = new Vector3(0, 0, 1)

    this._fitAnimation = new Tween(this.camera.position)
      .to(cameraTarget, 600)
      .easing(TWEEN.Easing.Quintic.InOut)
      .onStart(() => {
        this.controls.enabled = false
      })
      .onUpdate(({}, t) => {
        this.controls.update()
      })
      .onComplete(() => {
        this.camera.lookAt(0, 0, 0)
        this.controls.target.set(0, 0, 0)
        this.controls.enabled = true
      })
      .start()

    if (center.equals(new Vector3())) {
      return
    }
    this._container.children[0]?.position.copy(center.multiplyScalar(-1))
  }

  ticker = () => {
    TWEEN.update()
    this.renderer.clear()
    this.renderer.render(this.scene, this.camera)
    requestAnimationFrame(this.ticker)
    this.controls.update()
  }

  appendTo(parent: HTMLElement) {
    parent.append(this.renderer.domElement)

    this.resize()

    this._resizeObserver = new ResizeObserver(this.resize)
    this._resizeObserver.observe(parent)
  }

  destroy() {
    this._fitAnimation?.stop()
    const parent = this.renderer.domElement.parentElement
    if (!parent) return
    this._resizeObserver?.unobserve(parent)
    this.controls.dispose()
    this.renderer.dispose()
    this.scene.traverse((m: Object3D) => {
      if (m instanceof Mesh) {
        m.geometry.dispose()
        if (Array.isArray(m.material)) {
          m.material.forEach((m: Material) => m.dispose())
        } else if (m.material) {
          m.material.dispose()
        }
      }
      m.removeFromParent()
    })
  }

  resize = () => {
    const parent = this.renderer.domElement.parentElement
    if (!parent) return
    const width = parent.clientWidth
    const height = parent.clientHeight
    this.camera.aspect = width / height
    this.camera.updateProjectionMatrix()

    this.renderer.setSize(width, height)
  }

  add(node: Object3D) {
    this._container.add(node)
  }
}
