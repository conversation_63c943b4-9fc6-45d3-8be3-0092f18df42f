import {
  getBooleanAt,
  getColorAt,
  getDoubleAt,
  getStringAt,
  getUint32At,
  getUint8At
} from './e2dx/utils/GetNumber'

self.addEventListener('message', (e) => {
  const filePath = e.data
  console.log(filePath)
  let data: Uint8Array | null
  let byteArray: any[] = []
  let req = new XMLHttpRequest()
  req.addEventListener('loadend', () => {
    if (req.status != 200) return byteArray
    for (let i = 0; i < req.responseText.length; ++i) {
      byteArray.push(req.responseText.charCodeAt(i) & 0xff)
    }

    data = new Uint8Array(byteArray)
    parse(data)
    data = null
    // callback(this)
  })
  req.open('GET', filePath, true)
  req.overrideMimeType('text\/plain; charset=x-user-defined')
  req.send(null)
})

const check = (data: Uint8Array) => {
  return new TextDecoder().decode(data.slice(0, 4)) === 'E3DX'
}

const getVersion = (data: Uint8Array) => {
  return parseFloat(data[4].toString() + '.' + data[5].toString())
}
const getType = (data: Uint8Array) => {
  return getUint8At(data, 6)
}

const MODEL_TYPES: { [key: number]: string } = {
  1: 'STEP',
  4: 'SOLIDWORKS PART',
  8: 'X_T'
}
let index = 0
const parse = (data: Uint8Array) => {
  index = 0
  if (!check(data)) console.error('this file is not an e3dx file.')
  index += 4
  const version = getVersion(data)
  console.log('version is %s', version)
  index += 2
  console.log('type is %s', getType(data))
  index += 1
  if (version > 10) {
    const configCount = getUint8At(data, 7)
    index += 1
    console.log('configCount:', configCount)
    const originType = getUint32At(data, 8)
    index += 4
    console.log('origin format:', originType, MODEL_TYPES[originType])
    if (configCount === 0) {
      const [jsonStr, length] = getStringAt(data, 12)
      index = 12 + length + getPaddingSize(length - 4)
      let json = JSON.parse(jsonStr as string)
      checkGeoItem(data, json)
      self.postMessage(json)
      json = null
    }
  }
}

const checkGeoItem = (data: Uint8Array, json: any) => {
  // while ()
  if (json.byteLength) {
    getChunk(data, json)
  }
  if (json.children) {
    json.children.forEach((item: any) => {
      checkGeoItem(data, item)
    })
  }
}

const getChunk = (data: Uint8Array, info: {
  byteLength: number
  name: string
  transform: any
  face: any
  hidden: boolean
  color: string | undefined
  offset: number
}) => {
  let {
    byteLength, offset, name, transform
  } = info
  offset += index
  let localIndex = 0
  const faceChunkLength = getUint32At(data, offset + localIndex)
  localIndex += 4
  const edgeChunkLength = getUint32At(data, offset + localIndex)
  localIndex += 4
  const geoArrayCount = getUint32At(data, offset + localIndex)
  localIndex += 4
  if (faceChunkLength > byteLength) return
  const hidden = getBooleanAt(data, offset + localIndex)
  localIndex += 1
  const hasColor = getBooleanAt(data, offset + localIndex)
  localIndex += 1
  localIndex += 2
  const color = hasColor ? getColorAt(data, offset + localIndex) : undefined
  if (hasColor) {
    localIndex += 4
  }
  const part = getDoubleAt(data, offset + localIndex)
  localIndex += 4

  localIndex += 4
  localIndex += 4

  const faceChunk = data.slice(offset + localIndex, offset + localIndex + faceChunkLength)
  info.face = faceChunk
  info.color = color
  info.hidden = hidden
}

function getPaddingSize(size: number, align = 4) {
  if (!align) {
    return 0
  }
  return (align - size % align) % align
}



