# 机器人配置器约束系统设计文档

## 概述

本文档描述了全新设计的机器人配置器约束系统，该系统支持复杂的功能到物料的约束关系，包括4种不同的约束模式和详细的槽位放置规范。

## 核心设计理念

### 问题分析
原有系统的功能约束过于简单，无法满足实际业务需求。现实中功能对物料的约束存在多种复杂情况：

1. **只有类别约束** - 功能只关心物料类型，不关心具体款式
2. **具体到特定款式** - 功能只能使用经过验证的特定物料
3. **类别+黑名单** - 允许某类物料，但排除特定不兼容的款式
4. **数量+位置要求** - 需要多个物料且必须安装在指定位置

### 解决方案
设计了基于 `MaterialRequirement` 的新约束系统，每个功能可以定义多个物料要求，每个要求包含：
- 约束类型和物料过滤规则
- 精确的数量要求
- 详细的槽位放置规范

## 数据结构设计

### MaterialRequirement
```typescript
export interface MaterialRequirement {
  id: string              // 要求的唯一标识
  name: string            // 要求名称
  description: string     // 详细描述
  constraintType: MaterialConstraintType  // 约束类型
  quantity: number        // 需要的数量
  placements: PlacementRule[]  // 放置规则
  
  // 根据约束类型使用不同字段
  allowedMaterialTypes?: MaterialType[]    // 类型1&3&4
  specificMaterialIds?: string[]          // 类型2
  blacklistMaterialIds?: string[]         // 类型3
}
```

### MaterialConstraintType
```typescript
export enum MaterialConstraintType {
  CATEGORY_ONLY = 'category_only',           // 只有类别限制
  SPECIFIC_MATERIALS = 'specific_materials', // 具体到特定物料
  CATEGORY_WITH_BLACKLIST = 'category_with_blacklist', // 类别+黑名单
  FLEXIBLE = 'flexible'                      // 灵活约束
}
```

### PlacementRule
```typescript
export interface PlacementRule {
  slotId: string          // 目标槽位ID
  priority: number        // 优先级（1-10）
  required: boolean       // 是否必须放置在此槽位
  description: string     // 放置描述
  conditions?: PlacementCondition[]  // 放置条件
}
```

## 4种约束模式详解

### 1. 类别约束 (CATEGORY_ONLY)
**适用场景**: 功能只关心物料类型，对具体款式无要求
```typescript
{
  constraintType: MaterialConstraintType.CATEGORY_ONLY,
  allowedMaterialTypes: [MaterialType.SENSOR],
  quantity: 1,
  placements: [{
    slotId: 'navigation_sensor',
    required: true,
    description: '必须安装在前方导航传感器槽位'
  }]
}
```

### 2. 特定物料约束 (SPECIFIC_MATERIALS)
**适用场景**: 功能只能使用经过测试验证的特定物料款式
```typescript
{
  constraintType: MaterialConstraintType.SPECIFIC_MATERIALS,
  specificMaterialIds: ['lidar_sick_lms111', 'camera_realsense_d435'],
  quantity: 1,
  placements: [{
    slotId: 'rear_sensor_slot',
    required: true,
    description: '必须安装在后部传感器槽位，用于后方避障'
  }]
}
```

### 3. 类别+黑名单约束 (CATEGORY_WITH_BLACKLIST)
**适用场景**: 允许某类物料，但排除特定不兼容的款式
```typescript
{
  constraintType: MaterialConstraintType.CATEGORY_WITH_BLACKLIST,
  allowedMaterialTypes: [MaterialType.SENSOR],
  blacklistMaterialIds: ['camera_realsense_d435'], // 相机不适合精确定位
  quantity: 2,
  placements: [
    {
      slotId: 'left_lidar_slot',
      required: true,
      description: '第1个传感器必须安装在左侧槽位'
    },
    {
      slotId: 'right_lidar_slot',
      required: true,
      description: '第2个传感器必须安装在右侧槽位'
    }
  ]
}
```

### 4. 灵活约束 (FLEXIBLE)
**适用场景**: 需要多个物料，可灵活分布在不同位置
```typescript
{
  constraintType: MaterialConstraintType.FLEXIBLE,
  allowedMaterialTypes: [MaterialType.SENSOR],
  quantity: 3,
  placements: [
    {
      slotId: 'navigation_sensor',
      priority: 9,
      required: false,
      description: '前方主传感器位置（推荐）'
    },
    {
      slotId: 'left_lidar_slot',
      priority: 8,
      required: false,
      description: '左侧传感器位置（推荐）'
    },
    // ... 更多可选位置
  ]
}
```

## 放置规则系统

### 优先级机制
- 1-10数字评分，数字越大优先级越高
- 系统按优先级为用户推荐最佳放置方案
- 支持 `required: true` 强制要求特定位置

### 条件依赖
```typescript
export interface PlacementCondition {
  type: 'depends_on' | 'conflicts_with' | 'requires_empty'
  targetSlotId?: string     // 依赖/冲突的槽位
  targetMaterialId?: string // 依赖/冲突的物料
  description: string
}
```

## 验证算法

### 约束检查流程
1. **数量验证** - 检查当前满足要求的物料数量
2. **类型过滤** - 根据约束类型过滤有效物料
3. **黑名单检查** - 排除不兼容的物料
4. **放置验证** - 检查必需位置是否满足
5. **建议生成** - 为违反约束生成修复建议

### 智能建议算法
- 按槽位优先级排序推荐方案
- 自动选择最适合的物料款式
- 生成具体的放置指导说明

## 演示场景

### 场景1：自主导航 - 类别约束
只需要传感器和控制器，不限制具体型号

### 场景2：避障功能 - 特定物料约束
只允许使用经过验证的特定传感器型号

### 场景3：高精度定位 - 类别+黑名单约束
需要2个传感器分别安装在左右两侧，但不能使用相机

### 场景4：环境监测 - 灵活约束
需要3个传感器，可灵活分布在4个可选位置中的任意3个

## 技术优势

1. **完整覆盖业务场景** - 支持所有4种约束模式
2. **精确位置控制** - 详细的槽位放置规范
3. **智能建议系统** - 自动生成最优配置方案
4. **扩展性强** - 易于添加新的约束类型和条件
5. **用户友好** - 提供清晰的约束说明和修复建议

## 使用示例

用户选择"高精度定位"功能后，系统会：
1. 检查是否有2个传感器满足类型要求
2. 验证所选传感器不在黑名单中
3. 确认左右槽位都有合适的传感器
4. 如有缺失，自动推荐合适的物料和安装位置

这种设计完全满足了您提到的所有业务需求，实现了功能到物料的精确约束控制！