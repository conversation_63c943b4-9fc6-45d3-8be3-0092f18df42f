# 机器人配置器 Demo

这是一个展示复杂业务约束求解的机器人配置系统演示。

## 🎯 业务场景

针对机器人制造商的产品配置需求，系统需要处理以下约束类型：

1. **物料依赖约束** - 主物料带子物料（如激光雷达需要配套线束和电源）
2. **物料类型约束** - 按槽位限制材料类型和数量（如主控制器槽位只能选控制器）  
3. **功能约束** - 功能实现需要特定物料组合（如导航功能需要传感器+控制器）

## 🏗️ 技术架构

### 核心组件

- **ConstraintSolver** - 约束求解引擎，基于CSP算法
- **RobotConfigurator** - 主配置器UI组件
- **类型系统** - 完整的TypeScript类型定义

### 算法特点

- **增量验证** - 每次选择后立即验证，无需全量计算
- **约束传播** - 自动分析依赖关系并提供建议
- **智能推荐** - 基于约束分析的优化建议

## 🚀 快速开始

### 在Storybook中查看

1. 启动开发服务器：
```bash
npm run dev
```

2. 在浏览器中访问：http://localhost:6006

3. 导航到 `Business > RobotConfigurator`

### 代码使用

```typescript
import { RobotConfigurator, demoRobotModel } from './robot-configurator'

// 基本使用
<robot-configurator .model=${demoRobotModel}></robot-configurator>
```

## 🧪 测试场景

### 1. 物料依赖约束测试

**操作**：选择 "SICK LMS111激光雷达"

**预期结果**：
- 系统提示需要配套的"以太网线5m"和"24V电源适配器"
- 右侧验证面板显示约束违反信息
- 提供自动修复建议

### 2. 功能约束测试

**操作**：勾选 "自主导航" 功能

**预期结果**：
- 系统分析当前配置是否满足导航功能需求
- 如果缺少必要物料，提示需要传感器和控制器
- 推荐具体的实现方案

### 3. 类型约束测试

**操作**：在"主控制器"槽位选择不同控制器

**预期结果**：
- 只能选择控制器类型的物料
- 数量限制为1个
- 不同控制器影响成本和兼容性

### 4. 成本优化测试

**操作**：对比不同配置方案

**预期结果**：
- 实时显示总成本
- 成本明细分解
- 优化建议（如有）

## 📊 数据结构示例

### 机器人型号定义

```typescript
const robotModel: RobotModel = {
  id: 'agv_standard_v1',
  name: 'AGV标准版 V1.0',
  basePrice: 15000,
  slots: [...], // 配置槽位
  materials: [...], // 可用物料
  constraintRules: [...] // 约束规则
}
```

### 约束规则示例

```typescript
// 物料依赖约束
{
  id: 'lidar_dependencies',
  type: ConstraintType.MATERIAL_DEPENDENCY,
  params: {
    primaryMaterialId: 'lidar_sick_lms111',
    requiredMaterials: [
      { materialId: 'cable_ethernet_5m', name: '以太网线5m' },
      { materialId: 'power_adapter_24v', name: '24V电源适配器' }
    ]
  }
}

// 功能约束
{
  id: 'navigation_function_constraint',
  type: ConstraintType.FUNCTIONAL,
  params: {
    functionId: 'navigation',
    requiredMaterialTypes: [MaterialType.SENSOR, MaterialType.CONTROLLER]
  }
}
```

## 🔧 扩展开发

### 添加新的约束类型

1. 在 `types.ts` 中定义新的 `ConstraintType`
2. 在 `ConstraintSolver` 中实现对应的检查方法
3. 在测试数据中添加约束规则

### 添加新的物料

1. 在 `demo-data.ts` 中的 `testMaterials` 数组添加物料定义
2. 设置物料的 `subMaterials` 和 `compatibilityRules`
3. 在相应的约束规则中引用新物料

### 自定义UI组件

```typescript
@customElement('custom-material-card')
export class CustomMaterialCard extends LitElement {
  @property() material!: Material
  @property() selected: boolean = false
  
  render() {
    return html`
      <!-- 自定义物料卡片UI -->
    `
  }
}
```

## 📈 性能优化

- **增量计算**：只计算变更部分的约束
- **缓存机制**：缓存约束计算结果
- **虚拟滚动**：大量物料时的UI优化
- **Web Worker**：复杂约束计算的后台处理

## 🎨 UI特性

- **响应式设计**：适配不同屏幕尺寸
- **实时反馈**：即时的约束验证和成本计算
- **智能提示**：约束违反时的自动修复建议
- **可视化**：直观的配置状态和进度显示

## 🔮 扩展方向

1. **3D可视化**：集成Three.js显示配置结果
2. **AI推荐**：机器学习驱动的智能推荐
3. **多目标优化**：成本、性能、可靠性的平衡优化
4. **协同配置**：多用户协同配置功能
5. **版本管理**：配置方案的版本控制和对比

## 📝 代码结构

```
robot-configurator/
├── types.ts              # 核心类型定义
├── constraint-solver.ts  # 约束求解引擎
├── components.ts         # UI组件实现
├── demo-data.ts          # 测试数据
├── index.stories.ts      # Storybook故事
├── index.ts              # 模块导出
└── README.md             # 文档说明
```

这个demo展示了如何在前端实现复杂的业务约束求解，为类似的产品配置器、BOM管理等场景提供了完整的解决方案参考。