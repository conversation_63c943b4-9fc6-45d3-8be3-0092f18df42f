import { LitElement, html, css, PropertyValues } from 'lit'
import { customElement, property, state } from 'lit/decorators.js'
import {
  RobotModel,
  Configuration,
  Material,
  ConfigurationSlot,
  Selection,
  ValidationResult,
  RobotFunction,
  MaterialType,
  Suggestion,
  ConstraintViolation,
  MaterialConstraintType,
  MaterialRequirement
} from './types'
import { ConstraintSolver } from './constraint-solver'

@customElement('robot-configurator')
export class RobotConfigurator extends LitElement {
  @property({ type: Object }) model!: RobotModel
  @state() private currentConfiguration: Configuration = {
    id: 'config_1',
    name: '我的配置',
    robotModelId: '',
    selections: {},
    functions: []
  }
  @state() private validationResult: ValidationResult = {
    isValid: true,
    violations: [],
    warnings: [],
    suggestions: []
  }
  @state() private constraintSolver?: ConstraintSolver

  static styles = css`
    :host {
      display: block;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f5f5;
      min-height: 100vh;
    }

    .configurator-container {
      display: grid;
      grid-template-columns: 300px 1fr 350px;
      gap: 20px;
      padding: 20px;
      max-width: 1400px;
      margin: 0 auto;
      height: 100vh;
      box-sizing: border-box;
    }

    .panel {
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow-y: auto;
    }

    .panel h2 {
      margin: 0 0 20px 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
      border-bottom: 2px solid #eee;
      padding-bottom: 10px;
    }

    .function-selector {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .function-card {
      border: 2px solid #e1e5e9;
      border-radius: 8px;
      padding: 12px;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .function-card:hover {
      border-color: #007acc;
      box-shadow: 0 2px 8px rgba(0,122,204,0.15);
    }

    .function-card.selected {
      border-color: #007acc;
      background: #f0f8ff;
    }

    .function-name {
      font-weight: 600;
      color: #333;
      margin-bottom: 4px;
    }

    .function-description {
      font-size: 12px;
      color: #666;
      line-height: 1.4;
    }

    .configuration-area {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .slot-section {
      background: #fafbfc;
      border-radius: 8px;
      padding: 16px;
      border: 1px solid #e1e5e9;
    }

    .slot-header {
      display: flex;
      justify-content: between;
      align-items: center;
      margin-bottom: 16px;
    }

    .slot-name {
      font-weight: 600;
      color: #333;
    }

    .slot-required {
      font-size: 11px;
      background: #ff4757;
      color: white;
      padding: 2px 6px;
      border-radius: 4px;
    }

    .slot-optional {
      font-size: 11px;
      background: #57606f;
      color: white;
      padding: 2px 6px;
      border-radius: 4px;
    }

    .material-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 12px;
    }

    .material-card {
      border: 1px solid #e1e5e9;
      border-radius: 6px;
      padding: 12px;
      cursor: pointer;
      transition: all 0.2s ease;
      background: white;
    }

    .material-card:hover {
      border-color: #007acc;
      box-shadow: 0 2px 6px rgba(0,122,204,0.15);
    }

    .material-card.selected {
      border-color: #007acc;
      background: #f0f8ff;
    }

    .material-card.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      background: #f8f9fa;
    }

    .material-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 8px;
    }

    .material-name {
      font-weight: 500;
      color: #333;
      font-size: 14px;
    }

    .material-price {
      font-size: 12px;
      color: #007acc;
      font-weight: 600;
    }

    .material-description {
      font-size: 12px;
      color: #666;
      line-height: 1.4;
      margin-bottom: 8px;
    }

    .material-specs {
      font-size: 11px;
      color: #57606f;
    }

    .quantity-selector {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 8px;
    }

    .quantity-input {
      width: 60px;
      padding: 4px 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 12px;
    }

    .validation-panel {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .validation-summary {
      padding: 12px;
      border-radius: 6px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .validation-summary.valid {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .validation-summary.invalid {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .violations-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .violation-item {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 4px;
      padding: 10px;
      font-size: 13px;
    }

    .violation-item.error {
      background: #f8d7da;
      border-color: #f5c6cb;
      color: #721c24;
    }

    .suggestions-list {
      display: flex;
      flex-direction: column;
      gap: 6px;
    }

    .suggestion-item {
      background: #e7f3ff;
      border: 1px solid #bee5eb;
      border-radius: 4px;
      padding: 8px;
      font-size: 12px;
      cursor: pointer;
      transition: background 0.2s ease;
    }

    .suggestion-item:hover {
      background: #d1ecf1;
    }

    .cost-summary {
      background: #f8f9fa;
      border-radius: 6px;
      padding: 12px;
      border: 1px solid #dee2e6;
    }

    .cost-total {
      font-size: 16px;
      font-weight: 600;
      color: #007acc;
      text-align: center;
      margin-bottom: 8px;
    }

    .cost-breakdown {
      font-size: 11px;
      color: #666;
    }

    .selected-materials {
      display: flex;
      flex-direction: column;
      gap: 6px;
      margin-top: 12px;
    }

    .selected-material {
      background: #e8f5e8;
      border: 1px solid #c3e6cb;
      border-radius: 4px;
      padding: 6px 8px;
      font-size: 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .remove-btn {
      background: #dc3545;
      color: white;
      border: none;
      border-radius: 3px;
      padding: 2px 6px;
      font-size: 10px;
      cursor: pointer;
    }

    .btn {
      background: #007acc;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 6px 12px;
      font-size: 12px;
      cursor: pointer;
      transition: background 0.2s ease;
    }

    .btn:hover {
      background: #005999;
    }

    .btn:disabled {
      background: #ccc;
      cursor: not-allowed;
    }
  `

  updated(changedProperties: PropertyValues) {
    if (changedProperties.has('model') && this.model) {
      this.constraintSolver = new ConstraintSolver(this.model)
      this.currentConfiguration.robotModelId = this.model.id
      this.validateConfiguration()
    }
  }

  render() {
    if (!this.model) {
      return html`<div class="configurator-container">
        <div class="panel">加载中...</div>
      </div>`
    }

    return html`
      <div class="configurator-container">
        <!-- 左侧：功能选择 -->
        <div class="panel">
          <h2>功能需求</h2>
          <div class="function-selector">
            ${this.model.availableFunctions.map(func => html`
              <div 
                class="function-card ${this.currentConfiguration.functions.includes(func.id) ? 'selected' : ''}"
                @click=${() => this.toggleFunction(func.id)}
              >
                <div class="function-name">${func.name}</div>
                <div class="function-description">${func.description}</div>
              </div>
            `)}
          </div>
        </div>

        <!-- 中间：配置区域 -->
        <div class="panel">
          <h2>${this.model.name} - 物料配置</h2>
          <div class="configuration-area">
            ${this.model.slots.map(slot => this.renderSlot(slot))}
          </div>
        </div>

        <!-- 右侧：验证和汇总 -->
        <div class="panel">
          <h2>配置验证</h2>
          <div class="validation-panel">
            ${this.renderValidationSummary()}
            ${this.renderViolations()}
            ${this.renderSuggestions()}
            ${this.renderCostSummary()}
          </div>
        </div>
      </div>
    `
  }

  private renderSlot(slot: ConfigurationSlot) {
    if (!this.constraintSolver) {
      return html`<div class="slot-section">加载中...</div>`
    }
    
    const availableOptions = this.constraintSolver.getAvailableOptions(slot.id, this.currentConfiguration)
    const currentSelections = this.currentConfiguration.selections[slot.id] || []

    return html`
      <div class="slot-section">
        <div class="slot-header">
          <span class="slot-name">${slot.name}</span>
          <span class="${slot.required ? 'slot-required' : 'slot-optional'}">
            ${slot.required ? '必选' : '可选'}
          </span>
        </div>

        <!-- 当前选择的材料 -->
        ${currentSelections.length > 0 ? html`
          <div class="selected-materials">
            ${currentSelections.map(selection => {
              const material = this.model.materials.find(m => m.id === selection.materialId)
              return html`
                <div class="selected-material">
                  <span>${material?.name}</span>
                  <button 
                    class="remove-btn"
                    @click=${() => this.removeSelection(slot.id, selection.materialId)}
                  >移除</button>
                </div>
              `
            })}
          </div>
        ` : ''}

        <!-- 可用材料选项 -->
        <div class="material-grid">
          ${availableOptions.map(material => {
            const isSelected = currentSelections.some(sel => sel.materialId === material.id)
            return html`
              <div 
                class="material-card ${isSelected ? 'selected' : ''}"
                @click=${() => this.selectMaterial(slot.id, material)}
              >
                <div class="material-header">
                  <div class="material-name">${material.name}</div>
                  <div class="material-price">¥${material.price}</div>
                </div>
                <div class="material-description">${material.description}</div>
                <div class="material-specs">
                  ${Object.entries(material.specifications).slice(0, 2).map(([key, value]) => 
                    `${key}: ${value}`
                  ).join(' | ')}
                </div>
                ${isSelected ? html`
                  <div class="selected-indicator">
                    <span style="color: #28a745; font-weight: 500;">✓ 已选择</span>
                  </div>
                ` : ''}
              </div>
            `
          })}
        </div>
      </div>
    `
  }

  private renderValidationSummary() {
    return html`
      <div class="validation-summary ${this.validationResult.isValid ? 'valid' : 'invalid'}">
        <span>${this.validationResult.isValid ? '✅' : '⚠️'}</span>
        <span>${this.validationResult.isValid ? '配置有效' : '配置需要调整'}</span>
      </div>
    `
  }

  private renderViolations() {
    if (this.validationResult.violations.length === 0) return ''

    return html`
      <div>
        <h3 style="margin: 0 0 8px 0; font-size: 14px; color: #333;">需要解决的问题</h3>
        <div class="violations-list">
          ${this.validationResult.violations.map(violation => html`
            <div class="violation-item ${violation.severity}">
              ${violation.message}
              ${violation.autoFixable ? html`
                <button 
                  class="btn" 
                  style="margin-top: 6px; font-size: 10px;"
                  @click=${() => this.autoFixViolation(violation)}
                >自动修复</button>
              ` : ''}
            </div>
          `)}
        </div>
      </div>
    `
  }

  private renderSuggestions() {
    if (this.validationResult.suggestions.length === 0) return ''

    return html`
      <div>
        <h3 style="margin: 0 0 8px 0; font-size: 14px; color: #333;">优化建议</h3>
        <div class="suggestions-list">
          ${this.validationResult.suggestions.map(suggestion => html`
            <div 
              class="suggestion-item"
              @click=${() => this.applySuggestion(suggestion)}
            >
              ${suggestion.reason}
            </div>
          `)}
        </div>
      </div>
    `
  }

  private renderCostSummary() {
    const totalCost = this.calculateTotalCost()
    
    return html`
      <div class="cost-summary">
        <div class="cost-total">总计: ¥${totalCost.toLocaleString()}</div>
        <div class="cost-breakdown">
          基础价格: ¥${this.model.basePrice.toLocaleString()}<br/>
          物料成本: ¥${(totalCost - this.model.basePrice).toLocaleString()}
        </div>
      </div>
    `
  }

  private toggleFunction(functionId: string) {
    if (this.currentConfiguration.functions.includes(functionId)) {
      this.currentConfiguration.functions = this.currentConfiguration.functions.filter(id => id !== functionId)
    } else {
      this.currentConfiguration.functions = [...this.currentConfiguration.functions, functionId]
    }
    this.validateConfiguration()
    this.requestUpdate()
  }

  private selectMaterial(slotId: string, material: Material) {
    const currentSelections = this.currentConfiguration.selections[slotId] || []
    const existingSelection = currentSelections.find(sel => sel.materialId === material.id)
    
    if (!existingSelection) {
      const newSelection: Selection = {
        materialId: material.id,
        slotId: slotId
      }
      
      // 每个槽只能安装一个物料，直接替换
      this.currentConfiguration.selections[slotId] = [newSelection]
      this.validateConfiguration()
      this.requestUpdate()
    }
  }

  private removeSelection(slotId: string, materialId: string) {
    if (this.currentConfiguration.selections[slotId]) {
      this.currentConfiguration.selections[slotId] = this.currentConfiguration.selections[slotId].filter(
        sel => sel.materialId !== materialId
      )
      this.validateConfiguration()
      this.requestUpdate()
    }
  }

  private applySuggestion(suggestion: Suggestion) {
    if (!suggestion.materialId) return
    
    const material = this.model.materials.find(m => m.id === suggestion.materialId)
    if (!material) return

    // 实现建议应用逻辑
    if (suggestion.type === 'add_material') {
      // 找到合适的槽位
      let targetSlotId = suggestion.slotId
      
      if (!targetSlotId) {
        // 自动找到第一个可以容纳该材料的槽位
        const compatibleSlot = this.model.slots.find(slot => 
          slot.allowedMaterialTypes.includes(material.type) ||
          slot.allowedMaterials.includes(material.id) ||
          (slot.allowedMaterialTypes.length === 0 && slot.allowedMaterials.length === 0)
        )
        if (compatibleSlot) {
          targetSlotId = compatibleSlot.id
        }
      }
      
      if (targetSlotId) {
        this.selectMaterial(targetSlotId, material)
      }
    } else if (suggestion.type === 'remove_material' && suggestion.slotId) {
      this.removeSelection(suggestion.slotId, suggestion.materialId)
    }
  }

  private autoFixViolation(violation: ConstraintViolation) {
    // 找到与此约束违反相关的建议
    const relatedSuggestions = this.validationResult.suggestions.filter(
      suggestion => suggestion.reason.includes('必需组件') || suggestion.priority >= 8
    )
    
    // 应用所有相关建议
    relatedSuggestions.forEach(suggestion => {
      this.applySuggestion(suggestion)
    })
    
    // 如果没有找到建议，尝试从约束规则中生成建议
    if (relatedSuggestions.length === 0) {
      this.generateAutoFixSuggestions(violation)
    }
  }

  private generateAutoFixSuggestions(violation: ConstraintViolation) {
    // 根据约束违反生成自动修复建议
    const constraint = this.model.constraintRules.find(c => c.id === violation.constraintId)
    if (!constraint) return

    if (constraint.type === 'material_dependency') {
      const { requiredMaterials } = constraint.params
      requiredMaterials?.forEach((reqMat: any) => {
        const suggestion: Suggestion = {
          type: 'add_material',
          materialId: reqMat.materialId,
          reason: '自动修复依赖约束',
          priority: 10
        }
        this.applySuggestion(suggestion)
      })
    }
  }

  private validateConfiguration() {
    if (this.constraintSolver) {
      this.validationResult = this.constraintSolver.validate(this.currentConfiguration)
    }
  }

  private calculateTotalCost(): number {
    if (!this.model) return 0
    let totalCost = this.model.basePrice
    
    Object.values(this.currentConfiguration.selections).flat().forEach(selection => {
      const material = this.model.materials.find(m => m.id === selection.materialId)
      if (material) {
        totalCost += material.price // 每个槽只能安装一个物料，所以数量固定为1
      }
    })
    
    return totalCost
  }
}