import {
  Configuration,
  ConstraintRule,
  ConstraintType,
  ValidationResult,
  ConstraintViolation,
  Suggestion,
  Material,
  RobotModel,
  Selection,
  MaterialType,
  MaterialConstraintType,
  MaterialRequirement,
  ConstraintWarning
} from './types'

export class ConstraintSolver {
  private model: RobotModel
  private constraints: Map<string, ConstraintRule>

  constructor(model: RobotModel) {
    this.model = model
    this.constraints = new Map()
    model.constraintRules.forEach(rule => {
      this.constraints.set(rule.id, rule)
    })
  }

  // 验证当前配置
  validate(configuration: Configuration): ValidationResult {
    const violations: ConstraintViolation[] = []
    const suggestions: Suggestion[] = []
    const warnings: ConstraintWarning[] = []

    // 检查传统约束规则
    for (const constraint of this.constraints.values()) {
      const result = this.checkConstraint(constraint, configuration)
      if (result.violations) {
        violations.push(...result.violations)
      }
      if (result.suggestions) {
        suggestions.push(...result.suggestions)
      }
    }

    // 检查功能的MaterialRequirement约束
    configuration.functions.forEach(functionId => {
      const functionResult = this.validateFunctionRequirements(functionId, configuration)
      if (functionResult.violations) {
        violations.push(...functionResult.violations)
      }
      if (functionResult.suggestions) {
        suggestions.push(...functionResult.suggestions)
      }
      if (functionResult.warnings) {
        warnings.push(...functionResult.warnings)
      }
    })

    return {
      isValid: violations.filter(v => v.severity === 'error').length === 0,
      violations,
      warnings,
      suggestions
    }
  }

  // 增量验证 - 用户每次选择后立即验证
  validateIncremental(
    currentConfig: Configuration, 
    newSelection: Selection
  ): ValidationResult {
    // 创建临时配置
    const tempConfig = this.applySelection(currentConfig, newSelection)
    return this.validate(tempConfig)
  }

  // 获取槽位的可用选项
  getAvailableOptions(slotId: string, currentConfig: Configuration): Material[] {
    const slot = this.model.slots.find(s => s.id === slotId)
    if (!slot) return []

    let availableMaterials = this.model.materials.filter(material => {
      // 检查材料类型约束
      if (slot.allowedMaterialTypes.length > 0) {
        return slot.allowedMaterialTypes.includes(material.type)
      }
      
      // 检查具体材料约束
      if (slot.allowedMaterials.length > 0) {
        return slot.allowedMaterials.includes(material.id)
      }
      
      return true
    })

    // 暂时不过滤掉会导致约束违反的材料，让用户能够选择
    // 用户选择后会在验证中显示约束违反和修复建议
    // availableMaterials = availableMaterials.filter(material => {
    //   const testSelection: Selection = {
    //     materialId: material.id,
    //     quantity: 1,
    //     slotId: slotId
    //   }
    //   
    //   const validationResult = this.validateIncremental(currentConfig, testSelection)
    //   return validationResult.violations.filter(v => v.severity === 'error').length === 0
    // })

    return availableMaterials
  }

  // 智能推荐
  getRecommendations(configuration: Configuration): Suggestion[] {
    const suggestions: Suggestion[] = []

    // 基于功能需求推荐材料
    for (const functionId of configuration.functions) {
      const func = this.model.availableFunctions.find(f => f.id === functionId)
      if (func) {
        const recommendations = this.recommendMaterialsForFunction(func, configuration)
        suggestions.push(...recommendations)
      }
    }

    // 基于成本优化推荐
    const costOptimizations = this.getCostOptimizations(configuration)
    suggestions.push(...costOptimizations)

    return suggestions.sort((a, b) => b.priority - a.priority)
  }

  private checkConstraint(
    constraint: ConstraintRule, 
    configuration: Configuration
  ): { violations?: ConstraintViolation[], suggestions?: Suggestion[] } {
    switch (constraint.type) {
      case ConstraintType.MATERIAL_DEPENDENCY:
        return this.checkMaterialDependency(constraint, configuration)
      
      case ConstraintType.MATERIAL_TYPE:
        return this.checkMaterialType(constraint, configuration)
      
      case ConstraintType.FUNCTIONAL:
        return this.checkFunctionalConstraint(constraint, configuration)
      
      case ConstraintType.MATERIAL_QUANTITY:
        return this.checkMaterialQuantityConstraint(constraint, configuration)
      
      case ConstraintType.MUTUAL_EXCLUSIVE:
        return this.checkMutualExclusive(constraint, configuration)
      
      case ConstraintType.SPECIFIC_PLACEMENT:
        return this.checkSpecificPlacementConstraint(constraint, configuration)
      
      default:
        return {}
    }
  }

  // 新增：检查功能的MaterialRequirement约束
  validateFunctionRequirements(functionId: string, configuration: Configuration): ValidationResult {
    const func = this.model.availableFunctions.find(f => f.id === functionId)
    if (!func || !configuration.functions.includes(functionId)) {
      return { isValid: true, violations: [], warnings: [], suggestions: [] }
    }

    const violations: ConstraintViolation[] = []
    const suggestions: Suggestion[] = []
    const warnings: ConstraintWarning[] = []

    // 检查每个物料要求
    func.materialRequirements?.forEach(requirement => {
      const reqResult = this.checkMaterialRequirement(requirement, configuration)
      if (reqResult.violations) violations.push(...reqResult.violations)
      if (reqResult.suggestions) suggestions.push(...reqResult.suggestions)
      if (reqResult.warnings) warnings.push(...reqResult.warnings)
    })

    return {
      isValid: violations.filter(v => v.severity === 'error').length === 0,
      violations,
      warnings,
      suggestions
    }
  }

  private checkMaterialRequirement(
    requirement: MaterialRequirement,
    configuration: Configuration
  ): { violations?: ConstraintViolation[], suggestions?: Suggestion[], warnings?: ConstraintWarning[] } {
    const violations: ConstraintViolation[] = []
    const suggestions: Suggestion[] = []
    const warnings: ConstraintWarning[] = []

    // 获取当前满足要求的物料选择
    const satisfyingSelections = this.findSatisfyingSelections(requirement, configuration)
    const currentCount = satisfyingSelections.length

    // 检查数量是否满足
    if (currentCount < requirement.quantity) {
      const missing = requirement.quantity - currentCount
      violations.push({
        constraintId: `requirement_${requirement.id}`,
        message: `${requirement.name}需要${requirement.quantity}个，当前只有${currentCount}个，还需要${missing}个`,
        severity: 'error',
        affectedSlots: requirement.placements.filter(p => p.required).map(p => p.slotId),
        autoFixable: true
      })

      // 生成建议
      this.generateRequirementSuggestions(requirement, configuration, missing, suggestions)
    } else if (currentCount > requirement.quantity) {
      warnings.push({
        constraintId: `requirement_${requirement.id}`,
        message: `${requirement.name}配置了${currentCount}个，超过需要的${requirement.quantity}个`,
        severity: 'info',
        affectedSlots: []
      })
    }

    // 检查放置规则
    this.checkPlacementRules(requirement, satisfyingSelections, violations, suggestions)

    return {
      violations: violations.length > 0 ? violations : undefined,
      suggestions: suggestions.length > 0 ? suggestions : undefined,
      warnings: warnings.length > 0 ? warnings : undefined
    }
  }

  private findSatisfyingSelections(requirement: MaterialRequirement, configuration: Configuration): Selection[] {
    const allSelections = Object.values(configuration.selections).flat()
    
    return allSelections.filter(selection => {
      const material = this.model.materials.find(m => m.id === selection.materialId)
      if (!material) return false

      switch (requirement.constraintType) {
        case MaterialConstraintType.CATEGORY_ONLY:
          return requirement.allowedMaterialTypes?.includes(material.type) || false

        case MaterialConstraintType.SPECIFIC_MATERIALS:
          return requirement.specificMaterialIds?.includes(material.id) || false

        case MaterialConstraintType.CATEGORY_WITH_BLACKLIST:
          const typeAllowed = requirement.allowedMaterialTypes?.includes(material.type) || false
          const notBlacklisted = !requirement.blacklistMaterialIds?.includes(material.id)
          return typeAllowed && notBlacklisted

        case MaterialConstraintType.FLEXIBLE:
          return requirement.allowedMaterialTypes?.includes(material.type) || false

        default:
          return false
      }
    })
  }

  private generateRequirementSuggestions(
    requirement: MaterialRequirement,
    configuration: Configuration,
    missingCount: number,
    suggestions: Suggestion[]
  ): void {
    // 按优先级排序可用的放置位置
    const availablePlacements = requirement.placements
      .filter(placement => {
        const slotSelections = configuration.selections[placement.slotId] || []
        return slotSelections.length === 0 // 槽位为空
      })
      .sort((a, b) => b.priority - a.priority)

    // 获取可用的物料
    const availableMaterials = this.getAvailableMaterialsForRequirement(requirement)

    // 为每个缺失的物料生成建议
    for (let i = 0; i < Math.min(missingCount, availablePlacements.length) && i < availableMaterials.length; i++) {
      const placement = availablePlacements[i]
      const material = availableMaterials[i % availableMaterials.length]

      suggestions.push({
        type: 'add_material',
        materialId: material.id,
        slotId: placement.slotId,
        reason: `${requirement.name}: ${placement.description}`,
        priority: placement.priority
      })
    }
  }

  private getAvailableMaterialsForRequirement(requirement: MaterialRequirement): Material[] {
    switch (requirement.constraintType) {
      case MaterialConstraintType.CATEGORY_ONLY:
        return this.model.materials.filter(m => 
          requirement.allowedMaterialTypes?.includes(m.type)
        )

      case MaterialConstraintType.SPECIFIC_MATERIALS:
        return this.model.materials.filter(m =>
          requirement.specificMaterialIds?.includes(m.id)
        )

      case MaterialConstraintType.CATEGORY_WITH_BLACKLIST:
        return this.model.materials.filter(m => {
          const typeAllowed = requirement.allowedMaterialTypes?.includes(m.type) || false
          const notBlacklisted = !requirement.blacklistMaterialIds?.includes(m.id)
          return typeAllowed && notBlacklisted
        })

      case MaterialConstraintType.FLEXIBLE:
        return this.model.materials.filter(m =>
          requirement.allowedMaterialTypes?.includes(m.type)
        )

      default:
        return []
    }
  }

  private checkPlacementRules(
    requirement: MaterialRequirement,
    satisfyingSelections: Selection[],
    violations: ConstraintViolation[],
    suggestions: Suggestion[]
  ): void {
    const requiredPlacements = requirement.placements.filter(p => p.required)
    
    // 检查必需的放置位置
    requiredPlacements.forEach(placement => {
      const hasRequiredPlacement = satisfyingSelections.some(sel => sel.slotId === placement.slotId)
      
      if (!hasRequiredPlacement) {
        violations.push({
          constraintId: `placement_${requirement.id}_${placement.slotId}`,
          message: `${requirement.name}: ${placement.description}`,
          severity: 'error',
          affectedSlots: [placement.slotId],
          autoFixable: true
        })

        // 如果槽位为空，生成建议
        const availableMaterials = this.getAvailableMaterialsForRequirement(requirement)
        if (availableMaterials.length > 0) {
          suggestions.push({
            type: 'add_material',
            materialId: availableMaterials[0].id,
            slotId: placement.slotId,
            reason: placement.description,
            priority: placement.priority
          })
        }
      }
    })
  }

  private checkMaterialDependency(
    constraint: ConstraintRule, 
    configuration: Configuration
  ): { violations?: ConstraintViolation[], suggestions?: Suggestion[] } {
    const { primaryMaterialId, requiredMaterials } = constraint.params
    
    const hasPrimaryMaterial = this.configurationHasMaterial(configuration, primaryMaterialId)
    
    if (hasPrimaryMaterial) {
      const missingMaterials = requiredMaterials.filter((reqMat: any) => 
        !this.configurationHasMaterial(configuration, reqMat.materialId)
      )
      
      if (missingMaterials.length > 0) {
        const primaryMaterial = this.model.materials.find(m => m.id === primaryMaterialId)
        
        return {
          violations: [{
            constraintId: constraint.id,
            message: `选择${primaryMaterial?.name}需要携带: ${missingMaterials.map((m: any) => m.name).join(', ')}`,
            severity: 'error' as const,
            affectedSlots: [],
            autoFixable: true
          }],
          suggestions: missingMaterials.map((reqMat: any) => {
            // 自动找到合适的槽位
            const material = this.model.materials.find(m => m.id === reqMat.materialId)
            const compatibleSlot = this.model.slots.find(slot => 
              slot.allowedMaterialTypes.includes(material?.type) ||
              slot.allowedMaterials.includes(reqMat.materialId) ||
              (slot.allowedMaterialTypes.length === 0 && slot.allowedMaterials.length === 0)
            )
            
            return {
              type: 'add_material' as const,
              materialId: reqMat.materialId,
              slotId: compatibleSlot?.id,
              reason: `${primaryMaterial?.name}的必需组件`,
              priority: 9
            }
          })
        }
      }
    }
    
    return {}
  }

  private checkMaterialType(
    constraint: ConstraintRule, 
    configuration: Configuration
  ): { violations?: ConstraintViolation[] } {
    const { slotId, allowedTypes, minQuantity, maxQuantity } = constraint.params
    
    const selections = configuration.selections[slotId] || []
    const selectedMaterials = selections.map(sel => 
      this.model.materials.find(m => m.id === sel.materialId)
    ).filter(Boolean)
    
    const violations: ConstraintViolation[] = []
    
    // 检查类型约束
    const invalidMaterials = selectedMaterials.filter(material => 
      material && !allowedTypes.includes(material.type)
    )
    
    if (invalidMaterials.length > 0) {
      violations.push({
        constraintId: constraint.id,
        message: `槽位只允许以下类型: ${allowedTypes.join(', ')}`,
        severity: 'error',
        affectedSlots: [slotId],
        autoFixable: false
      })
    }
    
    // 检查数量约束
    const totalQuantity = selections.reduce((sum, sel) => sum + sel.quantity, 0)
    
    if (totalQuantity < minQuantity) {
      violations.push({
        constraintId: constraint.id,
        message: `最少需要${minQuantity}个物料`,
        severity: 'error',
        affectedSlots: [slotId],
        autoFixable: false
      })
    }
    
    if (totalQuantity > maxQuantity) {
      violations.push({
        constraintId: constraint.id,
        message: `最多只能选择${maxQuantity}个物料`,
        severity: 'error',
        affectedSlots: [slotId],
        autoFixable: false
      })
    }
    
    return violations.length > 0 ? { violations } : {}
  }

  private checkFunctionalConstraint(
    constraint: ConstraintRule, 
    configuration: Configuration
  ): { violations?: ConstraintViolation[], suggestions?: Suggestion[] } {
    const { functionId, requiredMaterialTypes } = constraint.params
    
    if (!configuration.functions.includes(functionId)) {
      return {}
    }
    
    const violations: ConstraintViolation[] = []
    const suggestions: Suggestion[] = []
    
    // 检查是否有足够的材料来实现功能
    for (const materialType of requiredMaterialTypes) {
      const hasMaterialOfType = this.configurationHasMaterialType(configuration, materialType)
      
      if (!hasMaterialOfType) {
        const func = this.model.availableFunctions.find(f => f.id === functionId)
        violations.push({
          constraintId: constraint.id,
          message: `实现${func?.name}功能需要${materialType}类型的物料`,
          severity: 'error',
          affectedSlots: [],
          autoFixable: true
        })
        
        // 推荐合适的材料
        const suitableMaterials = this.model.materials.filter(m => m.type === materialType)
        if (suitableMaterials.length > 0) {
          suggestions.push({
            type: 'add_material',
            materialId: suitableMaterials[0].id,
            reason: `实现${func?.name}功能`,
            priority: 8
          })
        }
      }
    }
    
    return { violations: violations.length > 0 ? violations : undefined, suggestions }
  }

  private checkMaterialQuantityConstraint(
    constraint: ConstraintRule, 
    configuration: Configuration
  ): { violations?: ConstraintViolation[] } {
    const { materialId, materialType, maxQuantity, message } = constraint.params
    
    const violations: ConstraintViolation[] = []
    let count = 0

    // 统计指定物料或物料类型的使用数量
    Object.values(configuration.selections).flat().forEach(selection => {
      const material = this.model.materials.find(m => m.id === selection.materialId)
      if (material) {
        if (materialId && material.id === materialId) {
          count++
        } else if (materialType && material.type === materialType) {
          count++
        }
      }
    })

    if (count > maxQuantity) {
      const targetName = materialId 
        ? this.model.materials.find(m => m.id === materialId)?.name || materialId
        : materialType || '物料'
      
      violations.push({
        constraintId: constraint.id,
        message: message || `${targetName}使用数量超限，最多允许${maxQuantity}个，当前使用${count}个`,
        severity: 'error',
        affectedSlots: [],
        autoFixable: false
      })
    }

    return violations.length > 0 ? { violations } : {}
  }

  private checkSpecificPlacementConstraint(
    constraint: ConstraintRule, 
    configuration: Configuration
  ): { violations?: ConstraintViolation[], suggestions?: Suggestion[] } {
    const { functionId, requiredPlacements } = constraint.params
    
    // 只有当功能被选择时才检查此约束
    if (!configuration.functions.includes(functionId)) {
      return {}
    }

    const violations: ConstraintViolation[] = []
    const suggestions: Suggestion[] = []

    requiredPlacements?.forEach((placement: any) => {
      const { slotId, materialType, materialId, description } = placement
      
      // 检查指定槽位是否有合适的物料
      const slotSelections = configuration.selections[slotId] || []
      let hasValidMaterial = false

      if (slotSelections.length > 0) {
        const selection = slotSelections[0] // 每个槽只能有一个物料
        const material = this.model.materials.find(m => m.id === selection.materialId)
        
        if (material) {
          if (materialId && material.id === materialId) {
            hasValidMaterial = true
          } else if (materialType && material.type === materialType) {
            hasValidMaterial = true
          }
        }
      }

      if (!hasValidMaterial) {
        const func = this.model.availableFunctions.find(f => f.id === functionId)
        violations.push({
          constraintId: constraint.id,
          message: `${func?.name}功能要求：${description}`,
          severity: 'error',
          affectedSlots: [slotId],
          autoFixable: true
        })

        // 生成建议
        if (materialType) {
          const suitableMaterials = this.model.materials.filter(m => m.type === materialType)
          if (suitableMaterials.length > 0) {
            suggestions.push({
              type: 'add_material',
              materialId: suitableMaterials[0].id,
              slotId: slotId,
              reason: description,
              priority: 9
            })
          }
        }
      }
    })

    return {
      violations: violations.length > 0 ? violations : undefined,
      suggestions: suggestions.length > 0 ? suggestions : undefined
    }
  }

  private checkMutualExclusive(
    constraint: ConstraintRule, 
    configuration: Configuration
  ): { violations?: ConstraintViolation[] } {
    // 实现互斥约束检查
    return {}
  }

  private configurationHasMaterial(configuration: Configuration, materialId: string): boolean {
    return Object.values(configuration.selections).flat().some(sel => sel.materialId === materialId)
  }

  private configurationHasMaterialType(configuration: Configuration, materialType: MaterialType): boolean {
    const selectedMaterials = Object.values(configuration.selections).flat()
    return selectedMaterials.some(sel => {
      const material = this.model.materials.find(m => m.id === sel.materialId)
      return material?.type === materialType
    })
  }

  private applySelection(configuration: Configuration, selection: Selection): Configuration {
    const newConfig = JSON.parse(JSON.stringify(configuration))
    
    // 每个槽只能安装一个物料，直接替换
    newConfig.selections[selection.slotId] = [selection]
    
    return newConfig
  }

  private recommendMaterialsForFunction(func: any, configuration: Configuration): Suggestion[] {
    // 实现功能基础的材料推荐
    return []
  }

  private getCostOptimizations(configuration: Configuration): Suggestion[] {
    // 实现成本优化建议
    return []
  }
}