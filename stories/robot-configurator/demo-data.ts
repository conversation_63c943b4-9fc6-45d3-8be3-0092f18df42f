import {
  RobotModel,
  Material,
  MaterialType,
  MaterialCategory,
  ConfigurationSlot,
  SlotType,
  RobotFunction,
  ConstraintRule,
  ConstraintType,
  MaterialConstraintType,
  MaterialRequirement,
  PlacementRule,
  ImplementationOption
} from './types'

// 材料分类
export const materialCategories: MaterialCategory[] = [
  { id: 'sensors', name: '传感器', description: '各类传感器设备' },
  { id: 'actuators', name: '执行器', description: '电机、气缸等执行器' },
  { id: 'controllers', name: '控制器', description: '控制单元和计算设备' },
  { id: 'power', name: '电源', description: '电源和电池系统' },
  { id: 'mechanical', name: '机械结构', description: '机械零部件' },
  { id: 'communication', name: '通讯', description: '通讯模块和线束' }
]

// 测试材料数据
export const testMaterials: Material[] = [
  // 激光雷达类
  {
    id: 'lidar_sick_lms111',
    name: 'SICK LMS111激光雷达',
    category: materialCategories[0],
    type: MaterialType.SENSOR,
    price: 8500,
    specifications: {
      range: '20m',
      accuracy: '±15mm',
      scanRate: '25Hz',
      interface: 'Ethernet'
    },
    subMaterials: [
      { materialId: 'cable_ethernet_5m', name: '以太网线5m', required: true, quantity: 1 },
      { materialId: 'power_adapter_24v', name: '24V电源适配器', required: true, quantity: 1 }
    ],
    compatibilityRules: ['requires_ethernet_port'],
    description: '高精度2D激光扫描仪，适用于导航和避障'
  },
  {
    id: 'lidar_velodyne_vlp16',
    name: 'Velodyne VLP-16激光雷达',
    category: materialCategories[0],
    type: MaterialType.SENSOR,
    price: 15000,
    specifications: {
      range: '100m',
      accuracy: '±3cm',
      scanRate: '10Hz',
      interface: 'Ethernet',
      channels: 16
    },
    subMaterials: [
      { materialId: 'cable_ethernet_10m', name: '以太网线10m', required: true, quantity: 1 },
      { materialId: 'power_adapter_12v', name: '12V电源适配器', required: true, quantity: 1 }
    ],
    compatibilityRules: ['requires_ethernet_port', 'requires_high_processing_power'],
    description: '3D激光雷达，提供360度环境感知'
  },

  // 相机类
  {
    id: 'camera_realsense_d435',
    name: 'Intel RealSense D435深度相机',
    category: materialCategories[0],
    type: MaterialType.SENSOR,
    price: 2500,
    specifications: {
      resolution: '1920x1080',
      fps: '30',
      depthRange: '0.1-10m',
      interface: 'USB3.0'
    },
    subMaterials: [
      { materialId: 'cable_usb3_3m', name: 'USB3.0线3m', required: true, quantity: 1 }
    ],
    compatibilityRules: ['requires_usb3_port'],
    description: 'RGB-D深度相机，支持SLAM和物体识别'
  },

  // 控制器类
  {
    id: 'controller_nvidia_xavier',
    name: 'NVIDIA Xavier AGX控制器',
    category: materialCategories[2],
    type: MaterialType.CONTROLLER,
    price: 12000,
    specifications: {
      cpu: 'ARM Carmel 8-core',
      gpu: 'Volta 512-core',
      memory: '32GB',
      storage: '32GB eUFS',
      interfaces: ['Ethernet', 'USB3.0', 'CAN']
    },
    subMaterials: [],
    compatibilityRules: ['provides_ethernet_port', 'provides_usb3_port', 'provides_high_processing_power'],
    description: '高性能AI计算平台'
  },
  {
    id: 'controller_raspberry_pi4',
    name: 'Raspberry Pi 4B控制器',
    category: materialCategories[2],
    type: MaterialType.CONTROLLER,
    price: 800,
    specifications: {
      cpu: 'ARM Cortex-A72 4-core',
      memory: '8GB',
      interfaces: ['Ethernet', 'USB3.0', 'GPIO']
    },
    subMaterials: [
      { materialId: 'sd_card_64gb', name: '64GB SD卡', required: true, quantity: 1 }
    ],
    compatibilityRules: ['provides_ethernet_port', 'provides_usb3_port'],
    description: '经济型控制器，适合轻量级应用'
  },

  // 电源类
  {
    id: 'power_adapter_24v',
    name: '24V电源适配器',
    category: materialCategories[3],
    type: MaterialType.POWER,
    price: 200,
    specifications: { voltage: '24V', current: '5A', power: '120W' },
    subMaterials: [],
    compatibilityRules: [],
    description: '24V直流电源适配器'
  },
  {
    id: 'power_adapter_12v',
    name: '12V电源适配器',
    category: materialCategories[3],
    type: MaterialType.POWER,
    price: 150,
    specifications: { voltage: '12V', current: '3A', power: '36W' },
    subMaterials: [],
    compatibilityRules: [],
    description: '12V直流电源适配器'
  },

  // 线束类
  {
    id: 'cable_ethernet_5m',
    name: '以太网线5m',
    category: materialCategories[5],
    type: MaterialType.COMMUNICATION,
    price: 50,
    specifications: { length: '5m', type: 'Cat6', connector: 'RJ45' },
    subMaterials: [],
    compatibilityRules: [],
    description: '5米以太网连接线'
  },
  {
    id: 'cable_ethernet_10m',
    name: '以太网线10m',
    category: materialCategories[5],
    type: MaterialType.COMMUNICATION,
    price: 80,
    specifications: { length: '10m', type: 'Cat6', connector: 'RJ45' },
    subMaterials: [],
    compatibilityRules: [],
    description: '10米以太网连接线'
  },
  {
    id: 'cable_usb3_3m',
    name: 'USB3.0线3m',
    category: materialCategories[5],
    type: MaterialType.COMMUNICATION,
    price: 30,
    specifications: { length: '3m', type: 'USB3.0', connector: 'Type-A to Type-C' },
    subMaterials: [],
    compatibilityRules: [],
    description: '3米USB3.0连接线'
  },

  // 其他配件
  {
    id: 'sd_card_64gb',
    name: '64GB SD卡',
    category: materialCategories[2],
    type: MaterialType.MECHANICAL, // 改为机械配件类型，不占用控制器槽位
    price: 120,
    specifications: { capacity: '64GB', class: 'Class 10', speed: 'UHS-I' },
    subMaterials: [],
    compatibilityRules: [],
    description: 'Raspberry Pi专用高速存储卡'
  }
]

// 机器人功能定义 - 展示4种不同的约束场景
export const robotFunctions: RobotFunction[] = [
  // 场景1：只有类别约束
  {
    id: 'navigation',
    name: '自主导航',
    description: '机器人自主路径规划和导航能力',
    implementations: [
      {
        id: 'nav_lidar_2d',
        name: '2D激光导航',
        materials: ['lidar_sick_lms111', 'controller_raspberry_pi4'],
        performance: { accuracy: 85, cost: 70, complexity: 60 },
        cost: 9300,
        description: '基于2D激光雷达的导航方案'
      },
      {
        id: 'nav_lidar_3d',
        name: '3D激光导航',
        materials: ['lidar_velodyne_vlp16', 'controller_nvidia_xavier'],
        performance: { accuracy: 95, cost: 30, complexity: 90 },
        cost: 27000,
        description: '基于3D激光雷达的高精度导航方案'
      },
      {
        id: 'nav_visual_slam',
        name: '视觉SLAM导航',
        materials: ['camera_realsense_d435', 'controller_nvidia_xavier'],
        performance: { accuracy: 80, cost: 60, complexity: 85 },
        cost: 14500,
        description: '基于视觉SLAM的导航方案'
      }
    ],
    materialRequirements: [
      {
        id: 'nav_main_sensor',
        name: '主导航传感器',
        description: '负责环境感知和路径规划的核心传感器',
        constraintType: MaterialConstraintType.CATEGORY_ONLY,
        quantity: 1,
        allowedMaterialTypes: [MaterialType.SENSOR],
        placements: [
          {
            slotId: 'navigation_sensor',
            priority: 10,
            required: true,
            description: '必须安装在前方导航传感器槽位'
          }
        ]
      },
      {
        id: 'nav_controller',
        name: '导航控制器',
        description: '处理导航算法的控制器',
        constraintType: MaterialConstraintType.CATEGORY_ONLY,
        quantity: 1,
        allowedMaterialTypes: [MaterialType.CONTROLLER],
        placements: [
          {
            slotId: 'main_controller',
            priority: 10,
            required: true,
            description: '必须安装在主控制器槽位'
          }
        ]
      }
    ]
  },
  // 场景2：具体到特定物料款式
  {
    id: 'obstacle_avoidance',
    name: '避障功能',
    description: '实时障碍物检测和避障',
    implementations: [
      {
        id: 'avoid_lidar',
        name: '激光避障',
        materials: ['lidar_sick_lms111'],
        performance: { accuracy: 90, cost: 80, complexity: 50 },
        cost: 8500,
        description: '激光雷达避障方案'
      },
      {
        id: 'avoid_camera',
        name: '视觉避障',
        materials: ['camera_realsense_d435'],
        performance: { accuracy: 75, cost: 90, complexity: 70 },
        cost: 2500,
        description: '深度相机避障方案'
      }
    ],
    materialRequirements: [
      {
        id: 'avoid_specific_sensor',
        name: '指定避障传感器',
        description: '只允许使用经过测试验证的特定传感器型号',
        constraintType: MaterialConstraintType.SPECIFIC_MATERIALS,
        quantity: 1,
        specificMaterialIds: ['lidar_sick_lms111', 'camera_realsense_d435'],
        placements: [
          {
            slotId: 'rear_sensor_slot',
            priority: 8,
            required: true,
            description: '必须安装在后部传感器槽位，用于后方避障'
          },
          {
            slotId: 'left_lidar_slot',
            priority: 6,
            required: false,
            description: '可选安装在左侧槽位，提供侧面避障'
          }
        ]
      }
    ]
  },
  // 场景3：类别+黑名单约束
  {
    id: 'precise_positioning',
    name: '高精度定位',
    description: '机器人高精度定位和局部避障功能，排除不兼容的传感器',
    implementations: [
      {
        id: 'dual_lidar_positioning',
        name: '双激光定位',
        materials: ['lidar_sick_lms111'],
        performance: { accuracy: 95, cost: 60, complexity: 70 },
        cost: 17000,
        description: '使用左右两个激光雷达进行高精度定位'
      }
    ],
    materialRequirements: [
      {
        id: 'dual_positioning_sensors',
        name: '双侧定位传感器',
        description: '左右两侧各需要一个传感器，但不能使用某些不兼容的型号',
        constraintType: MaterialConstraintType.CATEGORY_WITH_BLACKLIST,
        quantity: 2,
        allowedMaterialTypes: [MaterialType.SENSOR],
        blacklistMaterialIds: ['camera_realsense_d435'], // 相机不适合精确定位
        placements: [
          {
            slotId: 'left_lidar_slot',
            priority: 10,
            required: true,
            description: '第1个传感器必须安装在左侧槽位'
          },
          {
            slotId: 'right_lidar_slot',
            priority: 10,
            required: true,
            description: '第2个传感器必须安装在右侧槽位'
          }
        ]
      }
    ]
  },
  
  // 场景4：灵活约束（类别+数量，支持多种放置方案）
  {
    id: 'environmental_monitoring',
    name: '环境监测',
    description: '多传感器环境监测系统，灵活配置',
    implementations: [
      {
        id: 'multi_sensor_monitoring',
        name: '多传感器监测',
        materials: ['lidar_sick_lms111', 'camera_realsense_d435'],
        performance: { accuracy: 85, cost: 70, complexity: 60 },
        cost: 12000,
        description: '使用多种传感器进行环境监测'
      }
    ],
    materialRequirements: [
      {
        id: 'monitoring_sensors',
        name: '监测传感器阵列',
        description: '需要3个传感器，可灵活分布在不同位置',
        constraintType: MaterialConstraintType.FLEXIBLE,
        quantity: 3,
        allowedMaterialTypes: [MaterialType.SENSOR],
        placements: [
          {
            slotId: 'navigation_sensor',
            priority: 9,
            required: false,
            description: '前方主传感器位置（推荐）'
          },
          {
            slotId: 'left_lidar_slot',
            priority: 8,
            required: false,
            description: '左侧传感器位置（推荐）'
          },
          {
            slotId: 'right_lidar_slot',
            priority: 8,
            required: false,
            description: '右侧传感器位置（推荐）'
          },
          {
            slotId: 'rear_sensor_slot',
            priority: 7,
            required: false,
            description: '后方传感器位置（可选）'
          }
        ]
      },
      {
        id: 'monitoring_controller',
        name: '监测数据处理单元',
        description: '处理多传感器数据的控制器',
        constraintType: MaterialConstraintType.SPECIFIC_MATERIALS,
        quantity: 1,
        specificMaterialIds: ['controller_nvidia_xavier'], // 需要高性能控制器
        placements: [
          {
            slotId: 'main_controller',
            priority: 10,
            required: true,
            description: '必须使用高性能控制器处理多传感器数据'
          }
        ]
      }
    ]
  }
]

// 配置槽位定义 - 每个槽只能安装一个物料
export const configurationSlots: ConfigurationSlot[] = [
  {
    id: 'main_controller',
    name: '主控制器',
    type: SlotType.MATERIAL_TYPE,
    allowedMaterialTypes: [MaterialType.CONTROLLER],
    allowedMaterials: [],
    required: true,
    constraints: ['controller_required'],
    position: 'center'
  },
  {
    id: 'navigation_sensor',
    name: '主导航传感器',
    type: SlotType.FUNCTION,
    allowedMaterialTypes: [MaterialType.SENSOR],
    allowedMaterials: [],
    required: true,
    constraints: ['navigation_sensor_constraint'],
    position: 'front'
  },
  {
    id: 'left_lidar_slot',
    name: '左侧激光雷达槽',
    type: SlotType.MATERIAL_TYPE,
    allowedMaterialTypes: [MaterialType.SENSOR],
    allowedMaterials: [],
    required: false,
    constraints: [],
    position: 'left'
  },
  {
    id: 'right_lidar_slot',
    name: '右侧激光雷达槽',
    type: SlotType.MATERIAL_TYPE,
    allowedMaterialTypes: [MaterialType.SENSOR],
    allowedMaterials: [],
    required: false,
    constraints: [],
    position: 'right'
  },
  {
    id: 'rear_sensor_slot',
    name: '后部传感器槽',
    type: SlotType.MATERIAL_TYPE,
    allowedMaterialTypes: [MaterialType.SENSOR],
    allowedMaterials: [],
    required: false,
    constraints: [],
    position: 'rear'
  },
  {
    id: 'power_slot_1',
    name: '主电源槽',
    type: SlotType.MATERIAL_TYPE,
    allowedMaterialTypes: [MaterialType.POWER],
    allowedMaterials: [],
    required: true,
    constraints: ['power_compatibility'],
    position: 'internal'
  },
  {
    id: 'power_slot_2',
    name: '备用电源槽',
    type: SlotType.MATERIAL_TYPE,
    allowedMaterialTypes: [MaterialType.POWER],
    allowedMaterials: [],
    required: false,
    constraints: [],
    position: 'internal'
  },
  {
    id: 'accessory_slot_1',
    name: '配件槽1',
    type: SlotType.MATERIAL_TYPE,
    allowedMaterialTypes: [MaterialType.COMMUNICATION, MaterialType.MECHANICAL],
    allowedMaterials: [],
    required: false,
    constraints: []
  },
  {
    id: 'accessory_slot_2',
    name: '配件槽2',
    type: SlotType.MATERIAL_TYPE,
    allowedMaterialTypes: [MaterialType.COMMUNICATION, MaterialType.MECHANICAL],
    allowedMaterials: [],
    required: false,
    constraints: []
  },
  {
    id: 'accessory_slot_3',
    name: '配件槽3',
    type: SlotType.MATERIAL_TYPE,
    allowedMaterialTypes: [MaterialType.COMMUNICATION, MaterialType.MECHANICAL],
    allowedMaterials: [],
    required: false,
    constraints: []
  }
]

// 约束规则定义
export const constraintRules: ConstraintRule[] = [
  {
    id: 'lidar_dependencies',
    type: ConstraintType.MATERIAL_DEPENDENCY,
    description: 'SICK LMS111激光雷达需要配套线束和电源',
    params: {
      primaryMaterialId: 'lidar_sick_lms111',
      requiredMaterials: [
        { materialId: 'cable_ethernet_5m', name: '以太网线5m' },
        { materialId: 'power_adapter_24v', name: '24V电源适配器' }
      ]
    }
  },
  {
    id: 'velodyne_dependencies',
    type: ConstraintType.MATERIAL_DEPENDENCY,
    description: 'Velodyne VLP-16需要配套线束和电源',
    params: {
      primaryMaterialId: 'lidar_velodyne_vlp16',
      requiredMaterials: [
        { materialId: 'cable_ethernet_10m', name: '以太网线10m' },
        { materialId: 'power_adapter_12v', name: '12V电源适配器' }
      ]
    }
  },
  {
    id: 'camera_dependencies',
    type: ConstraintType.MATERIAL_DEPENDENCY,
    description: 'RealSense相机需要USB线',
    params: {
      primaryMaterialId: 'camera_realsense_d435',
      requiredMaterials: [
        { materialId: 'cable_usb3_3m', name: 'USB3.0线3m' }
      ]
    }
  },
  {
    id: 'raspberry_pi_dependencies',
    type: ConstraintType.MATERIAL_DEPENDENCY,
    description: 'Raspberry Pi需要SD卡',
    params: {
      primaryMaterialId: 'controller_raspberry_pi4',
      requiredMaterials: [
        { materialId: 'sd_card_64gb', name: '64GB SD卡' }
      ]
    }
  },
  {
    id: 'controller_required',
    type: ConstraintType.MATERIAL_TYPE,
    description: '必须选择一个主控制器',
    params: {
      slotId: 'main_controller',
      allowedTypes: [MaterialType.CONTROLLER],
      minQuantity: 1,
      maxQuantity: 1
    }
  },
  {
    id: 'navigation_function_constraint',
    type: ConstraintType.FUNCTIONAL,
    description: '导航功能需要传感器和控制器',
    params: {
      functionId: 'navigation',
      requiredMaterialTypes: [MaterialType.SENSOR, MaterialType.CONTROLLER]
    }
  },
  // 新的物料数量约束示例
  {
    id: 'lidar_quantity_limit',
    type: ConstraintType.MATERIAL_QUANTITY,
    description: 'SICK激光雷达最多使用2个',
    params: {
      materialId: 'lidar_sick_lms111',
      maxQuantity: 2,
      message: '同一型号的激光雷达最多只能使用2个'
    }
  },
  {
    id: 'controller_quantity_limit',
    type: ConstraintType.MATERIAL_QUANTITY,
    description: '控制器类型物料最多使用1个',
    params: {
      materialType: MaterialType.CONTROLLER,
      maxQuantity: 1,
      message: '整个系统只能使用1个控制器'
    }
  },
  // 特定放置约束 - 高精度定位功能
  {
    id: 'precise_positioning_placement',
    type: ConstraintType.SPECIFIC_PLACEMENT,
    description: '高精度定位功能需要在左右两侧安装激光雷达',
    params: {
      functionId: 'precise_positioning',
      requiredPlacements: [
        {
          slotId: 'left_lidar_slot',
          materialType: MaterialType.SENSOR,
          description: '左侧必须安装传感器'
        },
        {
          slotId: 'right_lidar_slot',
          materialType: MaterialType.SENSOR,
          description: '右侧必须安装传感器'
        }
      ]
    }
  }
]

// 完整的机器人型号定义
export const demoRobotModel: RobotModel = {
  id: 'agv_standard_v1',
  name: 'AGV标准版 V1.0',
  description: '标准自主导航机器人平台，支持多种传感器配置和功能扩展',
  basePrice: 15000,
  slots: configurationSlots,
  availableFunctions: robotFunctions,
  materials: testMaterials,
  constraintRules: constraintRules
}

// 预设配置用于演示约束效果
export const demoConfigurationWithConstraints = {
  id: 'demo_config',
  name: '演示配置 - 触发约束',
  robotModelId: 'agv_standard_v1',
  selections: {
    'navigation_sensor': [
      { materialId: 'lidar_sick_lms111', slotId: 'navigation_sensor' }
    ]
  },
  functions: ['navigation']
}