import type { Meta, StoryObj } from '@storybook/web-components'
import { html } from 'lit'
import './components'
import './rule-manager/components'
import './rule-manager/material-form'
import './rule-manager/constraint-form'
import { demoRobotModel, demoConfigurationWithConstraints } from './demo-data'

const meta: Meta = {
  title: 'Business/RobotConfigurator',
  component: 'robot-configurator',
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: `
# 机器人配置器 Demo

这是一个展示复杂业务约束求解的机器人配置系统demo。

## 功能特点

### 🎯 三类约束支撑
1. **物料依赖约束** - 选择激光雷达自动携带线束和电源
2. **物料类型约束** - 按槽位限制可选材料类型和数量
3. **功能约束** - 选择功能后推荐或限制相应物料

### 🧠 智能约束求解
- 实时约束验证和冲突检测
- 自动依赖分析和建议
- 增量式约束传播算法

### 🎨 直观配置界面
- 左侧：功能需求选择
- 中间：分槽位物料配置
- 右侧：实时验证和成本汇总

## 测试场景

1. **依赖约束测试**：选择"SICK LMS111激光雷达"，观察系统自动提示需要配套的线束和电源

2. **功能约束测试**：勾选"自主导航"功能，观察系统推荐的实现方案

3. **类型约束测试**：在主控制器槽位尝试选择非控制器类材料，观察约束拦截

4. **成本优化测试**：对比不同方案的成本差异

## 架构亮点

- **CSP约束求解**：使用约束满足问题算法
- **增量验证**：每次选择后立即验证，性能优化
- **模块化设计**：约束规则与UI解耦，易于扩展
- **智能推荐**：基于约束分析的智能建议系统
        `
      }
    }
  },
  argTypes: {
    model: {
      control: false,
      description: '机器人型号配置数据'
    }
  }
}

export default meta
type Story = StoryObj

export const Default: Story = {
  name: '机器人配置器Demo',
  render: () => html`
    <robot-configurator .model=${demoRobotModel}></robot-configurator>
  `,
  parameters: {
    docs: {
      source: {
        code: `
// 使用示例
import { demoRobotModel } from './demo-data'
import './components'

// 在HTML中使用
<robot-configurator .model="\${demoRobotModel}"></robot-configurator>

// 或在Lit组件中
render() {
  return html\`
    <robot-configurator .model=\${this.robotModel}></robot-configurator>
  \`
}
        `
      }
    }
  }
}

export const EmptyConfiguration: Story = {
  name: '空配置状态',
  render: () => {
    const emptyModel = { ...demoRobotModel }
    // 清空预选功能，展示初始状态
    return html`
      <robot-configurator .model=${emptyModel}></robot-configurator>
    `
  },
  parameters: {
    docs: {
      description: {
        story: '展示用户初次进入配置器时的空白状态，所有约束都未激活。'
      }
    }
  }
}

export const PresetConstraintDemo: Story = {
  name: '预设约束演示',
  render: () => {
    // 创建一个带有预设配置的版本
    const configuratorWithPreset = document.createElement('robot-configurator') as any
    configuratorWithPreset.model = demoRobotModel
    
    // 延迟设置预设配置，确保组件已初始化
    setTimeout(() => {
      if (configuratorWithPreset.currentConfiguration) {
        configuratorWithPreset.currentConfiguration = { ...demoConfigurationWithConstraints }
        configuratorWithPreset.validateConfiguration()
        configuratorWithPreset.requestUpdate()
      }
    }, 100)
    
    return html`
      <div style="padding: 20px; background: #f5f5f5; min-height: 100vh;">
        <div style="max-width: 1200px; margin: 0 auto;">
          <h1 style="text-align: center; margin-bottom: 30px; color: #333;">
            约束演示 - 预设了SICK激光雷达
          </h1>
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h2 style="color: #007acc; margin-top: 0;">当前状态</h2>
            <p style="color: #666; line-height: 1.6;">
              已预设选择了"SICK LMS111激光雷达"和"自主导航"功能。<br/>
              <strong style="color: #d73027;">系统检测到约束违反</strong>：激光雷达需要配套的线束和电源。<br/>
              请点击右侧的"自动修复"按钮解决约束违反。
            </p>
          </div>

          ${configuratorWithPreset}
        </div>
      </div>
    `
  },
  parameters: {
    docs: {
      description: {
        story: '展示预设配置触发约束违反的场景，演示自动修复功能。'
      }
    }
  }
}

export const ConstraintDemo: Story = {
  name: '交互式约束演示',
  render: () => html`
    <div style="padding: 20px; background: #f5f5f5; min-height: 100vh;">
      <div style="max-width: 1200px; margin: 0 auto;">
        <h1 style="text-align: center; margin-bottom: 30px; color: #333;">
          机器人配置系统 - 约束求解演示
        </h1>
        
        <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <h2 style="color: #007acc; margin-top: 0;">操作指南</h2>
          <ol style="line-height: 1.6; color: #666;">
            <li><strong>测试物料依赖约束</strong>：在"导航传感器"槽位选择激光雷达，观察右侧约束验证面板的提示</li>
            <li><strong>测试依赖修复</strong>：选择"Raspberry Pi 4B控制器"，系统会自动提示需要SD卡</li>
            <li><strong>验证槽位约束</strong>：主控制器槽位最多只能选1个，SD卡会自动添加到配件槽位</li>
            <li><strong>测试自动修复</strong>：点击约束违反项的"自动修复"按钮</li>
            <li><strong>查看成本汇总</strong>：观察右下角的实时成本计算</li>
          </ol>
          
          <div style="background: #e8f5e8; padding: 12px; border-radius: 6px; margin-top: 16px; border-left: 4px solid #28a745;">
            <strong style="color: #155724;">修复验证：</strong>
            <span style="color: #155724;">64GB SD卡现在作为配件，不会占用主控制器的数量约束</span>
          </div>
        </div>

        <robot-configurator .model=${demoRobotModel}></robot-configurator>
      </div>
    </div>
  `,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        story: `
完整的约束演示场景，包含操作指南。这个故事展示了系统如何处理复杂的业务约束：

**约束类型展示：**
- 物料依赖约束：激光雷达 → 线束+电源
- 功能约束：导航功能 → 传感器+控制器  
- 类型约束：槽位限制特定材料类型
- 数量约束：每个槽位的最小/最大数量限制

**智能特性：**
- 实时约束验证
- 自动依赖检测
- 智能推荐算法
- 成本优化建议
        `
      }
    }
  }
}