// 机器人配置器模块导出

// 导出核心类型
export * from './types'

// 导出约束求解器
export { ConstraintSolver } from './constraint-solver'

// 导出组件
export * from './components'

// 导出演示数据
export { 
  demoRobotModel, 
  testMaterials, 
  robotFunctions, 
  constraintRules,
  configurationSlots,
  materialCategories
} from './demo-data'

// 导出工具函数
export const ConfiguratorUtils = {
  /**
   * 创建空配置
   */
  createEmptyConfiguration(modelId: string, name: string = '新配置') {
    return {
      id: `config_${Date.now()}`,
      name,
      robotModelId: modelId,
      selections: {},
      functions: []
    }
  },

  /**
   * 计算配置成本
   */
  calculateConfigurationCost(configuration: any, model: any): number {
    let totalCost = model.basePrice
    
    Object.values(configuration.selections).flat().forEach((selection: any) => {
      const material = model.materials.find((m: any) => m.id === selection.materialId)
      if (material) {
        totalCost += material.price * selection.quantity
      }
    })
    
    return totalCost
  },

  /**
   * 导出配置为JSON
   */
  exportConfiguration(configuration: any): string {
    return JSON.stringify(configuration, null, 2)
  },

  /**
   * 从JSON导入配置
   */
  importConfiguration(jsonString: string): any {
    try {
      return JSON.parse(jsonString)
    } catch (error) {
      throw new Error('无效的配置文件格式')
    }
  },

  /**
   * 生成BOM清单
   */
  generateBOM(configuration: any, model: any) {
    const bom: any[] = []
    
    Object.values(configuration.selections).flat().forEach((selection: any) => {
      const material = model.materials.find((m: any) => m.id === selection.materialId)
      if (material) {
        bom.push({
          id: material.id,
          name: material.name,
          quantity: selection.quantity,
          unitPrice: material.price,
          totalPrice: material.price * selection.quantity,
          category: material.category.name,
          specifications: material.specifications
        })

        // 添加子物料
        material.subMaterials?.forEach((subMat: any) => {
          if (subMat.required) {
            const subMaterial = model.materials.find((m: any) => m.id === subMat.materialId)
            if (subMaterial) {
              bom.push({
                id: subMaterial.id,
                name: `${subMaterial.name} (${material.name}的配件)`,
                quantity: subMat.quantity * selection.quantity,
                unitPrice: subMaterial.price,
                totalPrice: subMaterial.price * subMat.quantity * selection.quantity,
                category: subMaterial.category.name,
                specifications: subMaterial.specifications,
                isSubMaterial: true,
                parentMaterialId: material.id
              })
            }
          }
        })
      }
    })
    
    return bom
  }
}

// 使用示例注释
/*
使用示例：

import { RobotConfigurator, demoRobotModel, ConstraintSolver } from './robot-configurator'

// 1. 基本使用
<robot-configurator .model=${demoRobotModel}></robot-configurator>

// 2. 创建自定义约束求解器
const solver = new ConstraintSolver(myRobotModel)
const validationResult = solver.validate(myConfiguration)

// 3. 生成BOM清单
const bom = ConfiguratorUtils.generateBOM(configuration, model)

// 4. 导出配置
const configJson = ConfiguratorUtils.exportConfiguration(configuration)
*/