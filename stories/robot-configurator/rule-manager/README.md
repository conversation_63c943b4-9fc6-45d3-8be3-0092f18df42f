# 机器人配置规则管理器

这是一个完整的规则录入和管理系统，专为机器人配置系统设计。它提供了直观的表单界面来创建和管理各类配置规则。

## 🚀 核心功能

### 1. 机器人型号管理
- 基本信息配置（ID、名称、价格、描述）
- 完整的数据验证
- 实时错误提示

### 2. 物料管理系统
- **基本信息**：ID、名称、类型、分类、价格
- **规格参数**：动态键值对配置
- **子物料依赖**：设置物料间的依赖关系
- **兼容性规则**：定义物料的兼容性要求

### 3. 约束规则配置
支持5种约束类型的可视化配置：

#### 物料依赖约束
```typescript
{
  type: 'material_dependency',
  params: {
    primaryMaterialId: 'lidar_sick_lms111',
    requiredMaterials: [
      { materialId: 'cable_ethernet_5m', name: '以太网线5m' },
      { materialId: 'power_adapter_24v', name: '24V电源适配器' }
    ]
  }
}
```

#### 物料类型约束
```typescript
{
  type: 'material_type',
  params: {
    slotId: 'main_controller',
    allowedTypes: ['controller'],
    minQuantity: 1,
    maxQuantity: 1
  }
}
```

#### 功能约束
```typescript
{
  type: 'functional',
  params: {
    functionId: 'navigation',
    requiredMaterialTypes: ['sensor', 'controller']
  }
}
```

### 4. 数据验证系统
- **实时验证**：输入时即时验证
- **完整性检查**：ID唯一性、引用有效性
- **类型检查**：确保数据类型正确
- **业务规则验证**：符合业务逻辑要求

### 5. 导入导出功能
- **JSON格式导出**：完整的配置数据
- **文件导入**：支持从JSON文件导入
- **数据预览**：导出前预览配置内容
- **版本管理**：导出时添加时间戳和版本信息

## 📋 使用指南

### 基本使用

```html
<!-- 基本规则管理器 -->
<rule-manager></rule-manager>

<!-- 带初始数据 -->
<rule-manager .initialData=${existingConfig}></rule-manager>
```

### 单独使用表单组件

```html
<!-- 物料表单 -->
<material-form 
  .availableMaterials=${materialIds}
  @material-save=${handleSave}
  @material-cancel=${handleCancel}
></material-form>

<!-- 约束表单 -->
<constraint-form 
  .availableMaterials=${materialIds}
  .availableSlots=${slotIds}
  .availableFunctions=${functionIds}
  @constraint-save=${handleSave}
></constraint-form>
```

### 工作流程

1. **项目初始化**
   - 在"机器人型号"标签页配置基本信息
   - 设置型号ID、名称、基础价格和描述

2. **物料库建设**
   - 在"物料管理"标签页录入所有物料
   - 配置物料规格、依赖关系和兼容性规则

3. **槽位架构设计**
   - 在"槽位配置"标签页定义配置结构
   - 设置槽位类型、数量限制和约束条件

4. **功能需求定义**
   - 在"功能定义"标签页配置机器人功能
   - 设置功能实现方案和性能指标

5. **约束规则配置**
   - 在"约束规则"标签页设置业务逻辑
   - 配置各类约束关系和验证规则

6. **配置验证导出**
   - 在"导出导入"标签页验证配置完整性
   - 导出完整的配置文件用于生产环境

## 🛠️ 技术特性

### 表单验证
- **实时验证**：输入时立即反馈
- **多层验证**：字段级、表单级、业务级
- **智能提示**：具体的错误信息和修复建议

### 数据管理
- **响应式状态**：使用Lit状态管理
- **不可变更新**：确保数据一致性
- **事件驱动**：松耦合的组件通信

### 用户体验
- **直观操作**：拖拽、点击、快捷键支持
- **视觉反馈**：加载状态、成功提示、错误高亮
- **无障碍性**：键盘导航、屏幕阅读器支持

## 📊 数据格式

### 完整配置结构
```typescript
interface RobotModelConfig {
  id: string
  name: string
  description: string
  basePrice: number
  materials: Material[]
  slots: ConfigurationSlot[]
  functions: RobotFunction[]
  constraints: ConstraintRule[]
}
```

### 物料定义
```typescript
interface Material {
  id: string
  name: string
  type: MaterialType
  category: MaterialCategory
  price: number
  specifications: Record<string, any>
  subMaterials: SubMaterial[]
  compatibilityRules: string[]
  description: string
}
```

### 约束规则
```typescript
interface ConstraintRule {
  id: string
  type: ConstraintType
  description: string
  params: Record<string, any>
}
```

## 🔧 扩展开发

### 添加新的约束类型

1. 在 `types.ts` 中扩展 `ConstraintType` 枚举
2. 在 `constraint-form.ts` 中添加对应的参数配置UI
3. 在约束求解器中实现验证逻辑

### 自定义表单组件

```typescript
@customElement('custom-form')
export class CustomForm extends LitElement {
  @property({ type: Object }) data?: any
  
  render() {
    return html`
      <!-- 自定义表单UI -->
    `
  }
  
  private save() {
    this.dispatchEvent(new CustomEvent('custom-save', {
      detail: this.data,
      bubbles: true
    }))
  }
}
```

### 添加新的验证规则

```typescript
export const CustomValidators = {
  // 自定义验证函数
  customRule: (value: any) => {
    if (!isValid(value)) {
      return '自定义错误信息'
    }
    return null
  }
}
```

## 🚦 最佳实践

### 1. ID命名规范
- 使用小写字母和下划线
- 保持简短但具有描述性
- 避免特殊字符和空格

### 2. 约束设计原则
- 尽量使用声明式约束而非命令式
- 保持约束的原子性和独立性
- 提供清晰的错误信息和修复建议

### 3. 数据组织策略
- 按功能模块组织物料和槽位
- 使用分层的约束关系
- 保持配置的可读性和可维护性

### 4. 性能优化
- 使用增量验证避免全量计算
- 缓存计算结果减少重复工作
- 延迟加载大型数据集

## 🧪 测试和调试

### Storybook故事
- `Default` - 基本功能演示
- `MaterialFormDemo` - 物料表单详细功能
- `ConstraintFormDemo` - 约束表单各类型配置
- `CompleteWorkflow` - 完整工作流演示

### 调试技巧
- 使用浏览器开发者工具查看组件状态
- 利用控制台输出验证配置数据
- 通过Storybook的Controls面板测试不同参数

## 📈 未来扩展

1. **可视化编辑器**：拖拽式约束关系设计
2. **模板系统**：预定义的配置模板
3. **协同编辑**：多用户同时编辑配置
4. **版本控制**：配置变更历史和回滚
5. **AI助手**：智能约束建议和优化

这个规则管理器为复杂的产品配置系统提供了完整的解决方案，可以适用于各种需要灵活配置和约束管理的业务场景。