import { LitElement, html, css, PropertyValues } from 'lit'
import { customElement, property, state } from 'lit/decorators.js'
import { 
  RuleManagerState, 
  MaterialFormData, 
  ConstraintFormData,
  SlotFormData,
  FunctionFormData,
  RobotModelFormData,
  FormEvent
} from './types'
import { MaterialType, ConstraintType, SlotType } from '../types'
import { materialCategories } from '../demo-data'
import { FormValidator, ValidationRules, SpecialValidators } from './validators'

@customElement('rule-manager')
export class RuleManager extends LitElement {
  @property({ type: Object }) initialData?: any
  
  @state() private state: RuleManagerState = {
    currentModel: null,
    materials: [],
    slots: [],
    functions: [],
    constraints: [],
    categories: materialCategories,
    activeTab: 'model',
    formState: {
      isValid: true,
      errors: {},
      touched: {}
    }
  }

  @state() private showMaterialForm: boolean = false
  @state() private editingMaterialIndex: number = -1
  @state() private showConstraintForm: boolean = false
  @state() private editingConstraintIndex: number = -1

  static styles = css`
    :host {
      display: block;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f8f9fa;
      min-height: 100vh;
    }

    .manager-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    .header {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      margin-bottom: 20px;
    }

    .tabs {
      display: flex;
      background: white;
      border-radius: 8px 8px 0 0;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      margin-bottom: 0;
    }

    .tab {
      padding: 12px 24px;
      cursor: pointer;
      border-bottom: 3px solid transparent;
      transition: all 0.2s ease;
      font-weight: 500;
    }

    .tab:hover {
      background: #f8f9fa;
    }

    .tab.active {
      border-bottom-color: #007acc;
      color: #007acc;
      background: #f0f8ff;
    }

    .content {
      background: white;
      padding: 24px;
      border-radius: 0 0 8px 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      min-height: 500px;
    }

    .form-section {
      margin-bottom: 24px;
    }

    .form-section h3 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #333;
      border-bottom: 1px solid #eee;
      padding-bottom: 8px;
    }

    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 16px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: 6px;
    }

    .form-group label {
      font-weight: 500;
      color: #333;
      font-size: 14px;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      transition: border-color 0.2s ease;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
      outline: none;
      border-color: #007acc;
      box-shadow: 0 0 0 2px rgba(0,122,204,0.1);
    }

    .form-group.error input,
    .form-group.error select,
    .form-group.error textarea {
      border-color: #dc3545;
    }

    .error-message {
      color: #dc3545;
      font-size: 12px;
      margin-top: 4px;
    }

    .required::after {
      content: '*';
      color: #dc3545;
      margin-left: 4px;
    }

    .checkbox-group {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 8px;
      margin-top: 8px;
    }

    .checkbox-item {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 13px;
    }

    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: background 0.2s ease;
    }

    .btn-primary {
      background: #007acc;
      color: white;
    }

    .btn-primary:hover {
      background: #005999;
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background: #545b62;
    }

    .btn-danger {
      background: #dc3545;
      color: white;
    }

    .btn-danger:hover {
      background: #c82333;
    }

    .actions {
      display: flex;
      gap: 12px;
      margin-top: 24px;
      padding-top: 16px;
      border-top: 1px solid #eee;
    }

    .list-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      border: 1px solid #eee;
      border-radius: 4px;
      margin-bottom: 8px;
      background: #fafbfc;
    }

    .list-item-info {
      flex: 1;
    }

    .list-item-title {
      font-weight: 500;
      color: #333;
    }

    .list-item-desc {
      font-size: 12px;
      color: #666;
      margin-top: 2px;
    }

    .list-item-actions {
      display: flex;
      gap: 8px;
    }

    .json-editor {
      min-height: 200px;
      font-family: 'Monaco', 'Consolas', monospace;
      font-size: 12px;
    }

    .preview-panel {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      padding: 16px;
      margin-top: 16px;
    }

    .preview-title {
      font-weight: 600;
      color: #333;
      margin-bottom: 12px;
    }

    .export-section {
      background: #e8f5e8;
      border-left: 4px solid #28a745;
      padding: 16px;
      border-radius: 4px;
      margin-top: 16px;
    }

    .form-overlay {
      background: #f8f9fa;
      border: 2px solid #007acc;
      border-radius: 8px;
      margin-top: 16px;
    }

    .form-overlay h3 {
      background: #007acc;
      color: white;
      margin: 0;
      padding: 12px 16px;
      border-radius: 6px 6px 0 0;
    }

    .form-overlay .form-content {
      padding: 16px;
    }
  `

  render() {
    return html`
      <div class="manager-container">
        <div class="header">
          <h1>机器人配置规则管理器</h1>
          <p>创建和管理机器人型号的物料、槽位、功能和约束规则</p>
        </div>

        <div class="tabs">
          ${this.renderTabs()}
        </div>

        <div class="content">
          ${this.renderContent()}
        </div>
      </div>
    `
  }

  private renderTabs() {
    const tabs = [
      { id: 'model', name: '机器人型号' },
      { id: 'materials', name: '物料管理' },
      { id: 'slots', name: '槽位配置' },
      { id: 'functions', name: '功能定义' },
      { id: 'constraints', name: '约束规则' },
      { id: 'export', name: '导出导入' }
    ]

    return tabs.map(tab => html`
      <div 
        class="tab ${this.state.activeTab === tab.id ? 'active' : ''}"
        @click=${() => this.switchTab(tab.id)}
      >
        ${tab.name}
      </div>
    `)
  }

  private renderContent() {
    switch (this.state.activeTab) {
      case 'model': return this.renderModelForm()
      case 'materials': return this.renderMaterialsTab()
      case 'slots': return this.renderSlotsTab()
      case 'functions': return this.renderFunctionsTab()
      case 'constraints': return this.renderConstraintsTab()
      case 'export': return this.renderExportTab()
      default: return html`<div>选择一个标签页</div>`
    }
  }

  private renderModelForm() {
    const model = this.state.currentModel || this.createEmptyModel()

    return html`
      <div class="form-section">
        <h3>基本信息</h3>
        <div class="form-grid">
          <div class="form-group">
            <label class="required">型号ID</label>
            <input 
              type="text" 
              .value=${model.id}
              @input=${(e: Event) => this.updateModel('id', (e.target as HTMLInputElement).value)}
              placeholder="例如: agv_standard_v1"
            />
            ${this.renderFieldError('id')}
          </div>

          <div class="form-group">
            <label class="required">型号名称</label>
            <input 
              type="text" 
              .value=${model.name}
              @input=${(e: Event) => this.updateModel('name', (e.target as HTMLInputElement).value)}
              placeholder="例如: AGV标准版 V1.0"
            />
            ${this.renderFieldError('name')}
          </div>

          <div class="form-group">
            <label class="required">基础价格</label>
            <input 
              type="number" 
              .value=${model.basePrice}
              @input=${(e: Event) => this.updateModel('basePrice', parseFloat((e.target as HTMLInputElement).value))}
              placeholder="0"
              min="0"
              step="100"
            />
            ${this.renderFieldError('basePrice')}
          </div>
        </div>

        <div class="form-group">
          <label class="required">描述</label>
          <textarea 
            .value=${model.description}
            @input=${(e: Event) => this.updateModel('description', (e.target as HTMLTextAreaElement).value)}
            placeholder="详细描述机器人型号的特点和用途"
            rows="3"
          ></textarea>
          ${this.renderFieldError('description')}
        </div>
      </div>

      <div class="actions">
        <button class="btn btn-primary" @click=${this.saveModel}>保存型号</button>
        <button class="btn btn-secondary" @click=${this.resetModel}>重置</button>
      </div>
    `
  }

  private renderMaterialsTab() {
    return html`
      <div class="form-section">
        <h3>物料列表</h3>
        ${this.state.materials.map((material, index) => this.renderMaterialItem(material, index))}
        
        <button class="btn btn-primary" @click=${this.addMaterial}>添加新物料</button>
      </div>

      ${this.renderMaterialForm()}
    `
  }

  private renderMaterialItem(material: MaterialFormData, index: number) {
    return html`
      <div class="list-item">
        <div class="list-item-info">
          <div class="list-item-title">${material.name} (${material.id})</div>
          <div class="list-item-desc">
            类型: ${material.type} | 价格: ¥${material.price} | ${material.description}
          </div>
        </div>
        <div class="list-item-actions">
          <button class="btn btn-secondary" @click=${() => this.editMaterial(index)}>编辑</button>
          <button class="btn btn-danger" @click=${() => this.deleteMaterial(index)}>删除</button>
        </div>
      </div>
    `
  }

  private renderMaterialForm() {
    if (!this.showMaterialForm) {
      return html`
        <div class="form-section">
          <h3>物料详情</h3>
          <p>点击"添加新物料"或"编辑"按钮来配置物料信息</p>
        </div>
      `
    }

    const material = this.editingMaterialIndex >= 0 
      ? this.state.materials[this.editingMaterialIndex] 
      : undefined

    const availableMaterialIds = this.state.materials.map(m => m.id)

    return html`
      <div class="form-overlay">
        <h3>${material ? '编辑物料' : '添加新物料'}</h3>
        <div class="form-content">
          <material-form 
            .material=${material}
            .availableMaterials=${availableMaterialIds}
            @material-save=${this.handleMaterialSave}
            @material-cancel=${this.handleMaterialCancel}
          ></material-form>
        </div>
      </div>
    `
  }

  private renderSlotsTab() {
    return html`
      <div class="form-section">
        <h3>槽位配置</h3>
        ${this.state.slots.map((slot, index) => this.renderSlotItem(slot, index))}
        
        <button class="btn btn-primary" @click=${this.addSlot}>添加新槽位</button>
      </div>
    `
  }

  private renderSlotItem(slot: SlotFormData, index: number) {
    return html`
      <div class="list-item">
        <div class="list-item-info">
          <div class="list-item-title">${slot.name} (${slot.id})</div>
          <div class="list-item-desc">
            类型: ${slot.type} | 数量: ${slot.minQuantity}-${slot.maxQuantity} | 
            ${slot.required ? '必选' : '可选'}
          </div>
        </div>
        <div class="list-item-actions">
          <button class="btn btn-secondary" @click=${() => this.editSlot(index)}>编辑</button>
          <button class="btn btn-danger" @click=${() => this.deleteSlot(index)}>删除</button>
        </div>
      </div>
    `
  }

  private renderFunctionsTab() {
    return html`
      <div class="form-section">
        <h3>功能定义</h3>
        ${this.state.functions.map((func, index) => this.renderFunctionItem(func, index))}
        
        <button class="btn btn-primary" @click=${this.addFunction}>添加新功能</button>
      </div>
    `
  }

  private renderFunctionItem(func: FunctionFormData, index: number) {
    return html`
      <div class="list-item">
        <div class="list-item-info">
          <div class="list-item-title">${func.name} (${func.id})</div>
          <div class="list-item-desc">${func.description}</div>
        </div>
        <div class="list-item-actions">
          <button class="btn btn-secondary" @click=${() => this.editFunction(index)}>编辑</button>
          <button class="btn btn-danger" @click=${() => this.deleteFunction(index)}>删除</button>
        </div>
      </div>
    `
  }

  private renderConstraintsTab() {
    return html`
      <div class="form-section">
        <h3>约束规则</h3>
        ${this.state.constraints.map((constraint, index) => this.renderConstraintItem(constraint, index))}
        
        <button class="btn btn-primary" @click=${this.addConstraint}>添加新约束</button>
      </div>

      ${this.renderConstraintForm()}
    `
  }

  private renderConstraintForm() {
    if (!this.showConstraintForm) {
      return html`
        <div class="form-section">
          <h3>约束详情</h3>
          <p>点击"添加新约束"或"编辑"按钮来配置约束规则</p>
        </div>
      `
    }

    const constraint = this.editingConstraintIndex >= 0 
      ? this.state.constraints[this.editingConstraintIndex] 
      : undefined

    const availableMaterialIds = this.state.materials.map(m => m.id)
    const availableSlotIds = this.state.slots.map(s => s.id)
    const availableFunctionIds = this.state.functions.map(f => f.id)

    return html`
      <div class="form-overlay">
        <h3>${constraint ? '编辑约束' : '添加新约束'}</h3>
        <div class="form-content">
          <constraint-form 
            .constraint=${constraint}
            .availableMaterials=${availableMaterialIds}
            .availableSlots=${availableSlotIds}
            .availableFunctions=${availableFunctionIds}
            @constraint-save=${this.handleConstraintSave}
            @constraint-cancel=${this.handleConstraintCancel}
          ></constraint-form>
        </div>
      </div>
    `
  }

  private renderConstraintItem(constraint: ConstraintFormData, index: number) {
    return html`
      <div class="list-item">
        <div class="list-item-info">
          <div class="list-item-title">${constraint.description} (${constraint.id})</div>
          <div class="list-item-desc">类型: ${constraint.type}</div>
        </div>
        <div class="list-item-actions">
          <button class="btn btn-secondary" @click=${() => this.editConstraint(index)}>编辑</button>
          <button class="btn btn-danger" @click=${() => this.deleteConstraint(index)}>删除</button>
        </div>
      </div>
    `
  }

  private renderExportTab() {
    const exportData = this.generateExportData()
    
    return html`
      <div class="form-section">
        <h3>导出配置</h3>
        <p>当前配置的完整数据，可以复制保存或导入到其他系统</p>
        
        <div class="export-section">
          <div class="preview-title">配置预览</div>
          <textarea 
            class="json-editor"
            readonly
            .value=${JSON.stringify(exportData, null, 2)}
          ></textarea>
        </div>

        <div class="actions">
          <button class="btn btn-primary" @click=${this.exportConfig}>下载配置文件</button>
          <button class="btn btn-secondary" @click=${this.copyToClipboard}>复制到剪贴板</button>
        </div>
      </div>

      <div class="form-section">
        <h3>导入配置</h3>
        <p>粘贴或选择配置文件来导入现有配置</p>
        
        <div class="form-group">
          <label>配置JSON数据</label>
          <textarea 
            class="json-editor"
            placeholder="粘贴配置JSON数据"
            @input=${this.handleImportInput}
          ></textarea>
        </div>

        <div class="actions">
          <input 
            type="file" 
            accept=".json"
            @change=${this.handleFileImport}
            style="display: none"
            id="file-import"
          />
          <button class="btn btn-secondary" @click=${() => document.getElementById('file-import')?.click()}>
            选择文件
          </button>
          <button class="btn btn-primary" @click=${this.importConfig}>导入配置</button>
        </div>
      </div>
    `
  }

  private renderFieldError(field: string) {
    const error = this.state.formState.errors[field]
    return error ? html`<div class="error-message">${error}</div>` : ''
  }

  // 事件处理方法
  private switchTab(tabId: string) {
    this.state = { ...this.state, activeTab: tabId }
  }

  private updateModel(field: keyof RobotModelFormData, value: any) {
    const currentModel = this.state.currentModel || this.createEmptyModel()
    this.state = {
      ...this.state,
      currentModel: { ...currentModel, [field]: value }
    }
  }

  private createEmptyModel(): RobotModelFormData {
    return {
      id: '',
      name: '',
      description: '',
      basePrice: 0,
      materials: [],
      slots: [],
      functions: [],
      constraints: []
    }
  }

  private saveModel() {
    if (!this.state.currentModel) return
    
    const validation = FormValidator.validate(this.state.currentModel, ValidationRules.model)
    
    if (validation.isValid) {
      // 保存成功逻辑
      this.dispatchEvent(new CustomEvent('model-saved', {
        detail: this.state.currentModel
      }))
    } else {
      this.state = {
        ...this.state,
        formState: {
          ...this.state.formState,
          errors: validation.errors
        }
      }
    }
  }

  private resetModel() {
    this.state = {
      ...this.state,
      currentModel: null,
      formState: { isValid: true, errors: {}, touched: {} }
    }
  }

  private addMaterial() {
    this.showMaterialForm = true
    this.editingMaterialIndex = -1
    this.requestUpdate()
  }

  private editMaterial(index: number) {
    this.showMaterialForm = true
    this.editingMaterialIndex = index
    this.requestUpdate()
  }

  private handleMaterialSave(event: CustomEvent) {
    const materialData = event.detail
    
    if (this.editingMaterialIndex >= 0) {
      // 编辑现有物料
      const updatedMaterials = [...this.state.materials]
      updatedMaterials[this.editingMaterialIndex] = materialData
      this.state = { ...this.state, materials: updatedMaterials }
    } else {
      // 添加新物料
      this.state = {
        ...this.state,
        materials: [...this.state.materials, materialData]
      }
    }
    
    // 关闭表单
    this.showMaterialForm = false
    this.editingMaterialIndex = -1
    this.requestUpdate()
  }

  private handleMaterialCancel() {
    this.showMaterialForm = false
    this.editingMaterialIndex = -1
    this.requestUpdate()
  }

  private deleteMaterial(index: number) {
    this.state = {
      ...this.state,
      materials: this.state.materials.filter((_, i) => i !== index)
    }
  }

  private addSlot() {
    // 添加新槽位逻辑
  }

  private editSlot(index: number) {
    // 编辑槽位逻辑
  }

  private deleteSlot(index: number) {
    this.state = {
      ...this.state,
      slots: this.state.slots.filter((_, i) => i !== index)
    }
  }

  private addFunction() {
    // 添加新功能逻辑
  }

  private editFunction(index: number) {
    // 编辑功能逻辑
  }

  private deleteFunction(index: number) {
    this.state = {
      ...this.state,
      functions: this.state.functions.filter((_, i) => i !== index)
    }
  }

  private addConstraint() {
    this.showConstraintForm = true
    this.editingConstraintIndex = -1
    this.requestUpdate()
  }

  private editConstraint(index: number) {
    this.showConstraintForm = true
    this.editingConstraintIndex = index
    this.requestUpdate()
  }

  private handleConstraintSave(event: CustomEvent) {
    const constraintData = event.detail
    
    if (this.editingConstraintIndex >= 0) {
      // 编辑现有约束
      const updatedConstraints = [...this.state.constraints]
      updatedConstraints[this.editingConstraintIndex] = constraintData
      this.state = { ...this.state, constraints: updatedConstraints }
    } else {
      // 添加新约束
      this.state = {
        ...this.state,
        constraints: [...this.state.constraints, constraintData]
      }
    }
    
    // 关闭表单
    this.showConstraintForm = false
    this.editingConstraintIndex = -1
    this.requestUpdate()
  }

  private handleConstraintCancel() {
    this.showConstraintForm = false
    this.editingConstraintIndex = -1
    this.requestUpdate()
  }

  private deleteConstraint(index: number) {
    this.state = {
      ...this.state,
      constraints: this.state.constraints.filter((_, i) => i !== index)
    }
    this.requestUpdate()
  }

  private generateExportData() {
    return {
      model: this.state.currentModel,
      materials: this.state.materials,
      slots: this.state.slots,
      functions: this.state.functions,
      constraints: this.state.constraints,
      exportTime: new Date().toISOString()
    }
  }

  private exportConfig() {
    const data = this.generateExportData()
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    const a = document.createElement('a')
    a.href = url
    a.download = `robot-config-${Date.now()}.json`
    a.click()
    
    URL.revokeObjectURL(url)
  }

  private async copyToClipboard() {
    const data = this.generateExportData()
    try {
      await navigator.clipboard.writeText(JSON.stringify(data, null, 2))
      // 显示成功提示
    } catch (error) {
      console.error('复制失败:', error)
    }
  }

  private handleImportInput(e: Event) {
    // 处理导入输入
  }

  private handleFileImport(e: Event) {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = () => {
        // 处理文件导入
      }
      reader.readAsText(file)
    }
  }

  private importConfig() {
    // 导入配置逻辑
  }
}