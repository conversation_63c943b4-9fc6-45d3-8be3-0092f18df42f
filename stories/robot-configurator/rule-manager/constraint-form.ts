import { LitElement, html, css } from 'lit'
import { customElement, property, state } from 'lit/decorators.js'
import { ConstraintFormData } from './types'
import { ConstraintType, MaterialType } from '../types'
import { FormValidator, ValidationRules } from './validators'

@customElement('constraint-form')
export class ConstraintForm extends LitElement {
  @property({ type: Object }) constraint?: ConstraintFormData
  @property({ type: Array }) availableMaterials: string[] = []
  @property({ type: Array }) availableSlots: string[] = []
  @property({ type: Array }) availableFunctions: string[] = []
  
  @state() private formData: ConstraintFormData = this.createEmptyConstraint()
  @state() private errors: Record<string, string> = {}

  static styles = css`
    :host {
      display: block;
    }

    .form-section {
      margin-bottom: 24px;
      padding: 16px;
      border: 1px solid #e1e5e9;
      border-radius: 6px;
      background: #fafbfc;
    }

    .form-section h4 {
      margin: 0 0 16px 0;
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }

    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: 6px;
    }

    .form-group label {
      font-weight: 500;
      color: #333;
      font-size: 13px;
    }

    .required::after {
      content: '*';
      color: #dc3545;
      margin-left: 4px;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      transition: border-color 0.2s ease;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
      outline: none;
      border-color: #007acc;
      box-shadow: 0 0 0 2px rgba(0,122,204,0.1);
    }

    .form-group.error input,
    .form-group.error select,
    .form-group.error textarea {
      border-color: #dc3545;
    }

    .error-message {
      color: #dc3545;
      font-size: 12px;
    }

    .param-section {
      background: white;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      padding: 16px;
      margin-top: 12px;
    }

    .param-title {
      font-weight: 500;
      color: #333;
      margin-bottom: 12px;
      font-size: 13px;
    }

    .material-list {
      display: grid;
      grid-template-columns: 1fr 150px 40px;
      gap: 8px;
      align-items: center;
      margin-bottom: 8px;
    }

    .material-list-header {
      display: grid;
      grid-template-columns: 1fr 150px 40px;
      gap: 8px;
      margin-bottom: 8px;
      font-weight: 500;
      font-size: 12px;
      color: #666;
    }

    .checkbox-group {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 8px;
    }

    .checkbox-item {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 13px;
    }

    .btn {
      padding: 6px 12px;
      border: none;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      transition: background 0.2s ease;
    }

    .btn-primary {
      background: #007acc;
      color: white;
    }

    .btn-primary:hover {
      background: #005999;
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-danger {
      background: #dc3545;
      color: white;
    }

    .btn-small {
      padding: 4px 8px;
      font-size: 11px;
    }

    .actions {
      display: flex;
      gap: 8px;
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid #dee2e6;
    }

    .json-textarea {
      min-height: 120px;
      font-family: 'Monaco', 'Consolas', monospace;
      font-size: 12px;
    }

    .help-text {
      font-size: 12px;
      color: #666;
      margin-top: 4px;
      line-height: 1.4;
    }

    .constraint-preview {
      background: #e8f5e8;
      border: 1px solid #c3e6cb;
      border-radius: 4px;
      padding: 12px;
      margin-top: 12px;
    }

    .preview-title {
      font-weight: 500;
      color: #155724;
      margin-bottom: 8px;
      font-size: 13px;
    }

    .preview-content {
      font-family: 'Monaco', 'Consolas', monospace;
      font-size: 11px;
      color: #155724;
      white-space: pre-wrap;
    }
  `

  connectedCallback() {
    super.connectedCallback()
    if (this.constraint) {
      this.formData = { ...this.constraint }
    }
  }

  render() {
    return html`
      <div class="form-section">
        <h4>基本信息</h4>
        <div class="form-grid">
          <div class="form-group ${this.errors.id ? 'error' : ''}">
            <label class="required">约束ID</label>
            <input 
              type="text" 
              .value=${this.formData.id}
              @input=${(e: Event) => this.updateField('id', (e.target as HTMLInputElement).value)}
              placeholder="例如: lidar_dependencies"
            />
            ${this.errors.id ? html`<div class="error-message">${this.errors.id}</div>` : ''}
          </div>

          <div class="form-group ${this.errors.type ? 'error' : ''}">
            <label class="required">约束类型</label>
            <select 
              .value=${this.formData.type}
              @change=${(e: Event) => this.updateField('type', (e.target as HTMLSelectElement).value)}
            >
              <option value="">请选择类型</option>
              ${Object.values(ConstraintType).map(type => html`
                <option value=${type} ?selected=${this.formData.type === type}>
                  ${this.getConstraintTypeName(type)}
                </option>
              `)}
            </select>
            ${this.errors.type ? html`<div class="error-message">${this.errors.type}</div>` : ''}
            <div class="help-text">${this.getConstraintTypeHelp(this.formData.type)}</div>
          </div>
        </div>

        <div class="form-group ${this.errors.description ? 'error' : ''}">
          <label class="required">约束描述</label>
          <textarea 
            .value=${this.formData.description}
            @input=${(e: Event) => this.updateField('description', (e.target as HTMLTextAreaElement).value)}
            placeholder="描述约束的具体内容和作用"
            rows="2"
          ></textarea>
          ${this.errors.description ? html`<div class="error-message">${this.errors.description}</div>` : ''}
        </div>
      </div>

      ${this.renderParametersSection()}

      ${this.renderPreview()}

      <div class="actions">
        <button class="btn btn-primary" @click=${this.save}>保存约束</button>
        <button class="btn btn-secondary" @click=${this.cancel}>取消</button>
        <button class="btn btn-secondary" @click=${this.reset}>重置</button>
      </div>
    `
  }

  private renderParametersSection() {
    if (!this.formData.type) {
      return html`
        <div class="form-section">
          <h4>参数配置</h4>
          <p style="color: #666;">请先选择约束类型</p>
        </div>
      `
    }

    return html`
      <div class="form-section">
        <h4>参数配置</h4>
        ${this.renderTypeSpecificParams()}
      </div>
    `
  }

  private renderTypeSpecificParams() {
    switch (this.formData.type) {
      case ConstraintType.MATERIAL_DEPENDENCY:
        return this.renderMaterialDependencyParams()
      
      case ConstraintType.MATERIAL_TYPE:
        return this.renderMaterialTypeParams()
      
      case ConstraintType.FUNCTIONAL:
        return this.renderFunctionalParams()
      
      case ConstraintType.QUANTITY:
        return this.renderQuantityParams()
      
      case ConstraintType.MUTUAL_EXCLUSIVE:
        return this.renderMutualExclusiveParams()
      
      default:
        return this.renderGenericParams()
    }
  }

  private renderMaterialDependencyParams() {
    const params = this.formData.params || {}
    const requiredMaterials = params.requiredMaterials || []

    return html`
      <div class="param-section">
        <div class="param-title">物料依赖配置</div>
        
        <div class="form-group">
          <label class="required">主物料ID</label>
          <select 
            .value=${params.primaryMaterialId || ''}
            @change=${(e: Event) => this.updateParam('primaryMaterialId', (e.target as HTMLSelectElement).value)}
          >
            <option value="">选择主物料</option>
            ${this.availableMaterials.map(id => html`
              <option value=${id}>${id}</option>
            `)}
          </select>
          <div class="help-text">选择这个物料时会触发依赖检查</div>
        </div>

        <div class="form-group">
          <label>必需的子物料</label>
          <div class="material-list-header">
            <div>物料ID</div>
            <div>显示名称</div>
            <div>操作</div>
          </div>
          ${requiredMaterials.map((material: any, index: number) => html`
            <div class="material-list">
              <select 
                .value=${material.materialId}
                @change=${(e: Event) => this.updateRequiredMaterial(index, 'materialId', (e.target as HTMLSelectElement).value)}
              >
                <option value="">选择物料</option>
                ${this.availableMaterials.map(id => html`
                  <option value=${id}>${id}</option>
                `)}
              </select>
              <input 
                type="text" 
                .value=${material.name || ''}
                @input=${(e: Event) => this.updateRequiredMaterial(index, 'name', (e.target as HTMLInputElement).value)}
                placeholder="显示名称"
              />
              <button class="btn btn-danger btn-small" @click=${() => this.removeRequiredMaterial(index)}>删除</button>
            </div>
          `)}
          <button class="btn btn-secondary btn-small" @click=${this.addRequiredMaterial}>添加子物料</button>
        </div>
      </div>
    `
  }

  private renderMaterialTypeParams() {
    const params = this.formData.params || {}
    const allowedTypes = params.allowedTypes || []

    return html`
      <div class="param-section">
        <div class="param-title">物料类型限制</div>
        
        <div class="form-group">
          <label class="required">目标槽位</label>
          <select 
            .value=${params.slotId || ''}
            @change=${(e: Event) => this.updateParam('slotId', (e.target as HTMLSelectElement).value)}
          >
            <option value="">选择槽位</option>
            ${this.availableSlots.map(id => html`
              <option value=${id}>${id}</option>
            `)}
          </select>
        </div>

        <div class="form-group">
          <label>允许的物料类型</label>
          <div class="checkbox-group">
            ${Object.values(MaterialType).map(type => html`
              <div class="checkbox-item">
                <input 
                  type="checkbox" 
                  .checked=${allowedTypes.includes(type)}
                  @change=${(e: Event) => this.toggleMaterialType(type, (e.target as HTMLInputElement).checked)}
                />
                <label>${this.getMaterialTypeName(type)}</label>
              </div>
            `)}
          </div>
        </div>

        <div class="form-grid">
          <div class="form-group">
            <label>最小数量</label>
            <input 
              type="number" 
              .value=${params.minQuantity || 0}
              @input=${(e: Event) => this.updateParam('minQuantity', parseInt((e.target as HTMLInputElement).value))}
              min="0"
            />
          </div>

          <div class="form-group">
            <label>最大数量</label>
            <input 
              type="number" 
              .value=${params.maxQuantity || 1}
              @input=${(e: Event) => this.updateParam('maxQuantity', parseInt((e.target as HTMLInputElement).value))}
              min="1"
            />
          </div>
        </div>
      </div>
    `
  }

  private renderFunctionalParams() {
    const params = this.formData.params || {}
    const requiredMaterialTypes = params.requiredMaterialTypes || []

    return html`
      <div class="param-section">
        <div class="param-title">功能约束配置</div>
        
        <div class="form-group">
          <label class="required">目标功能</label>
          <select 
            .value=${params.functionId || ''}
            @change=${(e: Event) => this.updateParam('functionId', (e.target as HTMLSelectElement).value)}
          >
            <option value="">选择功能</option>
            ${this.availableFunctions.map(id => html`
              <option value=${id}>${id}</option>
            `)}
          </select>
        </div>

        <div class="form-group">
          <label>必需的物料类型</label>
          <div class="checkbox-group">
            ${Object.values(MaterialType).map(type => html`
              <div class="checkbox-item">
                <input 
                  type="checkbox" 
                  .checked=${requiredMaterialTypes.includes(type)}
                  @change=${(e: Event) => this.toggleFunctionMaterialType(type, (e.target as HTMLInputElement).checked)}
                />
                <label>${this.getMaterialTypeName(type)}</label>
              </div>
            `)}
          </div>
          <div class="help-text">选择此功能时必须包含这些类型的物料</div>
        </div>
      </div>
    `
  }

  private renderQuantityParams() {
    const params = this.formData.params || {}

    return html`
      <div class="param-section">
        <div class="param-title">数量约束配置</div>
        
        <div class="form-group">
          <label>目标槽位</label>
          <select 
            .value=${params.slotId || ''}
            @change=${(e: Event) => this.updateParam('slotId', (e.target as HTMLSelectElement).value)}
          >
            <option value="">选择槽位</option>
            ${this.availableSlots.map(id => html`
              <option value=${id}>${id}</option>
            `)}
          </select>
        </div>

        <div class="form-grid">
          <div class="form-group">
            <label>最小数量</label>
            <input 
              type="number" 
              .value=${params.minQuantity || 0}
              @input=${(e: Event) => this.updateParam('minQuantity', parseInt((e.target as HTMLInputElement).value))}
              min="0"
            />
          </div>

          <div class="form-group">
            <label>最大数量</label>
            <input 
              type="number" 
              .value=${params.maxQuantity || 1}
              @input=${(e: Event) => this.updateParam('maxQuantity', parseInt((e.target as HTMLInputElement).value))}
              min="1"
            />
          </div>
        </div>
      </div>
    `
  }

  private renderMutualExclusiveParams() {
    const params = this.formData.params || {}
    const exclusiveMaterials = params.exclusiveMaterials || []

    return html`
      <div class="param-section">
        <div class="param-title">互斥约束配置</div>
        
        <div class="form-group">
          <label>互斥物料组</label>
          <div style="margin-bottom: 8px;">
            ${exclusiveMaterials.map((materialId: string, index: number) => html`
              <div style="display: flex; gap: 8px; align-items: center; margin-bottom: 4px;">
                <select 
                  .value=${materialId}
                  @change=${(e: Event) => this.updateExclusiveMaterial(index, (e.target as HTMLSelectElement).value)}
                >
                  <option value="">选择物料</option>
                  ${this.availableMaterials.map(id => html`
                    <option value=${id}>${id}</option>
                  `)}
                </select>
                <button class="btn btn-danger btn-small" @click=${() => this.removeExclusiveMaterial(index)}>删除</button>
              </div>
            `)}
          </div>
          <button class="btn btn-secondary btn-small" @click=${this.addExclusiveMaterial}>添加互斥物料</button>
          <div class="help-text">这些物料不能同时选择</div>
        </div>
      </div>
    `
  }

  private renderGenericParams() {
    return html`
      <div class="param-section">
        <div class="param-title">自定义参数</div>
        <div class="form-group">
          <label>参数配置 (JSON格式)</label>
          <textarea 
            class="json-textarea"
            .value=${JSON.stringify(this.formData.params || {}, null, 2)}
            @input=${this.updateGenericParams}
            placeholder='{\n  "key": "value"\n}'
          ></textarea>
          <div class="help-text">请输入有效的JSON格式参数</div>
        </div>
      </div>
    `
  }

  private renderPreview() {
    return html`
      <div class="constraint-preview">
        <div class="preview-title">约束预览</div>
        <div class="preview-content">${JSON.stringify(this.formData, null, 2)}</div>
      </div>
    `
  }

  private createEmptyConstraint(): ConstraintFormData {
    return {
      id: '',
      type: ConstraintType.MATERIAL_DEPENDENCY,
      description: '',
      params: {}
    }
  }

  private updateField(field: keyof ConstraintFormData, value: any) {
    this.formData = { ...this.formData, [field]: value }
    
    // 当类型改变时，重置参数
    if (field === 'type') {
      this.formData.params = {}
    }
    
    this.validateField(field)
  }

  private updateParam(key: string, value: any) {
    this.formData = {
      ...this.formData,
      params: { ...this.formData.params, [key]: value }
    }
  }

  private validateField(field: keyof ConstraintFormData) {
    const rules = ValidationRules.constraint
    const rule = rules[field as keyof typeof rules]
    
    if (rule) {
      const validation = FormValidator.validate({ [field]: this.formData[field] }, { [field]: rule })
      if (validation.errors[field as string]) {
        this.errors = { ...this.errors, [field]: validation.errors[field as string] }
      } else {
        const { [field as string]: removed, ...rest } = this.errors
        this.errors = rest
      }
      this.requestUpdate()
    }
  }

  // 物料依赖相关方法
  private addRequiredMaterial() {
    const params = this.formData.params || {}
    const requiredMaterials = params.requiredMaterials || []
    
    this.updateParam('requiredMaterials', [
      ...requiredMaterials,
      { materialId: '', name: '' }
    ])
  }

  private updateRequiredMaterial(index: number, field: string, value: string) {
    const params = this.formData.params || {}
    const requiredMaterials = [...(params.requiredMaterials || [])]
    requiredMaterials[index] = { ...requiredMaterials[index], [field]: value }
    this.updateParam('requiredMaterials', requiredMaterials)
  }

  private removeRequiredMaterial(index: number) {
    const params = this.formData.params || {}
    const requiredMaterials = (params.requiredMaterials || []).filter((_: any, i: number) => i !== index)
    this.updateParam('requiredMaterials', requiredMaterials)
  }

  // 物料类型相关方法
  private toggleMaterialType(type: MaterialType, checked: boolean) {
    const params = this.formData.params || {}
    let allowedTypes = [...(params.allowedTypes || [])]
    
    if (checked) {
      if (!allowedTypes.includes(type)) {
        allowedTypes.push(type)
      }
    } else {
      allowedTypes = allowedTypes.filter(t => t !== type)
    }
    
    this.updateParam('allowedTypes', allowedTypes)
  }

  private toggleFunctionMaterialType(type: MaterialType, checked: boolean) {
    const params = this.formData.params || {}
    let requiredMaterialTypes = [...(params.requiredMaterialTypes || [])]
    
    if (checked) {
      if (!requiredMaterialTypes.includes(type)) {
        requiredMaterialTypes.push(type)
      }
    } else {
      requiredMaterialTypes = requiredMaterialTypes.filter(t => t !== type)
    }
    
    this.updateParam('requiredMaterialTypes', requiredMaterialTypes)
  }

  // 互斥约束相关方法
  private addExclusiveMaterial() {
    const params = this.formData.params || {}
    const exclusiveMaterials = params.exclusiveMaterials || []
    this.updateParam('exclusiveMaterials', [...exclusiveMaterials, ''])
  }

  private updateExclusiveMaterial(index: number, value: string) {
    const params = this.formData.params || {}
    const exclusiveMaterials = [...(params.exclusiveMaterials || [])]
    exclusiveMaterials[index] = value
    this.updateParam('exclusiveMaterials', exclusiveMaterials)
  }

  private removeExclusiveMaterial(index: number) {
    const params = this.formData.params || {}
    const exclusiveMaterials = (params.exclusiveMaterials || []).filter((_: string, i: number) => i !== index)
    this.updateParam('exclusiveMaterials', exclusiveMaterials)
  }

  private updateGenericParams(e: Event) {
    const text = (e.target as HTMLTextAreaElement).value
    try {
      const params = JSON.parse(text)
      this.formData = { ...this.formData, params }
    } catch (error) {
      // JSON解析错误，暂时不更新
    }
  }

  private getConstraintTypeName(type: ConstraintType): string {
    const typeNames = {
      [ConstraintType.MATERIAL_DEPENDENCY]: '物料依赖约束',
      [ConstraintType.MATERIAL_TYPE]: '物料类型约束',
      [ConstraintType.FUNCTIONAL]: '功能约束',
      [ConstraintType.QUANTITY]: '数量约束',
      [ConstraintType.MUTUAL_EXCLUSIVE]: '互斥约束'
    }
    return typeNames[type] || type
  }

  private getConstraintTypeHelp(type: ConstraintType): string {
    const helpTexts = {
      [ConstraintType.MATERIAL_DEPENDENCY]: '当选择主物料时，自动要求选择相关的子物料',
      [ConstraintType.MATERIAL_TYPE]: '限制特定槽位只能选择特定类型的物料',
      [ConstraintType.FUNCTIONAL]: '当选择某个功能时，要求必须包含特定类型的物料',
      [ConstraintType.QUANTITY]: '限制槽位中物料的数量范围',
      [ConstraintType.MUTUAL_EXCLUSIVE]: '设置物料之间的互斥关系，不能同时选择'
    }
    return helpTexts[type] || ''
  }

  private getMaterialTypeName(type: MaterialType): string {
    const typeNames = {
      [MaterialType.SENSOR]: '传感器',
      [MaterialType.ACTUATOR]: '执行器',
      [MaterialType.CONTROLLER]: '控制器',
      [MaterialType.POWER]: '电源',
      [MaterialType.MECHANICAL]: '机械',
      [MaterialType.COMMUNICATION]: '通讯'
    }
    return typeNames[type] || type
  }

  private save() {
    const validation = FormValidator.validate(this.formData, ValidationRules.constraint)
    
    if (validation.isValid) {
      this.dispatchEvent(new CustomEvent('constraint-save', {
        detail: this.formData,
        bubbles: true
      }))
    } else {
      this.errors = validation.errors
      this.requestUpdate()
    }
  }

  private cancel() {
    this.dispatchEvent(new CustomEvent('constraint-cancel', { bubbles: true }))
  }

  private reset() {
    this.formData = this.constraint ? { ...this.constraint } : this.createEmptyConstraint()
    this.errors = {}
    this.requestUpdate()
  }
}