import type { Meta, StoryObj } from '@storybook/web-components'
import { html } from 'lit'
import './function-requirement-form'
import { MaterialType } from '../types'

const meta: Meta = {
  title: 'Business/FunctionRequirement',
  component: 'function-requirement-form',
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: `
# 功能物料要求配置器

专门用于配置功能对物料的复杂约束需求，支持4种约束模式和详细的槽位放置规范。

## 🎯 4种约束模式

### 1. 类别约束 (CATEGORY_ONLY)
- **适用场景**: 只关心物料类型，不限制具体款式
- **示例**: 导航功能需要任意传感器和控制器

### 2. 特定物料约束 (SPECIFIC_MATERIALS) 
- **适用场景**: 只允许使用经过验证的特定物料款式
- **示例**: 避障功能只能使用指定的激光雷达型号

### 3. 类别+黑名单约束 (CATEGORY_WITH_BLACKLIST)
- **适用场景**: 允许某类物料，但排除特定不兼容款式
- **示例**: 精确定位需要传感器，但不能使用相机

### 4. 灵活约束 (FLEXIBLE)
- **适用场景**: 基于类别的灵活配置，支持多种放置方案
- **示例**: 环境监测需要3个传感器，可分布在多个位置

## 🏗️ 槽位放置配置

每种约束都必须配置详细的安装位置：
- **槽位选择**: 指定具体的安装槽位
- **优先级**: 1-10评分，系统按优先级推荐
- **必需性**: 标记是否必须安装在特定位置
- **描述说明**: 详细的放置要求描述

## ✨ 智能特性

- 🎯 **实时预览**: 即时查看配置的JSON结构
- 🔄 **动态界面**: 根据约束类型动态显示配置选项
- ✅ **完整验证**: 确保配置数据的完整性和有效性
- 💡 **智能提示**: 提供详细的使用说明和最佳实践
        `
      }
    }
  },
  argTypes: {
    requirement: {
      control: false,
      description: '要编辑的物料要求数据'
    },
    availableMaterials: {
      control: false,
      description: '可用的物料列表'
    },
    availableSlots: {
      control: false,
      description: '可用的槽位列表'
    }
  }
}

export default meta
type Story = StoryObj

// 模拟数据
const mockMaterials = [
  { id: 'lidar_sick_lms111', name: 'SICK LMS111 激光雷达', type: MaterialType.SENSOR },
  { id: 'lidar_velodyne_vlp16', name: 'Velodyne VLP-16 3D激光雷达', type: MaterialType.SENSOR },
  { id: 'camera_realsense_d435', name: 'Intel RealSense D435 深度相机', type: MaterialType.SENSOR },
  { id: 'controller_raspberry_pi4', name: 'Raspberry Pi 4B 控制器', type: MaterialType.CONTROLLER },
  { id: 'controller_nvidia_xavier', name: 'NVIDIA Xavier NX 控制器', type: MaterialType.CONTROLLER },
  { id: 'battery_lithium_24v', name: '24V锂电池组', type: MaterialType.POWER },
  { id: 'power_adapter_24v', name: '24V电源适配器', type: MaterialType.POWER },
  { id: 'cable_ethernet_5m', name: '以太网线5米', type: MaterialType.COMMUNICATION },
  { id: 'sd_card_64gb', name: '64GB SD卡', type: MaterialType.MECHANICAL }
]

const mockSlots = [
  { id: 'main_controller', name: '主控制器', position: 'center' },
  { id: 'navigation_sensor', name: '主导航传感器', position: 'front' },
  { id: 'left_lidar_slot', name: '左侧激光雷达槽', position: 'left' },
  { id: 'right_lidar_slot', name: '右侧激光雷达槽', position: 'right' },
  { id: 'rear_sensor_slot', name: '后部传感器槽', position: 'rear' },
  { id: 'power_slot_1', name: '主电源槽', position: 'internal' },
  { id: 'power_slot_2', name: '备用电源槽', position: 'internal' },
  { id: 'accessory_slot_1', name: '配件槽1' },
  { id: 'accessory_slot_2', name: '配件槽2' },
  { id: 'accessory_slot_3', name: '配件槽3' }
]

export const CategoryOnlyDemo: Story = {
  name: '场景1: 类别约束演示',
  render: () => html`
    <div style="padding: 20px; background: #f8f9fa; min-height: 100vh;">
      <div style="max-width: 1000px; margin: 0 auto;">
        <div style="background: #e3f2fd; padding: 16px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #2196f3;">
          <h2 style="margin: 0 0 8px 0; color: #1976d2;">📂 场景1: 类别约束演示</h2>
          <p style="margin: 0; color: #1976d2;">
            <strong>适用场景</strong>: 功能只关心物料类型，不限制具体款式。如导航功能需要任意传感器和控制器。
          </p>
        </div>
        
        <function-requirement-form 
          .availableMaterials=${mockMaterials}
          .availableSlots=${mockSlots}
          @requirement-save=${(e: CustomEvent) => {
            console.log('保存物料要求:', e.detail)
            alert('物料要求已保存！查看控制台了解详情。')
          }}
          @requirement-cancel=${() => {
            console.log('取消编辑')
            alert('已取消编辑')
          }}
        ></function-requirement-form>
      </div>
    </div>
  `,
  parameters: {
    docs: {
      description: {
        story: `
演示类别约束模式的配置：

**配置要点：**
1. 选择"📂 类别约束"模式
2. 勾选需要的物料类型（如传感器、控制器）
3. 设置数量要求
4. 配置安装槽位规则

**典型用途：**
- 导航功能：需要任意传感器 + 任意控制器
- 基础移动：需要任意执行器 + 任意电源
- 通用功能：对物料品牌和型号没有特殊要求

**优势：**
- 最大的选择灵活性
- 成本优化空间大
- 供应链风险低
        `
      }
    }
  }
}

export const SpecificMaterialsDemo: Story = {
  name: '场景2: 特定物料约束演示',
  render: () => html`
    <div style="padding: 20px; background: #f8f9fa; min-height: 100vh;">
      <div style="max-width: 1000px; margin: 0 auto;">
        <div style="background: #fff3e0; padding: 16px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #ff9800;">
          <h2 style="margin: 0 0 8px 0; color: #f57c00;">🎯 场景2: 特定物料约束演示</h2>
          <p style="margin: 0; color: #f57c00;">
            <strong>适用场景</strong>: 只允许使用经过测试验证的特定物料款式。如高精度避障只能用指定激光雷达。
          </p>
        </div>
        
        <function-requirement-form 
          .availableMaterials=${mockMaterials}
          .availableSlots=${mockSlots}
          @requirement-save=${(e: CustomEvent) => {
            console.log('保存物料要求:', e.detail)
            alert('物料要求已保存！查看控制台了解详情。')
          }}
          @requirement-cancel=${() => {
            console.log('取消编辑')
            alert('已取消编辑')
          }}
        ></function-requirement-form>
      </div>
    </div>
  `,
  parameters: {
    docs: {
      description: {
        story: `
演示特定物料约束模式的配置：

**配置要点：**
1. 选择"🎯 特定物料"模式
2. 勾选允许的具体物料款式
3. 设置数量和安装位置

**典型用途：**
- 高精度功能：只能用经过标定的特定传感器
- 安全关键：只允许通过认证的特定控制器
- 专利限制：只能使用特定品牌和型号

**优势：**
- 最高的性能保证
- 严格的质量控制
- 明确的兼容性
        `
      }
    }
  }
}

export const CategoryWithBlacklistDemo: Story = {
  name: '场景3: 类别+黑名单约束演示',
  render: () => html`
    <div style="padding: 20px; background: #f8f9fa; min-height: 100vh;">
      <div style="max-width: 1000px; margin: 0 auto;">
        <div style="background: #fce4ec; padding: 16px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #e91e63;">
          <h2 style="margin: 0 0 8px 0; color: #c2185b;">🚫 场景3: 类别+黑名单约束演示</h2>
          <p style="margin: 0; color: #c2185b;">
            <strong>适用场景</strong>: 允许某类物料，但排除特定不兼容款式。如定位功能需要传感器但不能用相机。
          </p>
        </div>
        
        <function-requirement-form 
          .availableMaterials=${mockMaterials}
          .availableSlots=${mockSlots}
          @requirement-save=${(e: CustomEvent) => {
            console.log('保存物料要求:', e.detail)
            alert('物料要求已保存！查看控制台了解详情。')
          }}
          @requirement-cancel=${() => {
            console.log('取消编辑')
            alert('已取消编辑')
          }}
        ></function-requirement-form>
      </div>
    </div>
  `,
  parameters: {
    docs: {
      description: {
        story: `
演示类别+黑名单约束模式的配置：

**配置要点：**
1. 选择"🚫 类别+黑名单"模式
2. 先选择允许的物料类型
3. 再选择要排除的特定物料
4. 配置数量和位置要求

**典型用途：**
- 精确定位：需要传感器，但相机精度不够
- 环境适应：需要执行器，但某些型号不防水
- 成本控制：需要控制器，但排除过于昂贵的型号

**优势：**
- 在灵活性和控制性间平衡
- 避免已知的不兼容问题
- 保持合理的选择空间
        `
      }
    }
  }
}

export const FlexibleDemo: Story = {
  name: '场景4: 灵活约束演示',
  render: () => html`
    <div style="padding: 20px; background: #f8f9fa; min-height: 100vh;">
      <div style="max-width: 1000px; margin: 0 auto;">
        <div style="background: #e8f5e8; padding: 16px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #4caf50;">
          <h2 style="margin: 0 0 8px 0; color: #388e3c;">🔧 场景4: 灵活约束演示</h2>
          <p style="margin: 0; color: #388e3c;">
            <strong>适用场景</strong>: 基于类别的灵活配置，支持多种放置方案。如环境监测需要3个传感器，可分布在4个位置中的任意3个。
          </p>
        </div>
        
        <function-requirement-form 
          .availableMaterials=${mockMaterials}
          .availableSlots=${mockSlots}
          @requirement-save=${(e: CustomEvent) => {
            console.log('保存物料要求:', e.detail)
            alert('物料要求已保存！查看控制台了解详情。')
          }}
          @requirement-cancel=${() => {
            console.log('取消编辑')
            alert('已取消编辑')
          }}
        ></function-requirement-form>
      </div>
    </div>
  `,
  parameters: {
    docs: {
      description: {
        story: `
演示灵活约束模式的配置：

**配置要点：**
1. 选择"🔧 灵活约束"模式
2. 选择允许的物料类型
3. 设置需要的总数量
4. 配置多个可选槽位，设置不同优先级

**典型用途：**
- 传感器阵列：需要多个传感器，位置可灵活调整
- 冗余系统：需要多个相同功能的设备
- 分布式架构：组件可以分散部署

**配置示例：**
- 需要3个传感器
- 可安装在前、左、右、后4个位置
- 前方优先级最高，后方优先级最低
- 系统会智能推荐最佳组合

**优势：**
- 最大的部署灵活性
- 适应不同的安装环境
- 支持渐进式配置
        `
      }
    }
  }
}

export const CompleteWorkflowDemo: Story = {
  name: '完整配置工作流演示',
  render: () => html`
    <div style="padding: 20px; background: #f8f9fa; min-height: 100vh;">
      <div style="max-width: 1000px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 8px; margin-bottom: 20px; color: white;">
          <h2 style="margin: 0 0 12px 0;">🚀 完整配置工作流演示</h2>
          <p style="margin: 0; opacity: 0.9;">
            展示从零开始配置一个复杂功能的完整流程，包含多个物料要求和详细的槽位规划。
          </p>
        </div>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(240px, 1fr)); gap: 16px; margin-bottom: 24px;">
          <div style="background: white; padding: 16px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h4 style="margin: 0 0 8px 0; color: #333;">📋 配置步骤</h4>
            <ol style="margin: 0; padding-left: 16px; font-size: 13px; color: #666;">
              <li>填写基本信息</li>
              <li>选择约束类型</li>
              <li>配置物料规则</li>
              <li>设置安装槽位</li>
              <li>预览和保存</li>
            </ol>
          </div>
          <div style="background: white; padding: 16px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h4 style="margin: 0 0 8px 0; color: #333;">🎯 关键要素</h4>
            <ul style="margin: 0; padding-left: 16px; font-size: 13px; color: #666;">
              <li>数量 + 款式约束</li>
              <li>安装位置规划</li>
              <li>优先级设置</li>
              <li>必需性标记</li>
            </ul>
          </div>
          <div style="background: white; padding: 16px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h4 style="margin: 0 0 8px 0; color: #333;">✨ 智能特性</h4>
            <ul style="margin: 0; padding-left: 16px; font-size: 13px; color: #666;">
              <li>实时预览配置</li>
              <li>动态界面适配</li>
              <li>完整性验证</li>
              <li>最佳实践提示</li>
            </ul>
          </div>
        </div>
        
        <function-requirement-form 
          .availableMaterials=${mockMaterials}
          .availableSlots=${mockSlots}
          @requirement-save=${(e: CustomEvent) => {
            console.log('保存物料要求:', e.detail)
            alert('✅ 配置已保存！\n\n这个配置将用于约束用户的物料选择，确保功能的正确实现。\n\n查看控制台了解完整的配置数据。')
          }}
          @requirement-cancel=${() => {
            console.log('取消编辑')
            alert('已取消编辑')
          }}
        ></function-requirement-form>
      </div>
    </div>
  `,
  parameters: {
    docs: {
      description: {
        story: `
完整的功能约束配置工作流：

**实际业务场景示例：**

假设要配置"高精度定位"功能：
1. **基本信息**: ID为"dual_positioning_sensors"，名称"双侧定位传感器"，需要2个
2. **约束类型**: 选择"类别+黑名单"，允许传感器但排除相机
3. **物料配置**: 允许传感器类型，黑名单中添加不适合的相机
4. **槽位规则**: 
   - 左侧槽位：优先级10，必需
   - 右侧槽位：优先级10，必需
5. **预览**: 确认JSON配置正确

**生成的约束效果：**
- 用户选择高精度定位功能时
- 系统要求在左右两个槽位各安装一个传感器
- 只能选择激光雷达等，不能选择相机
- 系统会自动验证和提供修复建议

**技术优势：**
- 完整覆盖复杂业务场景
- 精确控制物料选择范围
- 详细的位置和数量约束
- 智能的用户体验优化
        `
      }
    }
  }
}