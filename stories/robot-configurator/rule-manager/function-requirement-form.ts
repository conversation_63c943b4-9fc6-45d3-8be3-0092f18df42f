import { LitElement, html, css } from 'lit'
import { customElement, property, state } from 'lit/decorators.js'
import { 
  MaterialRequirement, 
  MaterialConstraintType, 
  PlacementRule, 
  MaterialType 
} from '../types'

export interface MaterialRequirementFormData {
  id: string
  name: string
  description: string
  constraintType: MaterialConstraintType
  quantity: number
  allowedMaterialTypes?: MaterialType[]
  specificMaterialIds?: string[]
  blacklistMaterialIds?: string[]
  placements: PlacementRule[]
}

@customElement('function-requirement-form')
export class FunctionRequirementForm extends LitElement {
  @property({ type: Object }) requirement?: MaterialRequirementFormData
  @property({ type: Array }) availableMaterials: Array<{id: string, name: string, type: MaterialType}> = []
  @property({ type: Array }) availableSlots: Array<{id: string, name: string, position?: string}> = []
  
  @state() private formData: MaterialRequirementFormData = this.createEmptyRequirement()
  @state() private errors: Record<string, string> = {}

  static styles = css`
    :host {
      display: block;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .form-container {
      background: white;
      border-radius: 8px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .form-header {
      border-bottom: 2px solid #007acc;
      padding-bottom: 16px;
      margin-bottom: 24px;
    }

    .form-header h2 {
      margin: 0;
      color: #007acc;
      font-size: 18px;
      font-weight: 600;
    }

    .form-section {
      margin-bottom: 24px;
      padding: 16px;
      border: 1px solid #e1e5e9;
      border-radius: 6px;
      background: #fafbfc;
    }

    .form-section h3 {
      margin: 0 0 16px 0;
      font-size: 14px;
      font-weight: 600;
      color: #333;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .section-icon {
      width: 16px;
      height: 16px;
      background: #007acc;
      border-radius: 3px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 10px;
      font-weight: bold;
    }

    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 16px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: 6px;
    }

    .form-group label {
      font-weight: 500;
      color: #333;
      font-size: 13px;
    }

    .required::after {
      content: '*';
      color: #dc3545;
      margin-left: 4px;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
      padding: 10px 12px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 14px;
      transition: all 0.2s ease;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
      outline: none;
      border-color: #007acc;
      box-shadow: 0 0 0 3px rgba(0,122,204,0.1);
    }

    .form-group.error input,
    .form-group.error select,
    .form-group.error textarea {
      border-color: #dc3545;
    }

    .error-message {
      color: #dc3545;
      font-size: 12px;
    }

    .constraint-type-selector {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 12px;
      margin: 16px 0;
    }

    .constraint-type-option {
      border: 2px solid #e1e5e9;
      border-radius: 8px;
      padding: 16px;
      cursor: pointer;
      transition: all 0.2s ease;
      background: white;
    }

    .constraint-type-option:hover {
      border-color: #007acc;
      background: #f0f8ff;
    }

    .constraint-type-option.selected {
      border-color: #007acc;
      background: #f0f8ff;
      box-shadow: 0 0 0 2px rgba(0,122,204,0.1);
    }

    .constraint-type-title {
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
      font-size: 14px;
    }

    .constraint-type-desc {
      font-size: 12px;
      color: #666;
      line-height: 1.4;
    }

    .material-selector {
      border: 1px solid #dee2e6;
      border-radius: 6px;
      background: white;
      max-height: 200px;
      overflow-y: auto;
    }

    .material-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      border-bottom: 1px solid #f1f3f4;
      transition: background 0.2s ease;
    }

    .material-item:hover {
      background: #f8f9fa;
    }

    .material-item:last-child {
      border-bottom: none;
    }

    .material-info {
      flex: 1;
    }

    .material-name {
      font-weight: 500;
      color: #333;
      font-size: 13px;
    }

    .material-type {
      font-size: 11px;
      color: #666;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .placement-config {
      border: 1px solid #dee2e6;
      border-radius: 6px;
      background: white;
      margin-top: 12px;
    }

    .placement-header {
      background: #f8f9fa;
      padding: 12px;
      border-bottom: 1px solid #dee2e6;
      font-weight: 500;
      color: #333;
      font-size: 13px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .placement-item {
      padding: 16px;
      border-bottom: 1px solid #f1f3f4;
    }

    .placement-item:last-child {
      border-bottom: none;
    }

    .placement-grid {
      display: grid;
      grid-template-columns: 1fr 1fr 80px 80px auto;
      gap: 12px;
      align-items: end;
    }

    .priority-input {
      width: 60px;
    }

    .checkbox-group {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
      gap: 8px;
      margin-top: 12px;
    }

    .checkbox-item {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 13px;
      padding: 8px 12px;
      border: 1px solid #e1e5e9;
      border-radius: 4px;
      background: white;
      transition: all 0.2s ease;
    }

    .checkbox-item:hover {
      background: #f8f9fa;
      border-color: #007acc;
    }

    .checkbox-item input[type="checkbox"]:checked + label {
      font-weight: 500;
      color: #007acc;
    }

    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 6px;
      font-size: 13px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      display: inline-flex;
      align-items: center;
      gap: 6px;
    }

    .btn-primary {
      background: #007acc;
      color: white;
    }

    .btn-primary:hover {
      background: #005999;
      transform: translateY(-1px);
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background: #545b62;
    }

    .btn-danger {
      background: #dc3545;
      color: white;
    }

    .btn-small {
      padding: 6px 12px;
      font-size: 12px;
    }

    .actions {
      display: flex;
      gap: 12px;
      margin-top: 24px;
      padding-top: 16px;
      border-top: 1px solid #dee2e6;
      justify-content: flex-end;
    }

    .preview-panel {
      background: #e8f5e8;
      border: 1px solid #c3e6cb;
      border-radius: 6px;
      padding: 16px;
      margin-top: 16px;
    }

    .preview-title {
      font-weight: 600;
      color: #155724;
      margin-bottom: 12px;
      font-size: 13px;
    }

    .preview-content {
      font-family: 'Monaco', 'Consolas', monospace;
      font-size: 11px;
      color: #155724;
      white-space: pre-wrap;
      background: rgba(255,255,255,0.6);
      padding: 12px;
      border-radius: 4px;
    }

    .help-text {
      font-size: 12px;
      color: #666;
      margin-top: 4px;
      line-height: 1.4;
      font-style: italic;
    }

    .quantity-display {
      background: #007acc;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-weight: 600;
      font-size: 12px;
      display: inline-block;
    }
  `

  connectedCallback() {
    super.connectedCallback()
    if (this.requirement) {
      this.formData = { ...this.requirement }
    }
  }

  private createEmptyRequirement(): MaterialRequirementFormData {
    return {
      id: '',
      name: '',
      description: '',
      constraintType: MaterialConstraintType.CATEGORY_ONLY,
      quantity: 1,
      allowedMaterialTypes: [],
      specificMaterialIds: [],
      blacklistMaterialIds: [],
      placements: []
    }
  }

  private updateFormData(field: string, value: any) {
    this.formData = { ...this.formData, [field]: value }
    this.requestUpdate()
  }

  private addPlacement() {
    const newPlacement: PlacementRule = {
      slotId: this.availableSlots[0]?.id || '',
      priority: 5,
      required: false,
      description: ''
    }
    this.updateFormData('placements', [...this.formData.placements, newPlacement])
  }

  private removePlacement(index: number) {
    const placements = [...this.formData.placements]
    placements.splice(index, 1)
    this.updateFormData('placements', placements)
  }

  private updatePlacement(index: number, field: string, value: any) {
    const placements = [...this.formData.placements]
    placements[index] = { ...placements[index], [field]: value }
    this.updateFormData('placements', placements)
  }

  private toggleMaterialType(type: MaterialType) {
    const types = this.formData.allowedMaterialTypes || []
    const index = types.indexOf(type)
    const newTypes = index >= 0 
      ? types.filter(t => t !== type)
      : [...types, type]
    this.updateFormData('allowedMaterialTypes', newTypes)
  }

  private toggleSpecificMaterial(materialId: string) {
    const materials = this.formData.specificMaterialIds || []
    const index = materials.indexOf(materialId)
    const newMaterials = index >= 0
      ? materials.filter(m => m !== materialId)
      : [...materials, materialId]
    this.updateFormData('specificMaterialIds', newMaterials)
  }

  private toggleBlacklistMaterial(materialId: string) {
    const materials = this.formData.blacklistMaterialIds || []
    const index = materials.indexOf(materialId)
    const newMaterials = index >= 0
      ? materials.filter(m => m !== materialId)
      : [...materials, materialId]
    this.updateFormData('blacklistMaterialIds', newMaterials)
  }

  private handleSave() {
    const event = new CustomEvent('requirement-save', {
      detail: this.formData,
      bubbles: true,
      composed: true
    })
    this.dispatchEvent(event)
  }

  private handleCancel() {
    const event = new CustomEvent('requirement-cancel', {
      bubbles: true,
      composed: true
    })
    this.dispatchEvent(event)
  }

  render() {
    return html`
      <div class="form-container">
        <div class="form-header">
          <h2>🎯 功能物料要求配置</h2>
        </div>

        <!-- 基本信息 -->
        <div class="form-section">
          <h3><span class="section-icon">1</span>基本信息</h3>
          <div class="form-grid">
            <div class="form-group">
              <label class="required">要求ID</label>
              <input 
                type="text" 
                .value=${this.formData.id}
                @input=${(e: Event) => this.updateFormData('id', (e.target as HTMLInputElement).value)}
                placeholder="如: nav_main_sensor"
              />
              <div class="help-text">唯一标识，用于系统内部引用</div>
            </div>
            <div class="form-group">
              <label class="required">要求名称</label>
              <input 
                type="text" 
                .value=${this.formData.name}
                @input=${(e: Event) => this.updateFormData('name', (e.target as HTMLInputElement).value)}
                placeholder="如: 主导航传感器"
              />
            </div>
            <div class="form-group">
              <label class="required">数量要求</label>
              <input 
                type="number" 
                min="1" 
                .value=${this.formData.quantity}
                @input=${(e: Event) => this.updateFormData('quantity', (e.target as HTMLInputElement).valueAsNumber)}
              />
              <div class="help-text">该功能需要多少个此类物料</div>
            </div>
          </div>
          <div class="form-group">
            <label>详细描述</label>
            <textarea 
              rows="2"
              .value=${this.formData.description}
              @input=${(e: Event) => this.updateFormData('description', (e.target as HTMLTextAreaElement).value)}
              placeholder="描述这个物料要求的具体用途和作用"
            ></textarea>
          </div>
        </div>

        <!-- 约束类型选择 -->
        <div class="form-section">
          <h3><span class="section-icon">2</span>约束类型</h3>
          <div class="constraint-type-selector">
            <div 
              class="constraint-type-option ${this.formData.constraintType === MaterialConstraintType.CATEGORY_ONLY ? 'selected' : ''}"
              @click=${() => this.updateFormData('constraintType', MaterialConstraintType.CATEGORY_ONLY)}
            >
              <div class="constraint-type-title">📂 类别约束</div>
              <div class="constraint-type-desc">只限制物料类型，不限制具体款式。适用于对物料性能要求不严格的场景。</div>
            </div>
            <div 
              class="constraint-type-option ${this.formData.constraintType === MaterialConstraintType.SPECIFIC_MATERIALS ? 'selected' : ''}"
              @click=${() => this.updateFormData('constraintType', MaterialConstraintType.SPECIFIC_MATERIALS)}
            >
              <div class="constraint-type-title">🎯 特定物料</div>
              <div class="constraint-type-desc">只允许使用指定的特定物料款式。适用于有严格兼容性要求的场景。</div>
            </div>
            <div 
              class="constraint-type-option ${this.formData.constraintType === MaterialConstraintType.CATEGORY_WITH_BLACKLIST ? 'selected' : ''}"
              @click=${() => this.updateFormData('constraintType', MaterialConstraintType.CATEGORY_WITH_BLACKLIST)}
            >
              <div class="constraint-type-title">🚫 类别+黑名单</div>
              <div class="constraint-type-desc">允许某类物料，但排除特定不兼容款式。适用于大部分兼容但有例外的场景。</div>
            </div>
            <div 
              class="constraint-type-option ${this.formData.constraintType === MaterialConstraintType.FLEXIBLE ? 'selected' : ''}"
              @click=${() => this.updateFormData('constraintType', MaterialConstraintType.FLEXIBLE)}
            >
              <div class="constraint-type-title">🔧 灵活约束</div>
              <div class="constraint-type-desc">基于类别的灵活配置，支持多种放置方案。适用于多传感器阵列等场景。</div>
            </div>
          </div>
        </div>

        <!-- 物料配置 -->
        <div class="form-section">
          <h3><span class="section-icon">3</span>物料配置 <span class="quantity-display">需要 ${this.formData.quantity} 个</span></h3>
          
          ${this.renderMaterialConfig()}
        </div>

        <!-- 安装槽位配置 -->
        <div class="form-section">
          <h3><span class="section-icon">4</span>安装槽位规则</h3>
          <div class="placement-config">
            <div class="placement-header">
              <span>槽位放置规则 (${this.formData.placements.length} 个规则)</span>
              <button class="btn btn-primary btn-small" @click=${this.addPlacement}>
                ➕ 添加槽位
              </button>
            </div>
            ${this.formData.placements.length === 0 ? html`
              <div style="padding: 24px; text-align: center; color: #666;">
                <div style="font-size: 14px; margin-bottom: 8px;">🎯 还没有配置安装槽位</div>
                <div style="font-size: 12px;">请添加至少一个槽位规则来指定物料的安装位置</div>
              </div>
            ` : ''}
            ${this.formData.placements.map((placement, index) => html`
              <div class="placement-item">
                <div class="placement-grid">
                  <div class="form-group">
                    <label>目标槽位</label>
                    <select 
                      .value=${placement.slotId}
                      @change=${(e: Event) => this.updatePlacement(index, 'slotId', (e.target as HTMLSelectElement).value)}
                    >
                      ${this.availableSlots.map(slot => html`
                        <option value=${slot.id}>${slot.name} ${slot.position ? `(${slot.position})` : ''}</option>
                      `)}
                    </select>
                  </div>
                  <div class="form-group">
                    <label>放置描述</label>
                    <input 
                      type="text" 
                      .value=${placement.description}
                      @input=${(e: Event) => this.updatePlacement(index, 'description', (e.target as HTMLInputElement).value)}
                      placeholder="如: 必须安装在左侧"
                    />
                  </div>
                  <div class="form-group">
                    <label>优先级</label>
                    <input 
                      type="number" 
                      min="1" 
                      max="10" 
                      class="priority-input"
                      .value=${placement.priority}
                      @input=${(e: Event) => this.updatePlacement(index, 'priority', (e.target as HTMLInputElement).valueAsNumber)}
                    />
                  </div>
                  <div class="form-group">
                    <label>必需</label>
                    <input 
                      type="checkbox" 
                      .checked=${placement.required}
                      @change=${(e: Event) => this.updatePlacement(index, 'required', (e.target as HTMLInputElement).checked)}
                    />
                  </div>
                  <div>
                    <button class="btn btn-danger btn-small" @click=${() => this.removePlacement(index)}>
                      🗑️
                    </button>
                  </div>
                </div>
              </div>
            `)}
          </div>
          <div class="help-text">
            💡 优先级1-10，数字越大优先级越高。系统会按优先级推荐最佳安装方案。
          </div>
        </div>

        <!-- 预览 -->
        <div class="preview-panel">
          <div class="preview-title">📋 配置预览</div>
          <div class="preview-content">${JSON.stringify(this.formData, null, 2)}</div>
        </div>

        <!-- 操作按钮 -->
        <div class="actions">
          <button class="btn btn-secondary" @click=${this.handleCancel}>
            ❌ 取消
          </button>
          <button class="btn btn-primary" @click=${this.handleSave}>
            ✅ 保存配置
          </button>
        </div>
      </div>
    `
  }

  private renderMaterialConfig() {
    switch (this.formData.constraintType) {
      case MaterialConstraintType.CATEGORY_ONLY:
        return this.renderCategoryConfig()
      case MaterialConstraintType.SPECIFIC_MATERIALS:
        return this.renderSpecificMaterialsConfig()
      case MaterialConstraintType.CATEGORY_WITH_BLACKLIST:
        return this.renderCategoryWithBlacklistConfig()
      case MaterialConstraintType.FLEXIBLE:
        return this.renderFlexibleConfig()
      default:
        return html``
    }
  }

  private renderCategoryConfig() {
    return html`
      <div>
        <label><strong>允许的物料类型:</strong></label>
        <div class="checkbox-group">
          ${Object.values(MaterialType).map(type => html`
            <div class="checkbox-item">
              <input 
                type="checkbox" 
                id="type-${type}"
                .checked=${this.formData.allowedMaterialTypes?.includes(type) || false}
                @change=${() => this.toggleMaterialType(type)}
              />
              <label for="type-${type}">${this.getTypeDisplayName(type)}</label>
            </div>
          `)}
        </div>
        <div class="help-text">
          选择允许的物料类型。用户可以在这些类型中任意选择具体款式。
        </div>
      </div>
    `
  }

  private renderSpecificMaterialsConfig() {
    return html`
      <div>
        <label><strong>允许的特定物料:</strong></label>
        <div class="material-selector">
          ${this.availableMaterials.map(material => html`
            <div class="material-item">
              <input 
                type="checkbox"
                .checked=${this.formData.specificMaterialIds?.includes(material.id) || false}
                @change=${() => this.toggleSpecificMaterial(material.id)}
              />
              <div class="material-info">
                <div class="material-name">${material.name}</div>
                <div class="material-type">${this.getTypeDisplayName(material.type)}</div>
              </div>
            </div>
          `)}
        </div>
        <div class="help-text">
          只允许使用选中的特定物料款式。这种模式适用于有严格兼容性要求的功能。
        </div>
      </div>
    `
  }

  private renderCategoryWithBlacklistConfig() {
    return html`
      <div>
        <label><strong>允许的物料类型:</strong></label>
        <div class="checkbox-group">
          ${Object.values(MaterialType).map(type => html`
            <div class="checkbox-item">
              <input 
                type="checkbox" 
                id="allow-type-${type}"
                .checked=${this.formData.allowedMaterialTypes?.includes(type) || false}
                @change=${() => this.toggleMaterialType(type)}
              />
              <label for="allow-type-${type}">${this.getTypeDisplayName(type)}</label>
            </div>
          `)}
        </div>
        
        <div style="margin-top: 16px;">
          <label><strong>黑名单物料 (排除以下款式):</strong></label>
          <div class="material-selector">
            ${this.availableMaterials.map(material => html`
              <div class="material-item">
                <input 
                  type="checkbox"
                  .checked=${this.formData.blacklistMaterialIds?.includes(material.id) || false}
                  @change=${() => this.toggleBlacklistMaterial(material.id)}
                />
                <div class="material-info">
                  <div class="material-name">${material.name}</div>
                  <div class="material-type">${this.getTypeDisplayName(material.type)}</div>
                </div>
              </div>
            `)}
          </div>
        </div>
        <div class="help-text">
          允许某类物料，但排除选中的特定款式。适用于大部分物料都兼容，但有少数例外的场景。
        </div>
      </div>
    `
  }

  private renderFlexibleConfig() {
    return html`
      <div>
        <label><strong>允许的物料类型:</strong></label>
        <div class="checkbox-group">
          ${Object.values(MaterialType).map(type => html`
            <div class="checkbox-item">
              <input 
                type="checkbox" 
                id="flex-type-${type}"
                .checked=${this.formData.allowedMaterialTypes?.includes(type) || false}
                @change=${() => this.toggleMaterialType(type)}
              />
              <label for="flex-type-${type}">${this.getTypeDisplayName(type)}</label>
            </div>
          `)}
        </div>
        <div class="help-text">
          灵活约束模式，重点在于数量和位置配置。用户可以在允许的类型中自由选择，系统会根据槽位规则进行智能推荐。
        </div>
      </div>
    `
  }

  private getTypeDisplayName(type: MaterialType): string {
    const typeNames = {
      [MaterialType.SENSOR]: '传感器',
      [MaterialType.ACTUATOR]: '执行器', 
      [MaterialType.CONTROLLER]: '控制器',
      [MaterialType.POWER]: '电源',
      [MaterialType.MECHANICAL]: '机械',
      [MaterialType.COMMUNICATION]: '通讯'
    }
    return typeNames[type] || type
  }
}

declare global {
  interface HTMLElementTagNameMap {
    'function-requirement-form': FunctionRequirementForm
  }
}