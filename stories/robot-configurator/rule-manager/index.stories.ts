import type { Meta, StoryObj } from '@storybook/web-components'
import { html } from 'lit'
import './components'
import './material-form'
import './constraint-form'
import { demoRobotModel } from '../demo-data'

const meta: Meta = {
  title: 'Business/RuleManager',
  component: 'rule-manager',
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: `
# 机器人配置规则管理器

这是一个完整的规则录入和管理系统，用于创建和维护机器人配置的各类规则。

## 主要功能

### 📝 规则类型管理
1. **机器人型号** - 基本信息配置
2. **物料管理** - 物料信息、规格、依赖关系
3. **槽位配置** - 配置槽位类型、约束条件
4. **功能定义** - 机器人功能和实现方案
5. **约束规则** - 各类业务约束逻辑
6. **导出导入** - 配置数据的导入导出

### 🔧 约束类型支持
- **物料依赖约束** - 主物料带子物料
- **物料类型约束** - 槽位物料类型限制
- **功能约束** - 功能实现的物料要求
- **数量约束** - 槽位数量限制
- **互斥约束** - 物料互斥关系

### ✅ 表单验证
- 实时验证用户输入
- 智能错误提示
- 数据完整性检查
- JSON格式验证

### 💾 数据管理
- 完整的CRUD操作
- 配置数据导出
- JSON格式导入
- 数据预览和验证

## 使用场景

这个规则管理器适用于：
- 产品配置器的规则录入
- 复杂约束关系的管理
- 业务规则的可视化配置
- 配置数据的批量管理
        `
      }
    }
  },
  argTypes: {
    initialData: {
      control: false,
      description: '初始化数据'
    }
  }
}

export default meta
type Story = StoryObj

export const Default: Story = {
  name: '规则管理器',
  render: () => html`
    <rule-manager></rule-manager>
  `,
  parameters: {
    docs: {
      source: {
        code: `
// 基本使用
<rule-manager></rule-manager>

// 带初始数据
<rule-manager .initialData=\${existingConfig}></rule-manager>
        `
      }
    }
  }
}

export const WithInitialData: Story = {
  name: '带初始数据',
  render: () => html`
    <rule-manager .initialData=${demoRobotModel}></rule-manager>
  `,
  parameters: {
    docs: {
      description: {
        story: '展示如何使用现有配置数据初始化规则管理器。'
      }
    }
  }
}

export const MaterialFormDemo: Story = {
  name: '物料表单演示',
  render: () => html`
    <div style="padding: 20px; background: #f8f9fa; min-height: 100vh;">
      <div style="max-width: 800px; margin: 0 auto;">
        <h1 style="margin-bottom: 20px;">物料表单演示</h1>
        <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <material-form 
            .availableMaterials=${['cable_ethernet_5m', 'power_adapter_24v', 'sd_card_64gb']}
            @material-save=${(e: CustomEvent) => {
              console.log('保存物料:', e.detail)
              alert('物料已保存！查看控制台了解详情。')
            }}
            @material-cancel=${() => {
              console.log('取消编辑')
            }}
          ></material-form>
        </div>
      </div>
    </div>
  `,
  parameters: {
    docs: {
      description: {
        story: `
演示物料表单的所有功能：

**功能特点：**
- 完整的物料信息录入
- 动态规格参数配置
- 子物料依赖关系设置
- 兼容性规则配置
- 实时表单验证
- 数据保存和取消操作

**表单字段：**
- 基本信息：ID、名称、类型、分类、价格
- 规格参数：可动态添加键值对
- 子物料：选择依赖的其他物料
- 兼容性规则：文本列表格式
        `
      }
    }
  }
}

export const ConstraintFormDemo: Story = {
  name: '约束表单演示',
  render: () => html`
    <div style="padding: 20px; background: #f8f9fa; min-height: 100vh;">
      <div style="max-width: 900px; margin: 0 auto;">
        <h1 style="margin-bottom: 20px;">约束规则表单演示</h1>
        <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <constraint-form 
            .availableMaterials=${['lidar_sick_lms111', 'cable_ethernet_5m', 'power_adapter_24v']}
            .availableSlots=${['main_controller', 'navigation_sensor', 'accessories']}
            .availableFunctions=${['navigation', 'obstacle_avoidance']}
            @constraint-save=${(e: CustomEvent) => {
              console.log('保存约束:', e.detail)
              alert('约束规则已保存！查看控制台了解详情。')
            }}
            @constraint-cancel=${() => {
              console.log('取消编辑')
            }}
          ></constraint-form>
        </div>
      </div>
    </div>
  `,
  parameters: {
    docs: {
      description: {
        story: `
演示约束规则表单的所有功能：

**约束类型：**
1. **物料依赖约束** - 配置主物料和必需的子物料
2. **物料类型约束** - 设置槽位允许的物料类型和数量
3. **功能约束** - 配置功能实现需要的物料类型
4. **数量约束** - 设置槽位的数量限制
5. **互斥约束** - 配置不能同时选择的物料

**智能特性：**
- 根据约束类型动态显示参数配置
- 实时预览约束规则的JSON结构
- 支持复杂参数的可视化配置
- 完整的表单验证和错误提示
        `
      }
    }
  }
}

export const CompleteWorkflow: Story = {
  name: '完整工作流演示',
  render: () => html`
    <div style="padding: 20px; background: #f5f5f5; min-height: 100vh;">
      <div style="max-width: 1200px; margin: 0 auto;">
        <h1 style="text-align: center; margin-bottom: 30px; color: #333;">
          规则管理器 - 完整工作流演示
        </h1>
        
        <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <h2 style="color: #007acc; margin-top: 0;">使用流程</h2>
          <ol style="line-height: 1.6; color: #666;">
            <li><strong>机器人型号配置</strong>：设置基本信息（名称、价格、描述）</li>
            <li><strong>物料管理</strong>：录入所有可用物料，包括规格和依赖关系</li>
            <li><strong>槽位配置</strong>：定义配置槽位的类型和约束条件</li>
            <li><strong>功能定义</strong>：配置机器人功能和实现方案</li>
            <li><strong>约束规则</strong>：设置各类业务约束逻辑</li>
            <li><strong>导出配置</strong>：保存完整的配置数据用于生产环境</li>
          </ol>
        </div>

        <rule-manager 
          @model-saved=${(e: CustomEvent) => {
            console.log('型号已保存:', e.detail)
            // 这里可以添加保存到服务器的逻辑
          }}
        ></rule-manager>
      </div>
    </div>
  `,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        story: `
展示规则管理器的完整使用流程：

**工作流程：**
1. **项目初始化** - 创建新的机器人型号项目
2. **物料库建设** - 录入所有可用的物料信息
3. **架构设计** - 定义槽位结构和功能需求
4. **约束配置** - 设置业务规则和约束逻辑
5. **测试验证** - 预览和验证配置的正确性
6. **部署应用** - 导出配置用于生产环境

**企业级特性：**
- 完整的数据验证和错误处理
- 支持大规模物料库管理
- 灵活的约束规则配置
- 标准化的数据导入导出
- 可扩展的插件架构
        `
      }
    }
  }
}