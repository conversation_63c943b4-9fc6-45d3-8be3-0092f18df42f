// 规则管理器模块导出

// 导出类型定义
export * from './types'

// 导出验证器
export * from './validators'

// 导出组件
export * from './components'
export * from './material-form'
export * from './constraint-form'

// 导出工具函数
export const RuleManagerUtils = {
  /**
   * 创建空的机器人型号配置
   */
  createEmptyRobotModel() {
    return {
      id: '',
      name: '',
      description: '',
      basePrice: 0,
      materials: [],
      slots: [],
      functions: [],
      constraints: []
    }
  },

  /**
   * 验证配置数据的完整性
   */
  validateConfiguration(config: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!config.id) errors.push('缺少型号ID')
    if (!config.name) errors.push('缺少型号名称')
    if (typeof config.basePrice !== 'number' || config.basePrice < 0) {
      errors.push('基础价格必须是非负数')
    }

    // 检查物料ID唯一性
    const materialIds = config.materials?.map((m: any) => m.id) || []
    const duplicateMaterials = materialIds.filter((id: string, index: number) => 
      materialIds.indexOf(id) !== index
    )
    if (duplicateMaterials.length > 0) {
      errors.push(`物料ID重复: ${duplicateMaterials.join(', ')}`)
    }

    // 检查槽位ID唯一性
    const slotIds = config.slots?.map((s: any) => s.id) || []
    const duplicateSlots = slotIds.filter((id: string, index: number) => 
      slotIds.indexOf(id) !== index
    )
    if (duplicateSlots.length > 0) {
      errors.push(`槽位ID重复: ${duplicateSlots.join(', ')}`)
    }

    // 检查约束引用的有效性
    config.constraints?.forEach((constraint: any, index: number) => {
      if (constraint.type === 'material_dependency') {
        const { primaryMaterialId, requiredMaterials } = constraint.params || {}
        
        if (primaryMaterialId && !materialIds.includes(primaryMaterialId)) {
          errors.push(`约束${index + 1}: 主物料ID "${primaryMaterialId}" 不存在`)
        }
        
        requiredMaterials?.forEach((reqMat: any) => {
          if (reqMat.materialId && !materialIds.includes(reqMat.materialId)) {
            errors.push(`约束${index + 1}: 必需物料ID "${reqMat.materialId}" 不存在`)
          }
        })
      }
      
      if (constraint.type === 'material_type' || constraint.type === 'quantity') {
        const { slotId } = constraint.params || {}
        if (slotId && !slotIds.includes(slotId)) {
          errors.push(`约束${index + 1}: 槽位ID "${slotId}" 不存在`)
        }
      }
    })

    return {
      isValid: errors.length === 0,
      errors
    }
  },

  /**
   * 将表单数据转换为标准配置格式
   */
  convertToStandardFormat(formData: any) {
    return {
      id: formData.id,
      name: formData.name,
      description: formData.description,
      basePrice: formData.basePrice,
      slots: formData.slots || [],
      availableFunctions: formData.functions || [],
      materials: formData.materials || [],
      constraintRules: formData.constraints || []
    }
  },

  /**
   * 从标准配置格式转换为表单数据
   */
  convertFromStandardFormat(standardConfig: any) {
    return {
      id: standardConfig.id || '',
      name: standardConfig.name || '',
      description: standardConfig.description || '',
      basePrice: standardConfig.basePrice || 0,
      materials: standardConfig.materials || [],
      slots: standardConfig.slots || [],
      functions: standardConfig.availableFunctions || [],
      constraints: standardConfig.constraintRules || []
    }
  },

  /**
   * 生成配置统计信息
   */
  generateStats(config: any) {
    return {
      materialCount: config.materials?.length || 0,
      slotCount: config.slots?.length || 0,
      functionCount: config.functions?.length || 0,
      constraintCount: config.constraints?.length || 0,
      requiredSlots: config.slots?.filter((s: any) => s.required).length || 0,
      optionalSlots: config.slots?.filter((s: any) => !s.required).length || 0,
      materialTypes: [...new Set(config.materials?.map((m: any) => m.type) || [])],
      constraintTypes: [...new Set(config.constraints?.map((c: any) => c.type) || [])]
    }
  },

  /**
   * 导出配置为JSON文件
   */
  exportAsJSON(config: any, filename?: string) {
    const exportData = {
      ...config,
      exportTime: new Date().toISOString(),
      version: '1.0.0'
    }
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
      type: 'application/json' 
    })
    const url = URL.createObjectURL(blob)
    
    const a = document.createElement('a')
    a.href = url
    a.download = filename || `robot-config-${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    
    URL.revokeObjectURL(url)
  },

  /**
   * 从JSON文件导入配置
   */
  async importFromJSON(file: File): Promise<any> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (event) => {
        try {
          const config = JSON.parse(event.target?.result as string)
          
          // 基本格式验证
          if (!config.id || !config.name) {
            throw new Error('配置文件格式不正确：缺少必要字段')
          }
          
          resolve(config)
        } catch (error) {
          reject(new Error(`配置文件解析失败: ${error}`))
        }
      }
      
      reader.onerror = () => {
        reject(new Error('文件读取失败'))
      }
      
      reader.readAsText(file)
    })
  },

  /**
   * 生成配置摘要
   */
  generateSummary(config: any): string {
    const stats = this.generateStats(config)
    
    return `
配置摘要：
- 型号：${config.name} (${config.id})
- 基础价格：¥${config.basePrice?.toLocaleString() || 0}
- 物料数量：${stats.materialCount}
- 槽位数量：${stats.slotCount} (必选: ${stats.requiredSlots}, 可选: ${stats.optionalSlots})
- 功能数量：${stats.functionCount}
- 约束数量：${stats.constraintCount}
- 物料类型：${stats.materialTypes.join(', ')}
- 约束类型：${stats.constraintTypes.join(', ')}
    `.trim()
  }
}

// 使用示例注释
/*
使用示例：

import { RuleManager, MaterialForm, ConstraintForm, RuleManagerUtils } from './rule-manager'

// 1. 基本使用
<rule-manager></rule-manager>

// 2. 带初始数据
<rule-manager .initialData=${existingConfig}></rule-manager>

// 3. 单独使用物料表单
<material-form 
  .availableMaterials=${materialIds}
  @material-save=${handleSave}
></material-form>

// 4. 单独使用约束表单
<constraint-form 
  .availableMaterials=${materialIds}
  .availableSlots=${slotIds}
  @constraint-save=${handleSave}
></constraint-form>

// 5. 使用工具函数
const validation = RuleManagerUtils.validateConfiguration(config)
if (!validation.isValid) {
  console.error('配置错误:', validation.errors)
}

// 6. 导出配置
RuleManagerUtils.exportAsJSON(config, 'my-robot-config.json')

// 7. 导入配置
const config = await RuleManagerUtils.importFromJSON(file)

// 8. 生成统计信息
const stats = RuleManagerUtils.generateStats(config)
console.log('配置统计:', stats)
*/