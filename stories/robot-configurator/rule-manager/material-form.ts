import { LitElement, html, css } from 'lit'
import { customElement, property, state } from 'lit/decorators.js'
import { MaterialFormData, SubMaterialFormData } from './types'
import { MaterialType } from '../types'
import { materialCategories } from '../demo-data'
import { FormValidator, ValidationRules } from './validators'

@customElement('material-form')
export class MaterialForm extends LitElement {
  @property({ type: Object }) material?: MaterialFormData
  @property({ type: Array }) availableMaterials: string[] = []
  
  @state() private formData: MaterialFormData = this.createEmptyMaterial()
  @state() private errors: Record<string, string> = {}

  static styles = css`
    :host {
      display: block;
    }

    .form-section {
      margin-bottom: 24px;
      padding: 16px;
      border: 1px solid #e1e5e9;
      border-radius: 6px;
      background: #fafbfc;
    }

    .form-section h4 {
      margin: 0 0 16px 0;
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }

    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: 6px;
    }

    .form-group label {
      font-weight: 500;
      color: #333;
      font-size: 13px;
    }

    .required::after {
      content: '*';
      color: #dc3545;
      margin-left: 4px;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      transition: border-color 0.2s ease;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
      outline: none;
      border-color: #007acc;
      box-shadow: 0 0 0 2px rgba(0,122,204,0.1);
    }

    .form-group.error input,
    .form-group.error select,
    .form-group.error textarea {
      border-color: #dc3545;
    }

    .error-message {
      color: #dc3545;
      font-size: 12px;
    }

    .sub-materials {
      border: 1px solid #dee2e6;
      border-radius: 4px;
      padding: 16px;
      background: white;
    }

    .sub-material-item {
      display: grid;
      grid-template-columns: 1fr 150px 80px 60px 40px;
      gap: 8px;
      align-items: end;
      margin-bottom: 12px;
      padding: 8px;
      background: #f8f9fa;
      border-radius: 4px;
    }

    .sub-material-header {
      display: grid;
      grid-template-columns: 1fr 150px 80px 60px 40px;
      gap: 8px;
      margin-bottom: 8px;
      font-weight: 500;
      font-size: 12px;
      color: #666;
    }

    .specifications {
      border: 1px solid #dee2e6;
      border-radius: 4px;
      padding: 16px;
      background: white;
    }

    .spec-item {
      display: grid;
      grid-template-columns: 150px 1fr 40px;
      gap: 8px;
      align-items: center;
      margin-bottom: 8px;
    }

    .btn {
      padding: 6px 12px;
      border: none;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      transition: background 0.2s ease;
    }

    .btn-primary {
      background: #007acc;
      color: white;
    }

    .btn-primary:hover {
      background: #005999;
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-danger {
      background: #dc3545;
      color: white;
    }

    .btn-small {
      padding: 4px 8px;
      font-size: 11px;
    }

    .actions {
      display: flex;
      gap: 8px;
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid #dee2e6;
    }

    .json-textarea {
      min-height: 100px;
      font-family: 'Monaco', 'Consolas', monospace;
      font-size: 12px;
    }
  `

  connectedCallback() {
    super.connectedCallback()
    if (this.material) {
      this.formData = { ...this.material }
    }
  }

  render() {
    return html`
      <div class="form-section">
        <h4>基本信息</h4>
        <div class="form-grid">
          <div class="form-group ${this.errors.id ? 'error' : ''}">
            <label class="required">物料ID</label>
            <input 
              type="text" 
              .value=${this.formData.id}
              @input=${(e: Event) => this.updateField('id', (e.target as HTMLInputElement).value)}
              placeholder="例如: lidar_sick_lms111"
            />
            ${this.errors.id ? html`<div class="error-message">${this.errors.id}</div>` : ''}
          </div>

          <div class="form-group ${this.errors.name ? 'error' : ''}">
            <label class="required">物料名称</label>
            <input 
              type="text" 
              .value=${this.formData.name}
              @input=${(e: Event) => this.updateField('name', (e.target as HTMLInputElement).value)}
              placeholder="例如: SICK LMS111激光雷达"
            />
            ${this.errors.name ? html`<div class="error-message">${this.errors.name}</div>` : ''}
          </div>

          <div class="form-group ${this.errors.type ? 'error' : ''}">
            <label class="required">物料类型</label>
            <select 
              .value=${this.formData.type}
              @change=${(e: Event) => this.updateField('type', (e.target as HTMLSelectElement).value)}
            >
              <option value="">请选择类型</option>
              ${Object.values(MaterialType).map(type => html`
                <option value=${type} ?selected=${this.formData.type === type}>
                  ${this.getTypeName(type)}
                </option>
              `)}
            </select>
            ${this.errors.type ? html`<div class="error-message">${this.errors.type}</div>` : ''}
          </div>

          <div class="form-group ${this.errors.categoryId ? 'error' : ''}">
            <label class="required">物料分类</label>
            <select 
              .value=${this.formData.categoryId}
              @change=${(e: Event) => this.updateField('categoryId', (e.target as HTMLSelectElement).value)}
            >
              <option value="">请选择分类</option>
              ${materialCategories.map(category => html`
                <option value=${category.id} ?selected=${this.formData.categoryId === category.id}>
                  ${category.name}
                </option>
              `)}
            </select>
            ${this.errors.categoryId ? html`<div class="error-message">${this.errors.categoryId}</div>` : ''}
          </div>

          <div class="form-group ${this.errors.price ? 'error' : ''}">
            <label class="required">价格（元）</label>
            <input 
              type="number" 
              .value=${this.formData.price}
              @input=${(e: Event) => this.updateField('price', parseFloat((e.target as HTMLInputElement).value))}
              placeholder="0"
              min="0"
              step="0.01"
            />
            ${this.errors.price ? html`<div class="error-message">${this.errors.price}</div>` : ''}
          </div>
        </div>

        <div class="form-group">
          <label>描述</label>
          <textarea 
            .value=${this.formData.description}
            @input=${(e: Event) => this.updateField('description', (e.target as HTMLTextAreaElement).value)}
            placeholder="物料的详细描述"
            rows="2"
          ></textarea>
        </div>
      </div>

      <div class="form-section">
        <h4>规格参数</h4>
        <div class="specifications">
          ${this.renderSpecifications()}
          <button class="btn btn-secondary btn-small" @click=${this.addSpecification}>
            添加规格
          </button>
        </div>
      </div>

      <div class="form-section">
        <h4>子物料依赖</h4>
        <div class="sub-materials">
          ${this.renderSubMaterials()}
          <button class="btn btn-secondary btn-small" @click=${this.addSubMaterial}>
            添加子物料
          </button>
        </div>
      </div>

      <div class="form-section">
        <h4>兼容性规则</h4>
        <div class="form-group">
          <label>兼容性规则列表（每行一个）</label>
          <textarea 
            .value=${this.formData.compatibilityRules.join('\n')}
            @input=${this.updateCompatibilityRules}
            placeholder="requires_ethernet_port&#10;provides_usb3_port"
            rows="3"
          ></textarea>
        </div>
      </div>

      <div class="actions">
        <button class="btn btn-primary" @click=${this.save}>保存物料</button>
        <button class="btn btn-secondary" @click=${this.cancel}>取消</button>
        <button class="btn btn-secondary" @click=${this.reset}>重置</button>
      </div>
    `
  }

  private renderSpecifications() {
    const specs = Object.entries(this.formData.specifications)
    
    return html`
      ${specs.map(([key, value], index) => html`
        <div class="spec-item">
          <input 
            type="text" 
            .value=${key}
            @input=${(e: Event) => this.updateSpecKey(index, (e.target as HTMLInputElement).value)}
            placeholder="规格名称"
          />
          <input 
            type="text" 
            .value=${value}
            @input=${(e: Event) => this.updateSpecValue(key, (e.target as HTMLInputElement).value)}
            placeholder="规格值"
          />
          <button class="btn btn-danger btn-small" @click=${() => this.removeSpec(key)}>删除</button>
        </div>
      `)}
    `
  }

  private renderSubMaterials() {
    if (this.formData.subMaterials.length === 0) {
      return html`<p style="color: #666; font-size: 13px;">暂无子物料依赖</p>`
    }

    return html`
      <div class="sub-material-header">
        <div>物料ID</div>
        <div>名称</div>
        <div>数量</div>
        <div>必需</div>
        <div>操作</div>
      </div>
      ${this.formData.subMaterials.map((subMaterial, index) => html`
        <div class="sub-material-item">
          <select 
            .value=${subMaterial.materialId}
            @change=${(e: Event) => this.updateSubMaterial(index, 'materialId', (e.target as HTMLSelectElement).value)}
          >
            <option value="">选择物料</option>
            ${this.availableMaterials.map(id => html`
              <option value=${id} ?selected=${subMaterial.materialId === id}>${id}</option>
            `)}
          </select>
          
          <input 
            type="text" 
            .value=${subMaterial.name}
            @input=${(e: Event) => this.updateSubMaterial(index, 'name', (e.target as HTMLInputElement).value)}
            placeholder="子物料名称"
          />
          
          <input 
            type="number" 
            .value=${subMaterial.quantity}
            @input=${(e: Event) => this.updateSubMaterial(index, 'quantity', parseInt((e.target as HTMLInputElement).value))}
            min="1"
          />
          
          <input 
            type="checkbox" 
            .checked=${subMaterial.required}
            @change=${(e: Event) => this.updateSubMaterial(index, 'required', (e.target as HTMLInputElement).checked)}
          />
          
          <button class="btn btn-danger btn-small" @click=${() => this.removeSubMaterial(index)}>删除</button>
        </div>
      `)}
    `
  }

  private createEmptyMaterial(): MaterialFormData {
    return {
      id: '',
      name: '',
      categoryId: '',
      type: MaterialType.SENSOR,
      price: 0,
      specifications: {},
      subMaterials: [],
      compatibilityRules: [],
      description: ''
    }
  }

  private updateField(field: keyof MaterialFormData, value: any) {
    this.formData = { ...this.formData, [field]: value }
    this.validateField(field)
  }

  private validateField(field: keyof MaterialFormData) {
    const rules = ValidationRules.material
    const rule = rules[field as keyof typeof rules]
    
    if (rule) {
      const validation = FormValidator.validate({ [field]: this.formData[field] }, { [field]: rule })
      if (validation.errors[field as string]) {
        this.errors = { ...this.errors, [field]: validation.errors[field as string] }
      } else {
        const { [field as string]: removed, ...rest } = this.errors
        this.errors = rest
      }
      this.requestUpdate()
    }
  }

  private addSpecification() {
    const key = `spec_${Object.keys(this.formData.specifications).length + 1}`
    this.formData = {
      ...this.formData,
      specifications: { ...this.formData.specifications, [key]: '' }
    }
  }

  private updateSpecKey(index: number, newKey: string) {
    const entries = Object.entries(this.formData.specifications)
    const [oldKey, value] = entries[index]
    
    const newSpecs = { ...this.formData.specifications }
    delete newSpecs[oldKey]
    newSpecs[newKey] = value
    
    this.formData = { ...this.formData, specifications: newSpecs }
  }

  private updateSpecValue(key: string, value: string) {
    this.formData = {
      ...this.formData,
      specifications: { ...this.formData.specifications, [key]: value }
    }
  }

  private removeSpec(key: string) {
    const { [key]: removed, ...rest } = this.formData.specifications
    this.formData = { ...this.formData, specifications: rest }
  }

  private addSubMaterial() {
    const newSubMaterial: SubMaterialFormData = {
      materialId: '',
      name: '',
      required: true,
      quantity: 1
    }
    
    this.formData = {
      ...this.formData,
      subMaterials: [...this.formData.subMaterials, newSubMaterial]
    }
  }

  private updateSubMaterial(index: number, field: keyof SubMaterialFormData, value: any) {
    const updated = [...this.formData.subMaterials]
    updated[index] = { ...updated[index], [field]: value }
    this.formData = { ...this.formData, subMaterials: updated }
  }

  private removeSubMaterial(index: number) {
    this.formData = {
      ...this.formData,
      subMaterials: this.formData.subMaterials.filter((_, i) => i !== index)
    }
  }

  private updateCompatibilityRules(e: Event) {
    const text = (e.target as HTMLTextAreaElement).value
    const rules = text.split('\n').filter(rule => rule.trim()).map(rule => rule.trim())
    this.formData = { ...this.formData, compatibilityRules: rules }
  }

  private getTypeName(type: MaterialType): string {
    const typeNames = {
      [MaterialType.SENSOR]: '传感器',
      [MaterialType.ACTUATOR]: '执行器',
      [MaterialType.CONTROLLER]: '控制器',
      [MaterialType.POWER]: '电源',
      [MaterialType.MECHANICAL]: '机械',
      [MaterialType.COMMUNICATION]: '通讯'
    }
    return typeNames[type] || type
  }

  private save() {
    const validation = FormValidator.validate(this.formData, ValidationRules.material)
    
    if (validation.isValid) {
      this.dispatchEvent(new CustomEvent('material-save', {
        detail: this.formData,
        bubbles: true
      }))
    } else {
      this.errors = validation.errors
      this.requestUpdate()
    }
  }

  private cancel() {
    this.dispatchEvent(new CustomEvent('material-cancel', { bubbles: true }))
  }

  private reset() {
    this.formData = this.material ? { ...this.material } : this.createEmptyMaterial()
    this.errors = {}
    this.requestUpdate()
  }
}