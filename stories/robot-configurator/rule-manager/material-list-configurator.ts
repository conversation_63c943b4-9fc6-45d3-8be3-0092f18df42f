import { LitElement, html, css } from 'lit'
import { customElement, property, state } from 'lit/decorators.js'
import { 
  MaterialType, 
  Material,
  ConfigurationSlot
} from '../types'

export interface MaterialListItem {
  id: string
  materialType: MaterialType
  quantity: number
  specificMaterialId?: string  // 如果指定了具体物料
  slotIds?: string[]          // 安装槽位（支持多选）
  required: boolean           // 是否必须
  description: string         // 描述
}

export interface MaterialTypeGroup {
  type: MaterialType
  name: string
  expanded: boolean
  quantity: number
  maxQuantity: number
  items: MaterialListItem[]
}

@customElement('material-list-configurator')
export class MaterialListConfigurator extends LitElement {
  @property({ type: Array }) availableMaterials: Material[] = []
  @property({ type: Array }) availableSlots: ConfigurationSlot[] = []
  @property({ type: String }) functionName: string = ''
  
  @state() private materialGroups: MaterialTypeGroup[] = []
  
  static styles = css`
    :host {
      display: block;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .configurator-container {
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px;
    }

    .header h2 {
      margin: 0 0 8px 0;
      font-size: 20px;
      font-weight: 600;
    }

    .header p {
      margin: 0;
      opacity: 0.9;
      font-size: 14px;
    }

    .material-table {
      width: 100%;
      border-collapse: collapse;
    }

    .table-header {
      background: #f8f9fa;
      border-bottom: 2px solid #dee2e6;
    }

    .table-header th {
      padding: 12px;
      text-align: left;
      font-weight: 600;
      color: #333;
      font-size: 13px;
      border-right: 1px solid #dee2e6;
    }

    .table-header th:last-child {
      border-right: none;
    }

    .type-row {
      background: #e3f2fd;
      cursor: pointer;
      transition: background 0.2s ease;
    }

    .type-row:hover {
      background: #bbdefb;
    }

    .type-row.expanded {
      background: #1976d2;
      color: white;
    }

    .type-cell {
      padding: 12px;
      font-weight: 600;
      border-right: 1px solid #dee2e6;
      position: relative;
    }

    .expand-icon {
      display: inline-block;
      width: 20px;
      text-align: center;
      transition: transform 0.2s ease;
      margin-right: 8px;
    }

    .expanded .expand-icon {
      transform: rotate(90deg);
    }

    .type-name {
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }

    .type-badge {
      background: rgba(255,255,255,0.2);
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 500;
    }

    .quantity-input {
      width: 60px;
      padding: 4px 8px;
      border: 1px solid #ccc;
      border-radius: 4px;
      text-align: center;
      font-size: 13px;
    }

    .type-row.expanded .quantity-input {
      background: white;
      color: #333;
    }

    .item-row {
      border-bottom: 1px solid #f1f3f4;
      transition: background 0.2s ease;
    }

    .item-row:hover {
      background: #f8f9fa;
    }

    .item-cell {
      padding: 8px 12px;
      border-right: 1px solid #f1f3f4;
      font-size: 13px;
    }

    .item-cell:last-child {
      border-right: none;
    }

    .item-index {
      color: #666;
      font-weight: 500;
      width: 40px;
    }

    .material-selector {
      width: 100%;
      padding: 4px 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 13px;
      background: white;
    }

    .material-selector:focus {
      outline: none;
      border-color: #007acc;
      box-shadow: 0 0 0 2px rgba(0,122,204,0.1);
    }

    .material-selector option[value=""] {
      color: #999;
      font-style: italic;
    }

    .slot-selector {
      width: 100%;
      padding: 4px 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 13px;
    }

    .status-indicator {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 6px;
    }

    .status-pending {
      background: #ffc107;
    }

    .status-specified {
      background: #28a745;
    }

    .status-required {
      background: #dc3545;
    }

    .item-description {
      font-size: 12px;
      color: #666;
      margin-top: 2px;
    }

    .no-items {
      text-align: center;
      padding: 24px;
      color: #999;
      font-style: italic;
    }

    .summary-panel {
      background: #f8f9fa;
      border-top: 1px solid #dee2e6;
      padding: 16px;
    }

    .summary-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
    }

    .summary-item {
      background: white;
      padding: 12px;
      border-radius: 6px;
      border-left: 4px solid #007acc;
    }

    .summary-label {
      font-size: 12px;
      color: #666;
      margin-bottom: 4px;
    }

    .summary-value {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .actions {
      padding: 16px;
      border-top: 1px solid #dee2e6;
      background: #f8f9fa;
      display: flex;
      gap: 12px;
      justify-content: flex-end;
    }

    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .btn-primary {
      background: #007acc;
      color: white;
    }

    .btn-primary:hover {
      background: #005999;
      transform: translateY(-1px);
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-export {
      background: #28a745;
      color: white;
    }

    .help-text {
      font-size: 12px;
      color: #666;
      margin-top: 8px;
      padding: 8px 12px;
      background: #e3f2fd;
      border-radius: 4px;
      border-left: 3px solid #2196f3;
    }

    .slot-multi-selector {
      display: flex;
      flex-direction: column;
      gap: 8px;
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #ddd;
      border-radius: 6px;
      padding: 8px;
      background: white;
    }

    .slot-option {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 6px 8px;
      border-radius: 4px;
      cursor: pointer;
      transition: background 0.2s ease;
      font-size: 13px;
    }

    .slot-option:hover {
      background: #f0f8ff;
    }

    .slot-option.selected {
      background: #e3f2fd;
      border: 1px solid #2196f3;
    }

    .slot-option input[type="checkbox"] {
      margin: 0;
    }

    .slot-name {
      flex: 1;
    }

    .slot-position {
      font-size: 11px;
      color: #666;
      background: #f5f5f5;
      padding: 2px 6px;
      border-radius: 10px;
    }

    .selected-slots {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
      margin-top: 4px;
    }

    .slot-tag {
      background: #e3f2fd;
      color: #1976d2;
      padding: 2px 6px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 500;
    }

    .no-slots-selected {
      color: #999;
      font-style: italic;
      font-size: 12px;
    }
  `

  connectedCallback() {
    super.connectedCallback()
    this.initializeMaterialGroups()
  }

  private initializeMaterialGroups() {
    this.materialGroups = Object.values(MaterialType).map(type => ({
      type,
      name: this.getTypeDisplayName(type),
      expanded: false,
      quantity: 0,
      maxQuantity: 10,
      items: []
    }))
  }

  private getTypeDisplayName(type: MaterialType): string {
    const typeNames = {
      [MaterialType.SENSOR]: '传感器',
      [MaterialType.ACTUATOR]: '执行器',
      [MaterialType.CONTROLLER]: '控制器',
      [MaterialType.POWER]: '电源',
      [MaterialType.MECHANICAL]: '机械',
      [MaterialType.COMMUNICATION]: '通讯'
    }
    return typeNames[type] || type
  }

  private toggleGroup(groupIndex: number) {
    const group = this.materialGroups[groupIndex]
    group.expanded = !group.expanded
    this.requestUpdate()
  }

  private updateQuantity(groupIndex: number, quantity: number) {
    const group = this.materialGroups[groupIndex]
    const oldQuantity = group.quantity
    group.quantity = Math.max(0, Math.min(quantity, group.maxQuantity))
    
    // 调整items数量
    if (group.quantity > oldQuantity) {
      // 增加items
      for (let i = oldQuantity; i < group.quantity; i++) {
        group.items.push({
          id: `${group.type}_item_${i + 1}`,
          materialType: group.type,
          quantity: 1,
          required: false,
          description: `${group.name} #${i + 1}`
        })
      }
    } else if (group.quantity < oldQuantity) {
      // 减少items
      group.items = group.items.slice(0, group.quantity)
    }
    
    this.requestUpdate()
  }

  private updateItemMaterial(groupIndex: number, itemIndex: number, materialId: string) {
    const group = this.materialGroups[groupIndex]
    const item = group.items[itemIndex]
    
    if (materialId) {
      item.specificMaterialId = materialId
      const material = this.availableMaterials.find(m => m.id === materialId)
      item.description = material?.name || item.description
    } else {
      item.specificMaterialId = undefined
      item.description = `${group.name} #${itemIndex + 1}`
    }
    
    this.requestUpdate()
  }

  private toggleItemSlot(groupIndex: number, itemIndex: number, slotId: string) {
    const group = this.materialGroups[groupIndex]
    const item = group.items[itemIndex]
    
    if (!item.slotIds) {
      item.slotIds = []
    }
    
    const index = item.slotIds.indexOf(slotId)
    if (index >= 0) {
      // 如果已存在，则移除
      item.slotIds.splice(index, 1)
    } else {
      // 如果不存在，则添加
      item.slotIds.push(slotId)
    }
    
    this.requestUpdate()
  }
  
  private isSlotSelected(item: MaterialListItem, slotId: string): boolean {
    return item.slotIds?.includes(slotId) || false
  }

  private toggleRequired(groupIndex: number, itemIndex: number) {
    const group = this.materialGroups[groupIndex]
    const item = group.items[itemIndex]
    item.required = !item.required
    this.requestUpdate()
  }

  private getItemStatus(item: MaterialListItem): 'pending' | 'specified' | 'required' {
    if (item.required) return 'required'
    if (item.specificMaterialId) return 'specified'
    return 'pending'
  }

  private getAvailableMaterialsForType(type: MaterialType): Material[] {
    return this.availableMaterials.filter(m => m.type === type)
  }

  private exportConfiguration() {
    const config = {
      functionName: this.functionName,
      materialRequirements: this.materialGroups
        .filter(group => group.quantity > 0)
        .map(group => ({
          materialType: group.type,
          items: group.items.map(item => ({
            id: item.id,
            specificMaterialId: item.specificMaterialId,
            slotIds: item.slotIds || [],
            required: item.required,
            description: item.description
          }))
        }))
    }
    
    console.log('导出配置:', config)
    
    const event = new CustomEvent('configuration-export', {
      detail: config,
      bubbles: true,
      composed: true
    })
    this.dispatchEvent(event)
  }

  private getTotalItems(): number {
    return this.materialGroups.reduce((total, group) => total + group.quantity, 0)
  }

  private getSpecifiedItems(): number {
    return this.materialGroups.reduce((total, group) => 
      total + group.items.filter(item => item.specificMaterialId).length, 0)
  }

  private getRequiredItems(): number {
    return this.materialGroups.reduce((total, group) => 
      total + group.items.filter(item => item.required).length, 0)
  }

  render() {
    return html`
      <div class="configurator-container">
        <div class="header">
          <h2>📋 物料清单配置器</h2>
          <p>为功能"${this.functionName}"配置物料需求，按类型展开设置数量和具体物料</p>
        </div>

        <table class="material-table">
          <thead class="table-header">
            <tr>
              <th style="width: 200px;">物料类型</th>
              <th style="width: 80px;">数量</th>
              <th style="width: 250px;">具体物料</th>
              <th style="width: 180px;">安装槽位</th>
              <th style="width: 80px;">必需</th>
              <th>说明</th>
            </tr>
          </thead>
          <tbody>
            ${this.materialGroups.map((group, groupIndex) => html`
              <!-- 类型行 -->
              <tr class="type-row ${group.expanded ? 'expanded' : ''}" 
                  @click=${() => this.toggleGroup(groupIndex)}>
                <td class="type-cell">
                  <div class="type-name">
                    <span class="expand-icon">▶</span>
                    ${group.name}
                    ${group.quantity > 0 ? html`<span class="type-badge">${group.quantity}个</span>` : ''}
                  </div>
                </td>
                <td class="type-cell">
                  <input 
                    type="number" 
                    class="quantity-input"
                    min="0" 
                    max="${group.maxQuantity}"
                    .value=${group.quantity}
                    @click=${(e: Event) => e.stopPropagation()}
                    @input=${(e: Event) => this.updateQuantity(groupIndex, (e.target as HTMLInputElement).valueAsNumber)}
                  />
                </td>
                <td class="type-cell" colspan="4">
                  <em style="opacity: 0.8; font-size: 12px;">
                    ${group.quantity === 0 ? '输入数量后展开配置具体物料' : `点击展开配置${group.quantity}个物料项目`}
                  </em>
                </td>
              </tr>

              <!-- 物料项目行 -->
              ${group.expanded && group.items.length > 0 ? group.items.map((item, itemIndex) => html`
                <tr class="item-row">
                  <td class="item-cell">
                    <span class="item-index">#${itemIndex + 1}</span>
                  </td>
                  <td class="item-cell">
                    <span class="status-indicator status-${this.getItemStatus(item)}"></span>
                    1个
                  </td>
                  <td class="item-cell">
                    <select 
                      class="material-selector"
                      .value=${item.specificMaterialId || ''}
                      @change=${(e: Event) => this.updateItemMaterial(groupIndex, itemIndex, (e.target as HTMLSelectElement).value)}
                    >
                      <option value="">待选择...</option>
                      ${this.getAvailableMaterialsForType(group.type).map(material => html`
                        <option value=${material.id}>${material.name}</option>
                      `)}
                    </select>
                  </td>
                  <td class="item-cell">
                    <div class="slot-multi-selector">
                      ${this.availableSlots.map(slot => html`
                        <div 
                          class="slot-option ${this.isSlotSelected(item, slot.id) ? 'selected' : ''}"
                          @click=${() => this.toggleItemSlot(groupIndex, itemIndex, slot.id)}
                        >
                          <input 
                            type="checkbox" 
                            .checked=${this.isSlotSelected(item, slot.id)}
                            @click=${(e: Event) => e.stopPropagation()}
                            @change=${() => this.toggleItemSlot(groupIndex, itemIndex, slot.id)}
                          />
                          <span class="slot-name">${slot.name}</span>
                          ${slot.position ? html`<span class="slot-position">${slot.position}</span>` : ''}
                        </div>
                      `)}
                    </div>
                    ${item.slotIds && item.slotIds.length > 0 ? html`
                      <div class="selected-slots">
                        ${item.slotIds.map(slotId => {
                          const slot = this.availableSlots.find(s => s.id === slotId)
                          return slot ? html`<span class="slot-tag">${slot.name}</span>` : ''
                        })}
                      </div>
                    ` : html`
                      <div class="no-slots-selected">未选择槽位</div>
                    `}
                  </td>
                  <td class="item-cell">
                    <input 
                      type="checkbox" 
                      .checked=${item.required}
                      @change=${() => this.toggleRequired(groupIndex, itemIndex)}
                    />
                  </td>
                  <td class="item-cell">
                    <div>${item.description}</div>
                    ${item.specificMaterialId ? html`
                      <div class="item-description">已指定具体物料</div>
                    ` : html`
                      <div class="item-description">可选择${group.name}类的任意物料</div>
                    `}
                  </td>
                </tr>
              `) : ''}
              
              ${group.expanded && group.items.length === 0 ? html`
                <tr class="item-row">
                  <td class="item-cell" colspan="6">
                    <div class="no-items">请先在上方设置数量，系统会自动生成对应的物料项目</div>
                  </td>
                </tr>
              ` : ''}
            `)}
          </tbody>
        </table>

        <div class="help-text">
          💡 <strong>使用说明：</strong>
          1. 点击物料类型展开配置项
          2. 设置数量后会自动生成对应的物料项目（item）
          3. 每个item可以选择具体物料或保持通用（待定）
          4. 指定安装槽位进行精确控制
          5. 标记必需的物料项目
        </div>

        <!-- 统计面板 -->
        <div class="summary-panel">
          <div class="summary-grid">
            <div class="summary-item">
              <div class="summary-label">总物料项目</div>
              <div class="summary-value">${this.getTotalItems()} 个</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">已指定具体物料</div>
              <div class="summary-value">${this.getSpecifiedItems()} 个</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">必需物料</div>
              <div class="summary-value">${this.getRequiredItems()} 个</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">配置完成度</div>
              <div class="summary-value">
                ${this.getTotalItems() > 0 ? Math.round((this.getSpecifiedItems() / this.getTotalItems()) * 100) : 0}%
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="actions">
          <button class="btn btn-secondary">
            🔄 重置配置
          </button>
          <button class="btn btn-export" @click=${this.exportConfiguration}>
            📤 导出配置
          </button>
          <button class="btn btn-primary" @click=${this.exportConfiguration}>
            ✅ 保存物料清单
          </button>
        </div>
      </div>
    `
  }
}

declare global {
  interface HTMLElementTagNameMap {
    'material-list-configurator': MaterialListConfigurator
  }
}