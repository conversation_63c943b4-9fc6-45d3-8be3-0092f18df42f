import type { Meta, StoryObj } from '@storybook/web-components'
import { html } from 'lit'
import './material-list-configurator'
import { MaterialType } from '../types'
import { testMaterials } from '../demo-data'

const meta: Meta = {
  title: 'Business/MaterialListConfigurator',
  component: 'material-list-configurator',
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: '基于表格形式的物料配置器，按照物料清单的思路来设计功能的物料需求。支持按类型设置数量，自动生成物料项目，指定具体物料和安装槽位。'
      }
    }
  },
  argTypes: {
    functionName: {
      control: 'text',
      description: '功能名称'
    },
    availableMaterials: {
      control: false,
      description: '可用的物料列表'
    },
    availableSlots: {
      control: false,
      description: '可用的槽位列表'
    }
  }
}

export default meta
type Story = StoryObj

// 模拟槽位数据
const mockSlots = [
  { id: 'main_controller', name: '主控制器', position: 'center' },
  { id: 'navigation_sensor', name: '主导航传感器', position: 'front' },
  { id: 'left_lidar_slot', name: '左侧激光雷达槽', position: 'left' },
  { id: 'right_lidar_slot', name: '右侧激光雷达槽', position: 'right' },
  { id: 'rear_sensor_slot', name: '后部传感器槽', position: 'rear' },
  { id: 'power_slot_1', name: '主电源槽', position: 'internal' },
  { id: 'power_slot_2', name: '备用电源槽', position: 'internal' },
  { id: 'accessory_slot_1', name: '配件槽1' },
  { id: 'accessory_slot_2', name: '配件槽2' },
  { id: 'accessory_slot_3', name: '配件槽3' }
]

export const BasicDemo: Story = {
  name: '基础物料清单配置',
  render: () => html`
    <div style="padding: 20px; background: #f8f9fa; min-height: 100vh;">
      <div style="max-width: 1200px; margin: 0 auto;">
        <div style="background: #e3f2fd; padding: 16px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #2196f3;">
          <h2 style="margin: 0 0 8px 0; color: #1976d2;">📋 基础物料清单配置演示</h2>
          <p style="margin: 0; color: #1976d2;">
            体验按照物料清单形式配置功能需求的完整流程
          </p>
        </div>
        
        <material-list-configurator 
          functionName="高精度定位系统"
          .availableMaterials=${testMaterials}
          .availableSlots=${mockSlots}
          @configuration-export=${(e: CustomEvent) => {
            console.log('物料清单配置:', e.detail)
            alert('✅ 物料清单已导出！\n\n查看控制台了解详细配置数据。')
          }}
        ></material-list-configurator>
      </div>
    </div>
  `,
  parameters: {
    docs: {
      description: {
        story: '基础的物料清单配置演示。点击物料类型设置数量，系统自动生成物料项目，可指定具体物料和安装槽位。'
      }
    }
  }
}

export const SensorArrayDemo: Story = {
  name: '传感器阵列配置演示',
  render: () => html`
    <div style="padding: 20px; background: #f8f9fa; min-height: 100vh;">
      <div style="max-width: 1200px; margin: 0 auto;">
        <div style="background: #fff3e0; padding: 16px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #ff9800;">
          <h2 style="margin: 0 0 8px 0; color: #f57c00;">🎯 传感器阵列配置演示</h2>
          <p style="margin: 0; color: #f57c00;">
            演示如何配置多传感器系统，包含主传感器、辅助传感器和备用传感器
          </p>
        </div>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 20px;">
          <div style="background: white; padding: 16px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h4 style="margin: 0 0 8px 0; color: #333;">🎯 配置目标</h4>
            <ul style="margin: 0; padding-left: 16px; font-size: 13px; color: #666;">
              <li>3个传感器项目</li>
              <li>1个主传感器(必需)</li>
              <li>2个辅助传感器(可选)</li>
            </ul>
          </div>
          <div style="background: white; padding: 16px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h4 style="margin: 0 0 8px 0; color: #333;">📍 位置分配</h4>
            <ul style="margin: 0; padding-left: 16px; font-size: 13px; color: #666;">
              <li>前方：主导航传感器</li>
              <li>左侧：辅助传感器</li>
              <li>右侧：辅助传感器</li>
            </ul>
          </div>
          <div style="background: white; padding: 16px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h4 style="margin: 0 0 8px 0; color: #333;">🔧 配置策略</h4>
            <ul style="margin: 0; padding-left: 16px; font-size: 13px; color: #666;">
              <li>主传感器指定具体型号</li>
              <li>辅助传感器保持灵活</li>
              <li>根据性能需求选择</li>
            </ul>
          </div>
        </div>
        
        <material-list-configurator 
          functionName="环境感知传感器阵列"
          .availableMaterials=${testMaterials}
          .availableSlots=${mockSlots}
          @configuration-export=${(e: CustomEvent) => {
            console.log('传感器阵列配置:', e.detail)
            const config = e.detail
            let summary = '🎯 传感器阵列配置完成！\n\n'
            config.materialRequirements.forEach((req: any) => {
              summary += `${req.materialType}类型: ${req.items.length}个\n`
              req.items.forEach((item: any, index: number) => {
                const status = item.required ? '(必需)' : '(可选)'
                const material = item.specificMaterialId ? '已指定物料' : '待定物料'
                const slot = item.slotId ? `→ ${item.slotId}` : '→ 自动分配'
                summary += `  #${index + 1}: ${material} ${slot} ${status}\n`
              })
              summary += '\n'
            })
            alert(summary + '查看控制台了解详细配置数据。')
          }}
        ></material-list-configurator>
      </div>
    </div>
  `,
  parameters: {
    docs: {
      description: {
        story: '传感器阵列配置演示。展示如何配置多传感器系统，包含主传感器（必需）和辅助传感器（可选），支持不同的安装位置和灵活配置。'
      }
    }
  }
}

export const ComplexSystemDemo: Story = {
  name: '复杂系统配置演示',
  render: () => html`
    <div style="padding: 20px; background: #f8f9fa; min-height: 100vh;">
      <div style="max-width: 1200px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 8px; margin-bottom: 20px; color: white;">
          <h2 style="margin: 0 0 12px 0;">🚀 复杂系统配置演示</h2>
          <p style="margin: 0; opacity: 0.9;">
            演示一个完整的自主机器人系统配置，包含多种物料类型和复杂的依赖关系
          </p>
        </div>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(240px, 1fr)); gap: 16px; margin-bottom: 20px;">
          <div style="background: white; padding: 16px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h4 style="margin: 0 0 8px 0; color: #333;">🧠 控制系统</h4>
            <ul style="margin: 0; padding-left: 16px; font-size: 13px; color: #666;">
              <li>主控制器 x1 (必需)</li>
              <li>备用控制器 x1 (可选)</li>
            </ul>
          </div>
          <div style="background: white; padding: 16px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h4 style="margin: 0 0 8px 0; color: #333;">👁️ 感知系统</h4>
            <ul style="margin: 0; padding-left: 16px; font-size: 13px; color: #666;">
              <li>主激光雷达 x1 (必需)</li>
              <li>辅助传感器 x2 (可选)</li>
              <li>后方传感器 x1 (可选)</li>
            </ul>
          </div>
          <div style="background: white; padding: 16px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h4 style="margin: 0 0 8px 0; color: #333;">⚡ 电源系统</h4>
            <ul style="margin: 0; padding-left: 16px; font-size: 13px; color: #666;">
              <li>主电源 x1 (必需)</li>
              <li>备用电源 x1 (可选)</li>
            </ul>
          </div>
          <div style="background: white; padding: 16px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h4 style="margin: 0 0 8px 0; color: #333;">📡 通讯系统</h4>
            <ul style="margin: 0; padding-left: 16px; font-size: 13px; color: #666;">
              <li>主通讯模块 x1 (可选)</li>
              <li>备用通讯 x1 (可选)</li>
            </ul>
          </div>
        </div>
        
        <material-list-configurator 
          functionName="完整自主导航机器人系统"
          .availableMaterials=${testMaterials}
          .availableSlots=${mockSlots}
          @configuration-export=${(e: CustomEvent) => {
            console.log('复杂系统配置:', e.detail)
            const config = e.detail
            
            let totalItems = 0
            let specifiedItems = 0
            let requiredItems = 0
            
            config.materialRequirements.forEach((req: any) => {
              totalItems += req.items.length
              specifiedItems += req.items.filter((item: any) => item.specificMaterialId).length
              requiredItems += req.items.filter((item: any) => item.required).length
            })
            
            const completeness = totalItems > 0 ? Math.round((specifiedItems / totalItems) * 100) : 0
            
            alert(`🎉 复杂系统配置完成！
            
📊 配置统计：
• 总物料项目：${totalItems} 个
• 已指定物料：${specifiedItems} 个  
• 必需物料：${requiredItems} 个
• 完成度：${completeness}%

🎯 系统架构：
• 核心功能保障：必需物料确保基本功能
• 性能提升选项：可选物料支持功能增强
• 冗余备份：关键系统有备用方案
• 灵活扩展：预留接口支持未来升级

查看控制台了解详细的配置数据结构。`)
          }}
        ></material-list-configurator>
      </div>
    </div>
  `,
  parameters: {
    docs: {
      description: {
        story: '复杂系统配置演示。展示完整的自主导航机器人系统配置，包含控制、感知、电源、通讯四个子系统，演示必需组件和可选组件的配置策略。'
      }
    }
  }
}