// 规则管理系统的扩展类型定义

import { 
  Material, 
  ConstraintRule, 
  ConfigurationSlot, 
  RobotFunction, 
  MaterialType, 
  SlotType, 
  ConstraintType,
  MaterialCategory,
  RobotModel
} from '../types'

// 表单状态类型
export interface FormState {
  isValid: boolean
  errors: Record<string, string>
  touched: Record<string, boolean>
}

// 物料表单数据
export interface MaterialFormData {
  id: string
  name: string
  categoryId: string
  type: MaterialType
  price: number
  specifications: Record<string, any>
  subMaterials: SubMaterialFormData[]
  compatibilityRules: string[]
  description: string
}

export interface SubMaterialFormData {
  materialId: string
  name: string
  required: boolean
  quantity: number
}

// 约束规则表单数据
export interface ConstraintFormData {
  id: string
  type: ConstraintType
  description: string
  params: Record<string, any>
}

// 槽位表单数据
export interface SlotFormData {
  id: string
  name: string
  type: SlotType
  allowedMaterialTypes: MaterialType[]
  allowedMaterials: string[]
  required: boolean
  maxQuantity: number
  minQuantity: number
  constraints: string[]
}

// 功能表单数据
export interface FunctionFormData {
  id: string
  name: string
  description: string
  requiredMaterialTypes: MaterialType[]
  implementations: ImplementationFormData[]
}

export interface ImplementationFormData {
  id: string
  name: string
  materials: string[]
  performance: Record<string, number>
  cost: number
  description: string
}

// 机器人型号表单数据
export interface RobotModelFormData {
  id: string
  name: string
  description: string
  basePrice: number
  materials: MaterialFormData[]
  slots: SlotFormData[]
  functions: FunctionFormData[]
  constraints: ConstraintFormData[]
}

// 表单验证规则
export interface ValidationRule {
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  custom?: (value: any) => string | null
}

export type ValidationRules<T> = {
  [K in keyof T]?: ValidationRule
}

// 表单事件类型
export interface FormEvent<T> {
  field: keyof T
  value: any
  validate?: boolean
}

// 规则管理器状态
export interface RuleManagerState {
  currentModel: RobotModelFormData | null
  materials: MaterialFormData[]
  slots: SlotFormData[]
  functions: FunctionFormData[]
  constraints: ConstraintFormData[]
  categories: MaterialCategory[]
  activeTab: string
  formState: FormState
}