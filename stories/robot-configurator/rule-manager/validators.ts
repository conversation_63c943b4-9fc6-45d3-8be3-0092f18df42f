// 表单验证器

import { ValidationRule, ValidationRules } from './types'
import { MaterialType, ConstraintType, SlotType } from '../types'

export class FormValidator {
  static validate<T>(data: T, rules: ValidationRules<T>): { isValid: boolean; errors: Record<string, string> } {
    const errors: Record<string, string> = {}
    
    for (const [field, rule] of Object.entries(rules)) {
      const value = (data as any)[field]
      const error = this.validateField(value, rule as ValidationRule, field)
      if (error) {
        errors[field] = error
      }
    }
    
    return {
      isValid: Object.keys(errors).length === 0,
      errors
    }
  }
  
  private static validateField(value: any, rule: ValidationRule, fieldName: string): string | null {
    // 必填验证
    if (rule.required && (value === undefined || value === null || value === '')) {
      return `${fieldName}是必填项`
    }
    
    // 如果值为空且不是必填，跳过其他验证
    if (!rule.required && (value === undefined || value === null || value === '')) {
      return null
    }
    
    // 最小值验证
    if (rule.min !== undefined) {
      if (typeof value === 'number' && value < rule.min) {
        return `${fieldName}不能小于${rule.min}`
      }
      if (typeof value === 'string' && value.length < rule.min) {
        return `${fieldName}长度不能少于${rule.min}个字符`
      }
    }
    
    // 最大值验证
    if (rule.max !== undefined) {
      if (typeof value === 'number' && value > rule.max) {
        return `${fieldName}不能大于${rule.max}`
      }
      if (typeof value === 'string' && value.length > rule.max) {
        return `${fieldName}长度不能超过${rule.max}个字符`
      }
    }
    
    // 正则表达式验证
    if (rule.pattern && typeof value === 'string' && !rule.pattern.test(value)) {
      return `${fieldName}格式不正确`
    }
    
    // 自定义验证
    if (rule.custom) {
      return rule.custom(value)
    }
    
    return null
  }
}

// 预定义验证规则
export const ValidationRules = {
  // 物料验证规则
  material: {
    id: { 
      required: true, 
      pattern: /^[a-zA-Z0-9_-]+$/, 
      min: 1, 
      max: 50 
    },
    name: { 
      required: true, 
      min: 1, 
      max: 100 
    },
    price: { 
      required: true, 
      min: 0,
      custom: (value: number) => {
        if (!Number.isFinite(value)) return '价格必须是有效数字'
        return null
      }
    },
    type: {
      required: true,
      custom: (value: string) => {
        if (!Object.values(MaterialType).includes(value as MaterialType)) {
          return '请选择有效的物料类型'
        }
        return null
      }
    }
  },
  
  // 约束规则验证
  constraint: {
    id: { 
      required: true, 
      pattern: /^[a-zA-Z0-9_-]+$/, 
      min: 1, 
      max: 50 
    },
    type: {
      required: true,
      custom: (value: string) => {
        if (!Object.values(ConstraintType).includes(value as ConstraintType)) {
          return '请选择有效的约束类型'
        }
        return null
      }
    },
    description: { 
      required: true, 
      min: 1, 
      max: 200 
    }
  },
  
  // 槽位验证规则
  slot: {
    id: { 
      required: true, 
      pattern: /^[a-zA-Z0-9_-]+$/, 
      min: 1, 
      max: 50 
    },
    name: { 
      required: true, 
      min: 1, 
      max: 100 
    },
    type: {
      required: true,
      custom: (value: string) => {
        if (!Object.values(SlotType).includes(value as SlotType)) {
          return '请选择有效的槽位类型'
        }
        return null
      }
    },
    maxQuantity: { 
      required: true, 
      min: 1, 
      max: 100 
    },
    minQuantity: { 
      required: true, 
      min: 0, 
      max: 100 
    }
  },
  
  // 功能验证规则
  function: {
    id: { 
      required: true, 
      pattern: /^[a-zA-Z0-9_-]+$/, 
      min: 1, 
      max: 50 
    },
    name: { 
      required: true, 
      min: 1, 
      max: 100 
    },
    description: { 
      required: true, 
      min: 1, 
      max: 200 
    }
  },
  
  // 机器人型号验证规则
  model: {
    id: { 
      required: true, 
      pattern: /^[a-zA-Z0-9_-]+$/, 
      min: 1, 
      max: 50 
    },
    name: { 
      required: true, 
      min: 1, 
      max: 100 
    },
    description: { 
      required: true, 
      min: 1, 
      max: 500 
    },
    basePrice: { 
      required: true, 
      min: 0,
      custom: (value: number) => {
        if (!Number.isFinite(value)) return '基础价格必须是有效数字'
        return null
      }
    }
  }
}

// 特殊验证函数
export const SpecialValidators = {
  // 验证ID唯一性
  uniqueId: (existingIds: string[]) => (value: string) => {
    if (existingIds.includes(value)) {
      return 'ID已存在，请使用其他ID'
    }
    return null
  },
  
  // 验证槽位数量约束
  slotQuantity: (minQuantity: number) => (maxQuantity: number) => {
    if (maxQuantity < minQuantity) {
      return '最大数量不能小于最小数量'
    }
    return null
  },
  
  // 验证物料依赖
  materialDependency: (availableMaterials: string[]) => (materialIds: string[]) => {
    const invalidIds = materialIds.filter(id => !availableMaterials.includes(id))
    if (invalidIds.length > 0) {
      return `以下物料ID不存在: ${invalidIds.join(', ')}`
    }
    return null
  },
  
  // 验证JSON格式
  jsonFormat: (value: string) => {
    try {
      JSON.parse(value)
      return null
    } catch {
      return 'JSON格式不正确'
    }
  }
}