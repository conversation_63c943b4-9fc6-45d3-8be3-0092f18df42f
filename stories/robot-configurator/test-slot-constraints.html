<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>槽位约束测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e1e5e9;
            border-radius: 6px;
        }
        .test-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        code {
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Monaco', 'Consolas', monospace;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>槽位约束测试验证</h1>
        <p>验证64GB SD卡不再占用主控制器槽位的数量约束</p>

        <div class="test-section">
            <div class="test-title">测试1: SD卡物料类型</div>
            <p>64GB SD卡的物料类型应该是 <code>MECHANICAL</code>，而不是 <code>CONTROLLER</code></p>
            <div id="test1-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">测试2: 主控制器槽位约束</div>
            <p>主控制器槽位应该只允许 <code>CONTROLLER</code> 类型的物料，最大数量为1</p>
            <div id="test2-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">测试3: 配件槽位约束</div>
            <p>配件和线束槽位应该允许 <code>MECHANICAL</code> 类型的物料（包括SD卡）</p>
            <div id="test3-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">测试4: 依赖约束</div>
            <p>选择Raspberry Pi控制器时，应该提示需要SD卡，但SD卡应该添加到配件槽位</p>
            <div id="test4-result" class="test-result"></div>
        </div>
    </div>

    <script type="module">
        // 导入测试数据
        import { demoRobotModel } from './demo-data.js';
        import { ConstraintSolver } from './constraint-solver.js';

        // 运行测试
        function runTests() {
            const model = demoRobotModel;
            const solver = new ConstraintSolver(model);

            // 测试1: SD卡物料类型
            const sdCard = model.materials.find(m => m.id === 'sd_card_64gb');
            const test1Result = document.getElementById('test1-result');
            if (sdCard && sdCard.type === 'mechanical') {
                test1Result.className = 'test-result success';
                test1Result.textContent = `✅ 通过: SD卡类型为 ${sdCard.type}`;
            } else {
                test1Result.className = 'test-result error';
                test1Result.textContent = `❌ 失败: SD卡类型为 ${sdCard?.type || '未找到'}，期望为 mechanical`;
            }

            // 测试2: 主控制器槽位约束
            const mainControllerSlot = model.slots.find(s => s.id === 'main_controller');
            const test2Result = document.getElementById('test2-result');
            if (mainControllerSlot && 
                mainControllerSlot.allowedMaterialTypes.includes('controller') &&
                mainControllerSlot.maxQuantity === 1 &&
                !mainControllerSlot.allowedMaterialTypes.includes('mechanical')) {
                test2Result.className = 'test-result success';
                test2Result.textContent = '✅ 通过: 主控制器槽位约束正确';
            } else {
                test2Result.className = 'test-result error';
                test2Result.textContent = '❌ 失败: 主控制器槽位约束不正确';
            }

            // 测试3: 配件槽位约束
            const accessoriesSlot = model.slots.find(s => s.id === 'accessories');
            const test3Result = document.getElementById('test3-result');
            if (accessoriesSlot && accessoriesSlot.allowedMaterialTypes.includes('mechanical')) {
                test3Result.className = 'test-result success';
                test3Result.textContent = '✅ 通过: 配件槽位支持mechanical类型物料';
            } else {
                test3Result.className = 'test-result error';
                test3Result.textContent = '❌ 失败: 配件槽位不支持mechanical类型物料';
            }

            // 测试4: 依赖约束
            const testConfig = {
                id: 'test',
                name: 'test',
                robotModelId: model.id,
                selections: {
                    'main_controller': [
                        { materialId: 'controller_raspberry_pi4', quantity: 1, slotId: 'main_controller' }
                    ]
                },
                functions: []
            };

            const validationResult = solver.validate(testConfig);
            const test4Result = document.getElementById('test4-result');
            
            const hasSDCardSuggestion = validationResult.suggestions.some(s => 
                s.materialId === 'sd_card_64gb' && s.slotId === 'accessories'
            );

            if (hasSDCardSuggestion) {
                test4Result.className = 'test-result success';
                test4Result.textContent = '✅ 通过: SD卡依赖约束正确，建议添加到配件槽位';
            } else {
                test4Result.className = 'test-result error';
                test4Result.textContent = '❌ 失败: SD卡依赖约束不正确';
            }
        }

        // 页面加载后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>