// 机器人配置系统核心类型定义

export interface Material {
  id: string
  name: string
  category: MaterialCategory
  type: MaterialType
  specifications: Record<string, any>
  price: number
  subMaterials: SubMaterial[]
  compatibilityRules: string[]
  description?: string
}

export interface SubMaterial {
  materialId: string
  name: string
  required: boolean
  quantity: number
}

export interface MaterialCategory {
  id: string
  name: string
  description: string
}

export enum MaterialType {
  SENSOR = 'sensor',
  ACTUATOR = 'actuator',
  CONTROLLER = 'controller',
  POWER = 'power',
  MECHANICAL = 'mechanical',
  COMMUNICATION = 'communication'
}

export interface RobotFunction {
  id: string
  name: string
  description: string
  // 废弃旧的简单约束，改用新的复杂约束系统
  // requiredMaterialTypes: MaterialType[]
  implementations: ImplementationOption[]
  // 新的物料要求系统（可选，用于向后兼容）
  materialRequirements?: MaterialRequirement[]
}

export interface MaterialRequirement {
  id: string  // 要求的唯一标识
  name: string  // 要求名称，如"主传感器"、"左侧定位传感器"
  description: string  // 详细描述
  constraintType: MaterialConstraintType  // 约束类型
  quantity: number  // 需要的数量
  placements: PlacementRule[]  // 放置规则
  
  // 根据约束类型使用不同字段
  allowedMaterialTypes?: MaterialType[]  // 类型1：只有类别
  specificMaterialIds?: string[]  // 类型2：具体到款
  blacklistMaterialIds?: string[]  // 类型3：类别+款黑名单（与allowedMaterialTypes配合使用）
}

export enum MaterialConstraintType {
  CATEGORY_ONLY = 'category_only',           // 只有类别限制
  SPECIFIC_MATERIALS = 'specific_materials', // 具体到特定物料
  CATEGORY_WITH_BLACKLIST = 'category_with_blacklist', // 类别+黑名单
  FLEXIBLE = 'flexible'                      // 灵活约束（类别+数量，无特定物料要求）
}

export interface PlacementRule {
  slotId: string    // 目标槽位ID
  priority: number  // 优先级（1-10，数字越大优先级越高）
  required: boolean // 是否必须放置在此槽位
  description: string // 放置描述，如"必须安装在左侧"、"推荐安装在前方"
  conditions?: PlacementCondition[] // 放置条件
}

export interface PlacementCondition {
  type: 'depends_on' | 'conflicts_with' | 'requires_empty'
  targetSlotId?: string     // 依赖/冲突的槽位
  targetMaterialId?: string // 依赖/冲突的物料
  description: string
}

export interface ImplementationOption {
  id: string
  name: string
  materials: string[] // material IDs
  performance: Record<string, number>
  cost: number
  description: string
}

export interface ConfigurationSlot {
  id: string
  name: string
  type: SlotType
  allowedMaterialTypes: MaterialType[]
  allowedMaterials: string[]
  required: boolean
  // 移除数量限制，因为每个槽只能安装一个物料
  // maxQuantity: number
  // minQuantity: number
  constraints: string[]
  // 新增：槽位位置信息（用于功能约束中的位置要求）
  position?: string  // 如 'left', 'right', 'front', 'back'
}

export enum SlotType {
  MATERIAL_TYPE = 'material_type',
  FUNCTION = 'function',
  FIXED = 'fixed'
}

export interface Configuration {
  id: string
  name: string
  robotModelId: string
  selections: Record<string, Selection[]> // slotId -> selections
  functions: string[] // selected function IDs
}

export interface Selection {
  materialId: string
  slotId: string
  // quantity 移除，因为每个槽只能安装一个物料
}

export interface ConstraintRule {
  id: string
  type: ConstraintType
  description: string
  params: Record<string, any>
}

export enum ConstraintType {
  MATERIAL_DEPENDENCY = 'material_dependency',
  MATERIAL_TYPE = 'material_type',
  FUNCTIONAL = 'functional',
  MATERIAL_QUANTITY = 'material_quantity',  // 物料级别的数量约束
  MUTUAL_EXCLUSIVE = 'mutual_exclusive',
  SPECIFIC_PLACEMENT = 'specific_placement'  // 特定放置约束（功能要求特定槽位）
}

export interface ValidationResult {
  isValid: boolean
  violations: ConstraintViolation[]
  warnings: ConstraintWarning[]
  suggestions: Suggestion[]
}

export interface ConstraintViolation {
  constraintId: string
  message: string
  severity: 'error' | 'warning'
  affectedSlots: string[]
  autoFixable: boolean
}

export interface ConstraintWarning {
  message: string
  type: 'performance' | 'cost' | 'compatibility'
}

export interface Suggestion {
  type: 'add_material' | 'remove_material' | 'replace_material' | 'adjust_quantity'
  materialId?: string
  slotId?: string
  reason: string
  priority: number
}

export interface RobotModel {
  id: string
  name: string
  description: string
  basePrice: number
  slots: ConfigurationSlot[]
  availableFunctions: RobotFunction[]
  materials: Material[]
  constraintRules: ConstraintRule[]
}