import { GLTFLoader }       from 'three/examples/jsm/loaders/GLTFLoader'
import { DRACOLoader }      from 'three/examples/jsm/loaders/DRACOLoader'
import { Object3D }         from 'three'
import { arrayBufferToUrl } from '../utils'
import { DRACOExporter }    from 'three/examples/jsm/exporters/DRACOExporter'
import { GLTFExporter }     from 'three/examples/jsm/exporters/GLTFExporter'

export const gltfExporter = new GLTFExporter()

export const gltfLoader = new GLTFLoader()
const dracoLoader = new DRACOLoader()
// @ts-ignore
if (window.dracoPath) {
  // @ts-ignore
  dracoLoader.setDecoderPath(window.dracoPath)
} else {
  dracoLoader.setDecoderPath('./draco/')
}
dracoLoader.setDecoderConfig({ type: 'wasm' })
gltfLoader.setDRACOLoader(dracoLoader)

export const dracoExporter = new DRACOExporter()

interface SGLBContent {
  version: number;
  jsonDescription: any; // 根据实际JSON结构调整类型
  glbContent?: ArrayBuffer;
  glb?: Object3D
}

const sglbIdentifierArray = [115, 103, 108, 98]

export function createSGLBFile(glbContent: ArrayBuffer, jsonDescription: any, version: number): ArrayBuffer {
  // 将 JSON 对象转换为字符串
  const jsonString = JSON.stringify(jsonDescription)
  // 获取 JSON 字符串的UTF-8编码字节数组
  const jsonStringBytes = new TextEncoder().encode(jsonString)

  // 假定 sglbIdentifierArray 已经定义
  const sglbIdentifierArray = new Uint8Array([115, 103, 108, 98]) // ASCII for 'sglb'

  // 创建版本号（2个字节）
  const versionBuffer = new Uint8Array(2)
  versionBuffer[0] = version >> 8 // 高位字节
  versionBuffer[1] = version & 0xFF // 低位字节

  // 创建 JSON 长度（4个字节）
  const jsonLengthBuffer = new Uint8Array(4)
  jsonLengthBuffer[0] = jsonStringBytes.length >> 24
  jsonLengthBuffer[1] = (jsonStringBytes.length >> 16) & 0xFF
  jsonLengthBuffer[2] = (jsonStringBytes.length >> 8) & 0xFF
  jsonLengthBuffer[3] = jsonStringBytes.length & 0xFF

  // 合并所有部分
  const glbContentUint8Array = new Uint8Array(glbContent)
  const totalLength = sglbIdentifierArray.length + versionBuffer.length + jsonLengthBuffer.length + jsonStringBytes.length + glbContentUint8Array.length
  const finalArray = new Uint8Array(totalLength)

  let offset = 0
  finalArray.set(sglbIdentifierArray, offset)
  offset += sglbIdentifierArray.length
  finalArray.set(versionBuffer, offset)
  offset += versionBuffer.length
  finalArray.set(jsonLengthBuffer, offset)
  offset += jsonLengthBuffer.length
  finalArray.set(jsonStringBytes, offset)
  offset += jsonStringBytes.length
  finalArray.set(glbContentUint8Array, offset)

  return finalArray.buffer
}

function parseSGLB(arrayBuffer: ArrayBuffer): SGLBContent {
  // 检查是否匹配 'sglb'
  const identifierBytes = new Uint8Array(arrayBuffer.slice(0, 4))
  let isMatch = true
  for (let i = 0; i < sglbIdentifierArray.length; i++) {
    if (identifierBytes[i] !== sglbIdentifierArray[i]) {
      isMatch = false
      break
    }
  }
  if (!isMatch) {
    throw new Error('Invalid file format')
  }
  // 解析版本号
  const versionBytes = new DataView(arrayBuffer, 4, 2)
  const version = versionBytes.getUint16(0, false) // 使用大端序

  // 解析 JSON 长度
  const jsonLengthBytes = new DataView(arrayBuffer, 6, 4)
  const jsonLength = jsonLengthBytes.getUint32(0, false) // 使用大端序
  //
  // // 解析 JSON 字符串
  const jsonStringBytes = new Uint8Array(arrayBuffer.slice(10, 10 + jsonLength))
  const jsonString = new TextDecoder().decode(jsonStringBytes)
  const jsonDescription = JSON.parse(jsonString)

  // 获取 GLB 内容
  const glbContent = arrayBuffer.slice(10 + jsonLength)

  return { version, jsonDescription, glbContent }
}

export interface SGLBLoaderProps {
  useCache: boolean
}

export class SGLBLoader {
  private cache = new Map<string, SGLBContent>()

  constructor(private props: SGLBLoaderProps) {
  }

  load(url: string, onLoad: (result: SGLBContent) => void, onProgress?: (event: ProgressEvent) => void, onError?: (event: ErrorEvent) => void): void {

    if (this.props.useCache) {
      if (this.cache.has(url)) {
        onLoad(this.cache.get(url)!)
        return
      }
    }

    const scope = this

    // 使用 fetch API 异步加载 sglb 文件
    fetch(url).then(response => {
      if (!response.ok) {
        onError?.(new ErrorEvent('NetworkError', { message: 'NetworkError for ' + url + ': ' + response.statusText }))
        return
      }

      return response.arrayBuffer()
    }).then(async buffer => {
      if (!buffer) {
        onError?.(new ErrorEvent('Error', { message: 'Not a valid sglb file.' }))
      }
      // 成功加载后，解析 sglb 文件
      try {
        const result = scope.parse(buffer as ArrayBuffer)
        if (!result.glbContent) {
          onError?.(new ErrorEvent('Error', { message: 'No glb content.' }))
          return
        }
        const glbUrl = arrayBufferToUrl(result.glbContent, 'model/gltf-binary')
        const glb = await gltfLoader.loadAsync(glbUrl)
        URL.revokeObjectURL(glbUrl)
        result.glbContent = undefined
        result.glb = glb.scene
        buffer = undefined
        this.cache.set(url, result)
        onLoad(result)
      } catch (e) {
        onError?.(new ErrorEvent('Error', { message: (e as Error)?.message }))
      }
    }).catch(e => {
      onError?.(new ErrorEvent('Error', { message: e.message }))
    })
  }

  async loadAsync(url: string): Promise<SGLBContent> {
    if (this.props.useCache && this.cache.has(url)) {
      return this.cache.get(url)!
    }

    try {
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`NetworkError for ${url}: ${response.statusText}`)
      }

      const buffer = await response.arrayBuffer()
      if (!buffer) {
        throw new Error('Not a valid sglb file.')
      }

      const result = this.parse(buffer)
      if (!result.glbContent) {
        throw new Error('No GLB content.')
      }

      const glbUrl = arrayBufferToUrl(result.glbContent, 'model/gltf-binary')
      const glb = await gltfLoader.loadAsync(glbUrl)
      URL.revokeObjectURL(glbUrl)

      // 清除 ArrayBuffer 引用，添加场景到结果
      result.glbContent = undefined
      result.glb = glb.scene

      this.cache.set(url, result)
      return result
    } catch (error) {
      throw error // 这里抛出错误，允许调用者处理
    }
  }

  parse(arrayBuffer: ArrayBuffer): SGLBContent {
    return parseSGLB(arrayBuffer)
  }

  clear() {
    const objs = [...this.cache.values()]
    for (let i = 0; i < objs.length; i++) {
      // clearThreeObjectMesh(objs[i].glbContent)
    }
  }
}

