import { css, html, LitElement, PropertyValues } from 'lit'
import { customElement, property, query }        from 'lit/decorators.js'

export interface Props {
  e3dx: string
  files: string[]
}

const url = 'http://192.168.0.31:3000'
@customElement('step-viewer')
export default class StepViewer extends LitElement {

  @query('.container')
  container: HTMLDivElement | undefined

  constructor() {
    super()
  }

  @property()
  files: string[] = []

  protected firstUpdated(_changedProperties: PropertyValues) {
    super.firstUpdated(_changedProperties)
    if (!this.container) return
  }

  protected updated(_changedProperties: PropertyValues) {
    super.updated(_changedProperties)

    if (this.files.length > 0) {
      const file = this.files[0]
      this._upload(file)
        .then()
    }
  }

  async blobURLToFile(blobURL: string, fileName?: string) {
    try {
      const response = await fetch(blobURL)
      const blob = await response.blob()

      if (!fileName) {
        fileName = blobURL.slice(blobURL.lastIndexOf('/')) + '.stp'
      }
      return new File([blob], fileName, { type: blob.type })
    } catch (error) {
      console.error('Error:', error)
      return null
    }
  }

  private _upload = async (filePath: string) => {
    const formData = new FormData()
    const file = await this.blobURLToFile(filePath)
    URL.revokeObjectURL(filePath)
    console.log(file)
    if (!file) return
    formData.append('file', file)
    const response = await fetch(`${url}/api/v1/resource/upload`, {
      method: 'POST',
      body: formData
    })
    const { id } = await response.json()
    if (id) {
      const r = await this._startTask(id)
      console.log(r)
    }
  }

  private async _startTask(id: string) {
    const response = await fetch(`${url}/api/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        metadata: {
          resID: id,
          targetExt: 'fbx'
        }
      })
    })
    if (response.ok) {
      return await response.json()
    }
  }

  protected render(): unknown {
    return html`
      ${this.files?.map(r => {
        return html`
          <div>
            <img src="${r}" alt=""/>
          </div>`
      })}
      <div class="container"></div>`
  }

  static styles = css`
    :host {
      display: block;
      width: 100%;
      height: 100%;
    }

    img {
      width: 40px;
    }
  `
}