import { Texture } from 'pixi.js'

export function downloadBlob(blob: Blob, filename: string): void {
  // 创建一个指向该 Blob 的 URL
  const url = URL.createObjectURL(blob)

  // 创建一个临时的<a>元素
  const a = document.createElement('a')
  a.href = url
  a.download = filename // 设置下载文件的名称

  // 将<a>元素添加到文档中去，以便能够触发点击事件
  document.body.appendChild(a)
  a.click() // 触发下载

  // 清理：移除<a>元素，释放Blob URL
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

export function calculateArrayAverage(array: number[]) {
  // 检查数组是否为空
  if (array.length === 0) {
    return 0
  }

  // 使用reduce方法累加数组中的所有值
  const sum = array.reduce((accumulator, currentValue) => {
    return accumulator + currentValue
  }, 0)

  // 将总和除以数组长度得到平均值
  return sum / array.length
}

export function arrayBufferToUrl(arrayBuffer: ArrayBuffer, mimeType: string): string {
  const blob = new Blob([arrayBuffer], { type: mimeType })
  return URL.createObjectURL(blob)
}

export function isAppleDevice() {
  return /Mac|iPod|iPhone|iPad/.test(navigator.platform) ||
    /Mac OS|iOS/.test(navigator.userAgent)
}

export function imageBitmapToPixiTexture(imageBitmap: ImageBitmap): Texture | undefined {
  // 创建 canvas
  let canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  canvas.width = imageBitmap.width
  canvas.height = imageBitmap.height

  if (!ctx) return
  ctx.drawImage(imageBitmap, 0, 0)
  const texture = Texture.from(canvas)
  // @ts-ignore
  canvas = null
  return texture
}

export function imageBitmapToUrl(imageBitmap: ImageBitmap): string | undefined {
  // 创建 canvas
  let canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  canvas.width = imageBitmap.width
  canvas.height = imageBitmap.height

  if (!ctx) return
  ctx.drawImage(imageBitmap, 0, 0)
  const base64 = canvas.toDataURL('image/png', 0.75)
  return base64
}

export function calculateFocalLength(fov: number, sensorSize = 43) {
  const fovInRadians = (fov * Math.PI) / 180 // 转换度到弧度
  return sensorSize / (2 * Math.tan(fovInRadians / 2))
}
