<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Lib Test</title>
  <style>
    body, html {
      margin: 0;
      padding: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    * {
      box-sizing: border-box;
    }

    #test {
      overflow: hidden;
      width: 100%;
      height: 100%;
    }
  </style>
</head>
<body>
<div id="test">

</div>
<script type="module">
  import * as T from '/dist/device-viewer.mjs'

  const {Scene3D} = T
  const scene = new Scene3D({
    useSound: true
  })
  const wrapper = document.getElementById('test')
  scene.appendTo(wrapper)
  scene.addAMR('/test/test.sglb').then(scene.fit)

  const cropper = new T.Cropper()
  wrapper.append(cropper)

  cropper.addEventListener('image-change', (r) => {
    console.log('image-change', r)
  })
  // cropper.style.position = 'absolute'
  cropper.add('/test/logo.png')
</script>
</body>
</html>
