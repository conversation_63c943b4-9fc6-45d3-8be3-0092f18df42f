import * as T from 'shop-components'

const {Scene3D} = T
const scene = new Scene3D({
  useSound: true
})
const wrapper = document.getElementById('test')
scene.appendTo(wrapper)
scene.addAMR('/test/test.sglb').then(scene.fit)

const cropper = new T.Cropper()
wrapper.append(cropper)

cropper.addEventListener('image-change', (r) => {
  console.log('image-change', r)
})
// cropper.style.position = 'absolute'
cropper.add('/test/logo.png')
