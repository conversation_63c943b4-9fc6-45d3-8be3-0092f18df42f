{"compilerOptions": {"target": "ESNext", "module": "ESNext", "lib": ["ES2020", "DOM", "DOM.Iterable"], "outDir": "./dist", "declaration": true, "moduleResolution": "Node", "strict": true, "jsx": "preserve", "sourceMap": true, "useDefineForClassFields": false, "experimentalDecorators": true, "strictNullChecks": true, "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "removeComments": true, "skipLibCheck": true, "baseUrl": ".", "paths": {"@/*": ["stories/*"]}}, "files": ["stories/main.ts", "package.json"], "include": ["stories/main.ts", "stories/**/*.ts", "stories/**/*.d.ts", "node_modules/countup.js/dist/countUp.d.ts", "@types/three", "stories/**/*.tsx", "stories/**/*.vue", "*.d.ts"], "exclude": ["stories/model-viewer/**/*"], "references": [{"path": "./tsconfig.node.json"}]}