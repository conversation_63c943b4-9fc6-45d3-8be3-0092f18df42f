// vite.config.js
import { resolve } from 'path'
import { defineConfig } from 'vite'
import dts from 'vite-plugin-dts'

export default defineConfig({
  base: './',
  plugins: [
    dts()
  ],
  resolve: {
    alias: {
      '@/device-viewer': '/stories/device-viewer',
      '@/devices': '/stories/devices'
    }
  },
  optimizeDeps: {
    exclude: [
      'three', // to prevent Vite's code chunking which causes an error
    ],
    esbuildOptions: {
      target: 'esnext', // Three.js uses top-level await
    },
  },
  build: {
    lib: {
      // Could also be a dictionary or array of multiple entry points
      entry: [
        resolve(__dirname, 'stories/main.ts'),
      ],
      name: 'ShopComponents',
      // the proper extensions will be added
      fileName: 'shop-components',
    },
    rollupOptions: {
      // 确保外部化处理那些你不想打包进库的依赖
      external: ['vue'],
      output: {
        // 在 UMD 构建模式下为这些外部化的依赖提供一个全局变量
        globals: {
          vue: 'Vue',
        },
      },
    },
  },
})
